<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Matières Premières</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Debug Matières Premières</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Actions</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary mb-2" onclick="checkData()">Vérifier Données</button>
                        <button class="btn btn-success mb-2" onclick="createMoreMaterials()">Créer Plus de Matières</button>
                        <button class="btn btn-info mb-2" onclick="testDropdown()">Tester Dropdown</button>
                        <button class="btn btn-warning mb-2" onclick="clearAll()">Tout Vider</button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Résultats</h5>
                    </div>
                    <div class="card-body">
                        <div id="results">
                            <p class="text-muted">Cliquez sur "Vérifier Données"</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Dropdown</h5>
                    </div>
                    <div class="card-body">
                        <select class="form-select" id="testSelect">
                            <option value="">Sélectionner</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Liste Complète</h5>
                    </div>
                    <div class="card-body">
                        <div id="fullList">
                            <p class="text-muted">Aucune donnée</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/core/storage.js"></script>
    <script src="assets/js/core/utils.js"></script>

    <script>
        window.addEventListener('load', () => {
            storage.init();
            checkData();
        });

        function checkData() {
            const rawMaterials = storage.get('rawMaterials') || [];
            const inventory = storage.get('inventory') || [];
            
            let html = `
                <h6>Statistiques :</h6>
                <p><strong>Matières premières totales:</strong> ${rawMaterials.length}</p>
                <p><strong>Articles en inventaire:</strong> ${inventory.length}</p>
            `;
            
            if (rawMaterials.length > 0) {
                html += '<h6>Premières 10 matières premières :</h6><ol>';
                rawMaterials.slice(0, 10).forEach(material => {
                    const invItem = inventory.find(i => i.materialId === material.id);
                    const price = invItem ? (invItem.unitPrice || invItem.averagePrice || 0) : 0;
                    html += `<li><strong>${material.name}</strong> (${material.unit}) - ID: ${material.id} - Prix: ${Utils.formatPrice(price)}</li>`;
                });
                html += '</ol>';
                
                if (rawMaterials.length > 10) {
                    html += `<p class="text-muted">... et ${rawMaterials.length - 10} autres</p>`;
                }
            }
            
            document.getElementById('results').innerHTML = html;
            updateFullList();
            updateTestDropdown();
        }

        function createMoreMaterials() {
            const additionalMaterials = [
                { name: 'Tomates', unit: 'kilogramme', categoryId: 1 },
                { name: 'Oignons', unit: 'kilogramme', categoryId: 1 },
                { name: 'Carottes', unit: 'kilogramme', categoryId: 1 },
                { name: 'Pommes de terre', unit: 'kilogramme', categoryId: 1 },
                { name: 'Courgettes', unit: 'kilogramme', categoryId: 1 },
                { name: 'Poivrons', unit: 'kilogramme', categoryId: 1 },
                { name: 'Concombres', unit: 'kilogramme', categoryId: 1 },
                { name: 'Salade', unit: 'pièce', categoryId: 1 },
                { name: 'Persil', unit: 'botte', categoryId: 1 },
                { name: 'Coriandre', unit: 'botte', categoryId: 1 },
                { name: 'Menthe', unit: 'botte', categoryId: 1 },
                { name: 'Basilic', unit: 'botte', categoryId: 1 },
                { name: 'Poulet entier', unit: 'kilogramme', categoryId: 2 },
                { name: 'Escalope de poulet', unit: 'kilogramme', categoryId: 2 },
                { name: 'Cuisses de poulet', unit: 'kilogramme', categoryId: 2 },
                { name: 'Bœuf haché', unit: 'kilogramme', categoryId: 2 },
                { name: 'Agneau', unit: 'kilogramme', categoryId: 2 },
                { name: 'Saumon', unit: 'kilogramme', categoryId: 3 },
                { name: 'Dorade', unit: 'kilogramme', categoryId: 3 },
                { name: 'Crevettes', unit: 'kilogramme', categoryId: 3 }
            ];

            const prices = [
                12.50, 8.00, 6.50, 5.00, 9.00, 15.00, 7.50, 3.00, 2.50, 3.00,
                4.00, 5.00, 45.00, 55.00, 35.00, 65.00, 85.00, 120.00, 80.00, 150.00
            ];

            let created = 0;
            additionalMaterials.forEach((material, index) => {
                const existing = storage.get('rawMaterials').find(m => m.name === material.name);
                if (!existing) {
                    const newMaterial = storage.add('rawMaterials', {
                        ...material,
                        description: 'Article créé automatiquement',
                        createdAt: new Date().toISOString()
                    });

                    // Créer l'entrée d'inventaire correspondante
                    storage.add('inventory', {
                        materialId: newMaterial.id,
                        quantity: Math.floor(Math.random() * 50) + 10,
                        averagePrice: prices[index] || 10.00,
                        unitPrice: prices[index] || 10.00,
                        totalValue: (Math.floor(Math.random() * 50) + 10) * (prices[index] || 10.00),
                        lastUpdated: new Date().toISOString()
                    });

                    created++;
                }
            });

            updateResults(`✅ ${created} nouvelles matières premières créées`);
            checkData();
        }

        function testDropdown() {
            const rawMaterials = storage.get('rawMaterials') || [];
            const inventory = storage.get('inventory') || [];
            
            updateResults(`🔍 Test du dropdown avec ${rawMaterials.length} matières premières...`);
            updateTestDropdown();
        }

        function updateTestDropdown() {
            const select = document.getElementById('testSelect');
            const rawMaterials = storage.get('rawMaterials') || [];
            const inventory = storage.get('inventory') || [];
            
            select.innerHTML = '<option value="">Sélectionner</option>';
            
            rawMaterials.forEach(material => {
                const inventoryItem = inventory.find(item => item.materialId === material.id);
                const unitPrice = inventoryItem ? (inventoryItem.unitPrice || inventoryItem.averagePrice || 0) : 0;
                const priceInfo = unitPrice > 0 ? ` - ${Utils.formatPrice(unitPrice)}` : '';
                
                const option = document.createElement('option');
                option.value = material.id;
                option.textContent = `${material.name} (${material.unit})${priceInfo}`;
                select.appendChild(option);
            });
        }

        function updateFullList() {
            const rawMaterials = storage.get('rawMaterials') || [];
            const inventory = storage.get('inventory') || [];
            
            if (rawMaterials.length === 0) {
                document.getElementById('fullList').innerHTML = '<p class="text-muted">Aucune matière première</p>';
                return;
            }
            
            let html = '<div class="table-responsive"><table class="table table-sm"><thead><tr><th>Nom</th><th>Unité</th><th>ID</th><th>Prix</th></tr></thead><tbody>';
            
            rawMaterials.forEach(material => {
                const invItem = inventory.find(i => i.materialId === material.id);
                const price = invItem ? (invItem.unitPrice || invItem.averagePrice || 0) : 0;
                html += `<tr>
                    <td>${material.name}</td>
                    <td>${material.unit}</td>
                    <td>${material.id}</td>
                    <td>${Utils.formatPrice(price)}</td>
                </tr>`;
            });
            
            html += '</tbody></table></div>';
            document.getElementById('fullList').innerHTML = html;
        }

        function clearAll() {
            storage.set('rawMaterials', []);
            storage.set('inventory', []);
            updateResults('🗑️ Toutes les données supprimées');
            checkData();
        }

        function updateResults(message) {
            document.getElementById('results').innerHTML = `
                <div class="alert alert-info">
                    ${message}
                </div>
            `;
        }
    </script>
</body>
</html>
