/**
 * RestoManager - Module Gestion des Fournisseurs
 * CRUD complet pour la gestion des fournisseurs avec historique
 */

class SuppliersModule {
    constructor() {
        this.suppliers = [];
        this.currentSupplier = null;
        this.searchTerm = '';
        this.sortBy = 'name';
        this.sortOrder = 'asc';
    }

    /**
     * Initialise le module
     */
    async init() {
        this.loadSuppliers();
    }

    /**
     * Charge les fournisseurs depuis le stockage
     */
    loadSuppliers() {
        const allSuppliers = storage.get('suppliers') || [];
        this.suppliers = this.deduplicateSuppliers(allSuppliers);
    }

    /**
     * Rend le contenu du module
     */
    async render() {
        return `
            <div class="suppliers-container">
                <!-- En-tête -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card-neomorphic">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <h2 class="mb-0">
                                            <i class="fas fa-truck text-primary me-2"></i>
                                            Gestion des Fournisseurs
                                        </h2>
                                        <p class="text-muted mb-0">
                                            ${this.suppliers.length} fournisseur(s) enregistré(s)
                                        </p>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <button class="btn btn-neomorphic btn-primary" onclick="suppliersModule.showAddModal()">
                                            <i class="fas fa-plus me-2"></i>
                                            Nouveau Fournisseur
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filtres et recherche -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" class="form-control" placeholder="Rechercher un fournisseur..." 
                                   id="searchSuppliers" value="${this.searchTerm}">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="sortSuppliers">
                            <option value="name" ${this.sortBy === 'name' ? 'selected' : ''}>Trier par nom</option>
                            <option value="type" ${this.sortBy === 'type' ? 'selected' : ''}>Trier par type</option>
                            <option value="status" ${this.sortBy === 'status' ? 'selected' : ''}>Trier par statut</option>
                            <option value="createdAt" ${this.sortBy === 'createdAt' ? 'selected' : ''}>Trier par date</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-neomorphic btn-info w-100" onclick="suppliersModule.exportSuppliers()">
                            <i class="fas fa-download me-2"></i>
                            Exporter
                        </button>
                    </div>
                </div>

                <!-- Liste des fournisseurs -->
                <div class="row">
                    <div class="col-12">
                        <div class="card-neomorphic">
                            <div class="card-body">
                                ${this.renderSuppliersList()}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modal d'ajout/édition -->
                ${this.renderSupplierModal()}

                <!-- Modal de détails -->
                ${this.renderDetailsModal()}
            </div>
        `;
    }

    /**
     * Rend la liste des fournisseurs
     */
    renderSuppliersList() {
        const filteredSuppliers = this.getFilteredSuppliers();

        if (filteredSuppliers.length === 0) {
            return `
                <div class="text-center py-5">
                    <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Aucun fournisseur trouvé</h5>
                    <p class="text-muted">Commencez par ajouter votre premier fournisseur</p>
                    <button class="btn btn-neomorphic btn-primary" onclick="suppliersModule.showAddModal()">
                        <i class="fas fa-plus me-2"></i>
                        Ajouter un fournisseur
                    </button>
                </div>
            `;
        }

        return `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Nom</th>
                            <th>Type</th>
                            <th>Contact</th>
                            <th>Statut</th>
                            <th>Dernière commande</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${filteredSuppliers.map(supplier => this.renderSupplierRow(supplier)).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    /**
     * Rend une ligne de fournisseur
     */
    renderSupplierRow(supplier) {
        const statusBadge = this.getStatusBadge(supplier.status);
        const lastOrder = this.getLastOrderDate(supplier.id);

        return `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="supplier-avatar me-3">
                            <i class="fas fa-building"></i>
                        </div>
                        <div>
                            <div class="fw-bold">${supplier.name}</div>
                            <small class="text-muted">${supplier.company || 'N/A'}</small>
                        </div>
                    </div>
                </td>
                <td>
                    ${this.renderSupplierTypes(supplier.type)}
                </td>
                <td>
                    <div>
                        <i class="fas fa-phone me-1"></i>
                        ${supplier.phone || 'N/A'}
                    </div>
                    <div>
                        <i class="fas fa-envelope me-1"></i>
                        ${supplier.email || 'N/A'}
                    </div>
                </td>
                <td>${statusBadge}</td>
                <td>
                    <small class="text-muted">
                        ${lastOrder ? Utils.formatDate(lastOrder) : 'Aucune commande'}
                    </small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-info" onclick="suppliersModule.showDetails('${supplier.id}')" 
                                title="Voir détails">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="suppliersModule.editSupplier('${supplier.id}')" 
                                title="Modifier">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="suppliersModule.deleteSupplier('${supplier.id}')" 
                                title="Supprimer">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    /**
     * Rend le modal d'ajout/édition
     */
    renderSupplierModal() {
        return `
            <div class="modal fade" id="supplierModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content modal-neomorphic">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-truck me-2"></i>
                                <span id="modalTitle">Nouveau Fournisseur</span>
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="supplierForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Nom du fournisseur *</label>
                                            <input type="text" class="form-control" id="supplierName" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Entreprise</label>
                                            <input type="text" class="form-control" id="supplierCompany">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Type *</label>
                                            <select class="form-select" id="supplierType" required>
                                                <option value="">Sélectionner un type</option>
                                                <option value="Viandes">Viandes</option>
                                                <option value="Légumes">Légumes</option>
                                                <option value="Volailles">Volailles</option>
                                                <option value="Poissons">Poissons</option>
                                                <option value="Produits laitiers">Produits laitiers</option>
                                                <option value="Épicerie">Épicerie</option>
                                                <option value="Alimentation générale">Alimentation générale</option>
                                                <option value="Achats divers">Achats divers</option>
                                                <option value="Produits asiatiques">Produits asiatiques</option>
                                                <option value="Charcuterie">Charcuterie</option>
                                                <option value="Boulangerie">Boulangerie</option>
                                                <option value="Pâtisserie">Pâtisserie</option>
                                                <option value="Emballage">Emballage</option>
                                                <option value="Produits finis">Produits finis</option>
                                                <option value="Boissons">Boissons</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Statut</label>
                                            <select class="form-select" id="supplierStatus">
                                                <option value="active">Actif</option>
                                                <option value="inactive">Inactif</option>
                                                <option value="suspended">Suspendu</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Téléphone</label>
                                            <input type="tel" class="form-control" id="supplierPhone">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Email</label>
                                            <input type="email" class="form-control" id="supplierEmail">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Adresse</label>
                                    <textarea class="form-control" id="supplierAddress" rows="3"></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Délai de livraison (jours)</label>
                                            <input type="number" class="form-control" id="supplierDeliveryTime" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Montant minimum de commande</label>
                                            <input type="number" class="form-control" id="supplierMinOrder" min="0" step="0.01">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Notes</label>
                                    <textarea class="form-control" id="supplierNotes" rows="2"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="button" class="btn btn-primary" onclick="suppliersModule.saveSupplier()">
                                <i class="fas fa-save me-2"></i>
                                Enregistrer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Rend le modal de détails
     */
    renderDetailsModal() {
        return `
            <div class="modal fade" id="supplierDetailsModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content modal-neomorphic">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-info-circle me-2"></i>
                                Détails du Fournisseur
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" id="supplierDetailsContent">
                            <!-- Contenu chargé dynamiquement -->
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Actions post-rendu
     */
    postRender() {
        // Gestionnaires d'événements
        this.attachEventListeners();
        
        // Référence globale pour les callbacks
        window.suppliersModule = this;
    }

    /**
     * Attache les gestionnaires d'événements
     */
    attachEventListeners() {
        // Recherche
        const searchInput = document.getElementById('searchSuppliers');
        if (searchInput) {
            searchInput.addEventListener('input', Utils.debounce((e) => {
                this.searchTerm = e.target.value;
                this.refreshList();
            }, 300));
        }

        // Tri
        const sortSelect = document.getElementById('sortSuppliers');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.sortBy = e.target.value;
                this.refreshList();
            });
        }
    }

    /**
     * Filtre et trie les fournisseurs avec déduplication
     */
    getFilteredSuppliers() {
        let filtered = [...this.suppliers];

        // Déduplication par nom de fournisseur
        const uniqueSuppliers = this.deduplicateSuppliers(filtered);

        // Filtrage par recherche
        let searchFiltered = uniqueSuppliers;
        if (this.searchTerm) {
            const term = this.searchTerm.toLowerCase();
            searchFiltered = uniqueSuppliers.filter(supplier =>
                supplier.name.toLowerCase().includes(term) ||
                supplier.company?.toLowerCase().includes(term) ||
                supplier.type.toLowerCase().includes(term)
            );
        }

        // Tri
        searchFiltered.sort((a, b) => {
            let aVal = a[this.sortBy];
            let bVal = b[this.sortBy];

            if (this.sortBy === 'createdAt') {
                aVal = new Date(aVal);
                bVal = new Date(bVal);
            }

            if (aVal < bVal) return this.sortOrder === 'asc' ? -1 : 1;
            if (aVal > bVal) return this.sortOrder === 'asc' ? 1 : -1;
            return 0;
        });

        return searchFiltered;
    }

    /**
     * Déduplique les fournisseurs par nom et combine leurs types
     */
    deduplicateSuppliers(suppliers) {
        const supplierMap = new Map();

        suppliers.forEach(supplier => {
            const key = supplier.name.toLowerCase().trim();

            if (supplierMap.has(key)) {
                // Fournisseur existant - combiner les types
                const existing = supplierMap.get(key);
                const existingTypes = existing.type ? existing.type.split(',').map(t => t.trim()) : [];
                const newTypes = supplier.type ? supplier.type.split(',').map(t => t.trim()) : [];

                // Combiner et dédupliquer les types
                const allTypes = [...new Set([...existingTypes, ...newTypes])].filter(t => t);
                existing.type = allTypes.join(', ');

                // Garder les informations les plus récentes
                if (supplier.createdAt > existing.createdAt) {
                    existing.phone = supplier.phone || existing.phone;
                    existing.email = supplier.email || existing.email;
                    existing.address = supplier.address || existing.address;
                    existing.company = supplier.company || existing.company;
                }
            } else {
                // Nouveau fournisseur
                supplierMap.set(key, { ...supplier });
            }
        });

        return Array.from(supplierMap.values());
    }

    /**
     * Rend les types d'un fournisseur avec badges multiples
     */
    renderSupplierTypes(typeString) {
        if (!typeString) {
            return '<span class="badge bg-secondary">Non spécifié</span>';
        }

        const types = typeString.split(',').map(t => t.trim()).filter(t => t);

        if (types.length === 0) {
            return '<span class="badge bg-secondary">Non spécifié</span>';
        }

        return types.map(type =>
            `<span class="badge bg-primary me-1 mb-1">${type}</span>`
        ).join('');
    }

    /**
     * Rafraîchit la liste
     */
    refreshList() {
        const container = document.querySelector('.card-neomorphic .card-body');
        if (container) {
            container.innerHTML = this.renderSuppliersList();
        }
    }

    /**
     * Affiche le modal d'ajout
     */
    showAddModal() {
        this.currentSupplier = null;
        document.getElementById('modalTitle').textContent = 'Nouveau Fournisseur';
        this.resetForm();
        new bootstrap.Modal(document.getElementById('supplierModal')).show();
    }

    /**
     * Édite un fournisseur
     */
    editSupplier(id) {
        this.currentSupplier = this.suppliers.find(s => s.id === id);
        if (!this.currentSupplier) return;

        document.getElementById('modalTitle').textContent = 'Modifier le Fournisseur';
        this.fillForm(this.currentSupplier);
        new bootstrap.Modal(document.getElementById('supplierModal')).show();
    }

    /**
     * Remplit le formulaire
     */
    fillForm(supplier) {
        document.getElementById('supplierName').value = supplier.name || '';
        document.getElementById('supplierCompany').value = supplier.company || '';
        document.getElementById('supplierType').value = supplier.type || '';
        document.getElementById('supplierStatus').value = supplier.status || 'active';
        document.getElementById('supplierPhone').value = supplier.phone || '';
        document.getElementById('supplierEmail').value = supplier.email || '';
        document.getElementById('supplierAddress').value = supplier.address || '';
        document.getElementById('supplierDeliveryTime').value = supplier.deliveryTime || '';
        document.getElementById('supplierMinOrder').value = supplier.minOrder || '';
        document.getElementById('supplierNotes').value = supplier.notes || '';
    }

    /**
     * Remet à zéro le formulaire
     */
    resetForm() {
        document.getElementById('supplierForm').reset();
    }

    /**
     * Sauvegarde un fournisseur
     */
    async saveSupplier() {
        const formData = this.getFormData();
        
        // Validation
        const validation = Utils.validateForm(formData, {
            name: { required: true, min: 2 },
            type: { required: true },
            email: { email: true },
            phone: { phone: true }
        });

        if (!validation.isValid) {
            this.showValidationErrors(validation.errors);
            return;
        }

        try {
            if (this.currentSupplier) {
                // Mise à jour
                const updated = storage.update('suppliers', this.currentSupplier.id, formData);
                if (updated) {
                    Utils.showToast('Fournisseur mis à jour avec succès', 'success');
                }
            } else {
                // Création
                const created = storage.add('suppliers', formData);
                if (created) {
                    Utils.showToast('Fournisseur créé avec succès', 'success');
                }
            }

            // Recharger les données et fermer le modal
            this.loadSuppliers();
            this.refreshList();
            bootstrap.Modal.getInstance(document.getElementById('supplierModal')).hide();

        } catch (error) {
            console.error('Erreur lors de la sauvegarde:', error);
            Utils.showToast('Erreur lors de la sauvegarde', 'error');
        }
    }

    /**
     * Récupère les données du formulaire
     */
    getFormData() {
        return {
            name: document.getElementById('supplierName').value.trim(),
            company: document.getElementById('supplierCompany').value.trim(),
            type: document.getElementById('supplierType').value,
            status: document.getElementById('supplierStatus').value,
            phone: document.getElementById('supplierPhone').value.trim(),
            email: document.getElementById('supplierEmail').value.trim(),
            address: document.getElementById('supplierAddress').value.trim(),
            deliveryTime: parseInt(document.getElementById('supplierDeliveryTime').value) || 0,
            minOrder: parseFloat(document.getElementById('supplierMinOrder').value) || 0,
            notes: document.getElementById('supplierNotes').value.trim()
        };
    }

    /**
     * Affiche les erreurs de validation
     */
    showValidationErrors(errors) {
        Object.keys(errors).forEach(field => {
            const input = document.getElementById(`supplier${field.charAt(0).toUpperCase() + field.slice(1)}`);
            if (input) {
                input.classList.add('is-invalid');
                // Retirer la classe après 3 secondes
                setTimeout(() => input.classList.remove('is-invalid'), 3000);
            }
        });

        Utils.showToast('Veuillez corriger les erreurs dans le formulaire', 'warning');
    }

    /**
     * Supprime un fournisseur
     */
    async deleteSupplier(id) {
        const supplier = this.suppliers.find(s => s.id === id);
        if (!supplier) return;

        const confirmed = await Utils.confirm(
            `Êtes-vous sûr de vouloir supprimer le fournisseur "${supplier.name}" ?`,
            'Confirmer la suppression'
        );

        if (confirmed) {
            if (storage.delete('suppliers', id)) {
                Utils.showToast('Fournisseur supprimé avec succès', 'success');
                this.loadSuppliers();
                this.refreshList();
            } else {
                Utils.showToast('Erreur lors de la suppression', 'error');
            }
        }
    }

    /**
     * Affiche les détails d'un fournisseur
     */
    showDetails(id) {
        const supplier = this.suppliers.find(s => s.id === id);
        if (!supplier) return;

        const content = this.renderSupplierDetails(supplier);
        document.getElementById('supplierDetailsContent').innerHTML = content;
        new bootstrap.Modal(document.getElementById('supplierDetailsModal')).show();
    }

    /**
     * Rend les détails d'un fournisseur
     */
    renderSupplierDetails(supplier) {
        const purchases = storage.get('purchases') || [];
        const supplierPurchases = purchases.filter(p => p.supplierId === supplier.id);
        const totalPurchases = Utils.sumBy(supplierPurchases, 'totalAmount');

        return `
            <div class="row">
                <div class="col-md-6">
                    <h6>Informations générales</h6>
                    <table class="table table-sm">
                        <tr><td><strong>Nom:</strong></td><td>${supplier.name}</td></tr>
                        <tr><td><strong>Entreprise:</strong></td><td>${supplier.company || 'N/A'}</td></tr>
                        <tr><td><strong>Type:</strong></td><td>${supplier.type}</td></tr>
                        <tr><td><strong>Statut:</strong></td><td>${this.getStatusBadge(supplier.status)}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>Contact</h6>
                    <table class="table table-sm">
                        <tr><td><strong>Téléphone:</strong></td><td>${supplier.phone || 'N/A'}</td></tr>
                        <tr><td><strong>Email:</strong></td><td>${supplier.email || 'N/A'}</td></tr>
                        <tr><td><strong>Adresse:</strong></td><td>${supplier.address || 'N/A'}</td></tr>
                    </table>
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-12">
                    <h6>Statistiques</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="stat-card">
                                <div class="stat-value text-primary">${supplierPurchases.length}</div>
                                <div class="stat-label">Commandes</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stat-card">
                                <div class="stat-value text-success">${Utils.formatPrice(totalPurchases)}</div>
                                <div class="stat-label">Total achats</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stat-card">
                                <div class="stat-value text-info">${supplier.deliveryTime || 0}j</div>
                                <div class="stat-label">Délai livraison</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            ${supplier.notes ? `
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>Notes</h6>
                        <p class="text-muted">${supplier.notes}</p>
                    </div>
                </div>
            ` : ''}
        `;
    }

    /**
     * Exporte les fournisseurs
     */
    exportSuppliers() {
        const data = this.getFilteredSuppliers();
        const filename = `fournisseurs_${new Date().toISOString().split('T')[0]}.csv`;
        Utils.exportToCSV(data, filename);
        Utils.showToast('Export réalisé avec succès', 'success');
    }

    /**
     * Retourne le badge de statut
     */
    getStatusBadge(status) {
        const badges = {
            active: '<span class="badge bg-success">Actif</span>',
            inactive: '<span class="badge bg-secondary">Inactif</span>',
            suspended: '<span class="badge bg-danger">Suspendu</span>'
        };
        return badges[status] || badges.active;
    }

    /**
     * Retourne la date de dernière commande
     */
    getLastOrderDate(supplierId) {
        const purchases = storage.get('purchases') || [];
        const supplierPurchases = purchases.filter(p => p.supplierId === supplierId);
        
        if (supplierPurchases.length === 0) return null;
        
        return supplierPurchases
            .sort((a, b) => new Date(b.date) - new Date(a.date))[0].date;
    }

    /**
     * Nettoie le module
     */
    destroy() {
        if (window.suppliersModule === this) {
            delete window.suppliersModule;
        }
    }
}

// Rendre le module disponible globalement
window.SuppliersModule = SuppliersModule;
