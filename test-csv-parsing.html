<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test CSV Parsing - RestoManager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Test CSV Parsing</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test de Fichier</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">Sélectionner un fichier CSV</label>
                            <input type="file" class="form-control" id="csvFile" accept=".csv,.txt">
                        </div>
                        
                        <div class="mb-3">
                            <button class="btn btn-primary" onclick="testFile()">
                                <i class="fas fa-play me-2"></i>Tester le Parsing
                            </button>
                            
                            <button class="btn btn-success ms-2" onclick="downloadTestFiles()">
                                <i class="fas fa-download me-2"></i>Télécharger Tests
                            </button>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Ou coller du contenu CSV :</label>
                            <textarea class="form-control" id="csvContent" rows="6" placeholder="Article,Quantité,Prix HT,TVA
Tomates,10,2.50,5.5
Oignons,5,1.20,5.5"></textarea>
                            <button class="btn btn-info mt-2" onclick="testContent()">
                                <i class="fas fa-play me-2"></i>Tester le Contenu
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Console de Debug</h5>
                    </div>
                    <div class="card-body">
                        <div id="debugOutput" style="max-height: 400px; overflow-y: auto; font-family: monospace; font-size: 12px; background: #f8f9fa; padding: 10px; border-radius: 5px;">
                            Aucun test effectué
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Résultats du Parsing</h5>
                    </div>
                    <div class="card-body">
                        <div id="results">
                            <p class="text-muted">Aucun résultat</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="assets/js/core/storage.js"></script>
    <script src="assets/js/core/utils.js"></script>
    <script src="assets/js/modules/inventory.js"></script>

    <script>
        let debugOutput = [];
        let inventoryModule;

        // Rediriger console.log vers notre debug
        const originalLog = console.log;
        const originalError = console.error;
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            debugOutput.push(`[LOG] ${args.join(' ')}`);
            updateDebugDisplay();
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            debugOutput.push(`[ERROR] ${args.join(' ')}`);
            updateDebugDisplay();
        };

        function updateDebugDisplay() {
            const debugDiv = document.getElementById('debugOutput');
            debugDiv.innerHTML = debugOutput.slice(-30).map(line => `<div>${line}</div>`).join('');
            debugDiv.scrollTop = debugDiv.scrollHeight;
        }

        // Initialiser le module
        window.addEventListener('load', () => {
            inventoryModule = new InventoryModule();
            inventoryModule.init().then(() => {
                console.log('Module inventory initialisé');
            });
        });

        async function testFile() {
            const fileInput = document.getElementById('csvFile');
            const file = fileInput.files[0];

            if (!file) {
                alert('Veuillez sélectionner un fichier');
                return;
            }

            debugOutput = [];
            console.log('=== TEST DE FICHIER ===');
            console.log(`Fichier: ${file.name}, Taille: ${file.size} bytes, Type: ${file.type}`);

            try {
                const text = await readFileAsText(file);
                console.log('Contenu du fichier (premiers 500 caractères):');
                console.log(text.substring(0, 500));
                
                await testParsing(text);
                
            } catch (error) {
                console.error('Erreur:', error.message);
                showError(error.message);
            }
        }

        async function testContent() {
            const content = document.getElementById('csvContent').value;
            
            if (!content.trim()) {
                alert('Veuillez saisir du contenu CSV');
                return;
            }

            debugOutput = [];
            console.log('=== TEST DE CONTENU ===');
            console.log('Contenu saisi:');
            console.log(content);
            
            try {
                await testParsing(content);
            } catch (error) {
                console.error('Erreur:', error.message);
                showError(error.message);
            }
        }

        async function testParsing(text) {
            try {
                // Test du parsing CSV
                console.log('--- Étape 1: Parsing CSV ---');
                const csvData = inventoryModule.parseCSV(text);
                console.log('Données CSV parsées:', csvData);
                
                // Test de la normalisation
                console.log('--- Étape 2: Normalisation ---');
                const normalizedData = inventoryModule.normalizeImportData(csvData);
                console.log('Données normalisées:', normalizedData);
                
                // Afficher les résultats
                showResults(normalizedData);
                
            } catch (error) {
                console.error('Erreur lors du parsing:', error.message);
                showError(error.message);
            }
        }

        function readFileAsText(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (e) => resolve(e.target.result);
                reader.onerror = () => reject(new Error('Erreur de lecture du fichier'));
                reader.readAsText(file, 'UTF-8');
            });
        }

        function showResults(data) {
            let totalHT = 0;
            let totalTTC = 0;
            
            const html = `
                <div class="alert alert-success">
                    <h6>✅ Parsing réussi!</h6>
                    <p>${data.length} article(s) traité(s) avec succès.</p>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Article</th>
                                <th>Quantité</th>
                                <th>Prix HT</th>
                                <th>TVA</th>
                                <th>Prix TTC</th>
                                <th>Montant HT</th>
                                <th>Montant TTC</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${data.map(item => {
                                totalHT += item.montantHT;
                                totalTTC += item.montantTTC;
                                
                                return `
                                    <tr>
                                        <td>${item.article}</td>
                                        <td>${item.quantite.toFixed(2)}</td>
                                        <td>${item.prixHT.toFixed(2)} €</td>
                                        <td>${item.tva.toFixed(1)}%</td>
                                        <td>${item.prixTTC.toFixed(2)} €</td>
                                        <td>${item.montantHT.toFixed(2)} €</td>
                                        <td>${item.montantTTC.toFixed(2)} €</td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                        <tfoot>
                            <tr class="table-info">
                                <td colspan="5"><strong>TOTAUX</strong></td>
                                <td><strong>${totalHT.toFixed(2)} €</strong></td>
                                <td><strong>${totalTTC.toFixed(2)} €</strong></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            `;
            
            document.getElementById('results').innerHTML = html;
        }

        function showError(message) {
            const html = `
                <div class="alert alert-danger">
                    <h6>❌ Erreur de parsing</h6>
                    <p>${message}</p>
                </div>
            `;
            
            document.getElementById('results').innerHTML = html;
        }

        function downloadTestFiles() {
            // Fichier 1: Format virgule
            const csvComma = `Article,Quantité,Prix HT,TVA
Tomates,10,2.50,5.5
Oignons,5,1.20,5.5
Carottes,8,1.80,5.5`;

            // Fichier 2: Format tabulation
            const csvTab = `Article\tQuantité\tPrix HT\tTVA
Tomates\t10\t2,50\t5,5
Oignons\t5\t1,20\t5,5
Carottes\t8\t1,80\t5,5`;

            // Fichier 3: Format point-virgule
            const csvSemicolon = `Article;Quantité;Prix HT;TVA
Tomates;10;2,50;5,5
Oignons;5;1,20;5,5
Carottes;8;1,80;5,5`;

            downloadFile(csvComma, 'test_virgule.csv');
            setTimeout(() => downloadFile(csvTab, 'test_tabulation.csv'), 100);
            setTimeout(() => downloadFile(csvSemicolon, 'test_pointvirgule.csv'), 200);
        }

        function downloadFile(content, filename) {
            const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    </script>
</body>
</html>
