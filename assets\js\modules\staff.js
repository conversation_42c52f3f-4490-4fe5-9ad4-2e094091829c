/**
 * RestoManager - Module Gestion du Personnel
 * Gestion complète du personnel avec fiches et statuts
 */

class StaffModule {
    constructor() {
        this.staff = [];
        this.currentStaff = null;
        this.searchTerm = '';
        this.filterStatus = '';
        this.filterPosition = '';
    }

    async init() {
        this.loadData();
    }

    loadData() {
        this.staff = storage.get('staff') || [];
    }

    async render() {
        return `
            <div class="staff-container">
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card-neomorphic">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <h2 class="mb-0">
                                            <i class="fas fa-users text-primary me-2"></i>
                                            Gestion du Personnel
                                        </h2>
                                        <p class="text-muted mb-0">${this.staff.length} membre(s) du personnel</p>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <button class="btn btn-neomorphic btn-primary" onclick="staffModule.showAddModal()">
                                            <i class="fas fa-user-plus me-2"></i>Nouveau Membre
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistiques -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-value">${this.getActiveStaffCount()}</div>
                            <div class="stat-label">Personnel Actif</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-user-tie"></i>
                            </div>
                            <div class="stat-value">${this.getManagersCount()}</div>
                            <div class="stat-label">Managers</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-utensils"></i>
                            </div>
                            <div class="stat-value">${this.getCooksCount()}</div>
                            <div class="stat-label">Cuisiniers</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-concierge-bell"></i>
                            </div>
                            <div class="stat-value">${this.getServersCount()}</div>
                            <div class="stat-label">Serveurs</div>
                        </div>
                    </div>
                </div>

                <!-- Filtres -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" placeholder="Rechercher..." 
                                   id="searchStaff" value="${this.searchTerm}">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="filterStatus">
                            <option value="">Tous les statuts</option>
                            <option value="active" ${this.filterStatus === 'active' ? 'selected' : ''}>Actif</option>
                            <option value="inactive" ${this.filterStatus === 'inactive' ? 'selected' : ''}>Inactif</option>
                            <option value="vacation" ${this.filterStatus === 'vacation' ? 'selected' : ''}>En congé</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="filterPosition">
                            <option value="">Tous les postes</option>
                            <option value="manager" ${this.filterPosition === 'manager' ? 'selected' : ''}>Manager</option>
                            <option value="chef" ${this.filterPosition === 'chef' ? 'selected' : ''}>Chef</option>
                            <option value="cook" ${this.filterPosition === 'cook' ? 'selected' : ''}>Cuisinier</option>
                            <option value="server" ${this.filterPosition === 'server' ? 'selected' : ''}>Serveur</option>
                            <option value="cleaner" ${this.filterPosition === 'cleaner' ? 'selected' : ''}>Agent d'entretien</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-neomorphic btn-info w-100" onclick="staffModule.exportStaff()">
                            <i class="fas fa-download me-1"></i>Export
                        </button>
                    </div>
                </div>

                <!-- Liste du personnel -->
                <div class="row">
                    <div class="col-12">
                        <div class="card-neomorphic">
                            <div class="card-body">
                                ${this.renderStaffList()}
                            </div>
                        </div>
                    </div>
                </div>

                ${this.renderStaffModal()}
            </div>
        `;
    }

    renderStaffList() {
        const filteredStaff = this.getFilteredStaff();

        if (filteredStaff.length === 0) {
            return `
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Aucun membre du personnel</h5>
                    <p class="text-muted">Ajoutez votre premier membre du personnel</p>
                    <button class="btn btn-neomorphic btn-primary" onclick="staffModule.showAddModal()">
                        <i class="fas fa-user-plus me-2"></i>Nouveau Membre
                    </button>
                </div>
            `;
        }

        return `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Nom</th>
                            <th>Poste</th>
                            <th>Contact</th>
                            <th>Statut</th>
                            <th>Date d'embauche</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${filteredStaff.map(member => this.renderStaffRow(member)).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    renderStaffRow(member) {
        const statusBadge = this.getStatusBadge(member.status);
        const positionIcon = this.getPositionIcon(member.position);

        return `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="staff-avatar me-3">
                            <i class="fas fa-user"></i>
                        </div>
                        <div>
                            <div class="fw-bold">${member.firstName} ${member.lastName}</div>
                            <small class="text-muted">${member.email || 'Pas d\'email'}</small>
                        </div>
                    </div>
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <i class="${positionIcon} me-2"></i>
                        ${this.getPositionName(member.position)}
                    </div>
                </td>
                <td>
                    <div>
                        <i class="fas fa-phone me-1"></i>
                        ${member.phone || 'N/A'}
                    </div>
                </td>
                <td>${statusBadge}</td>
                <td>
                    <small class="text-muted">
                        ${member.hireDate ? Utils.formatDate(member.hireDate) : 'N/A'}
                    </small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-info" onclick="staffModule.viewStaff('${member.id}')" title="Voir détails">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="staffModule.editStaff('${member.id}')" title="Modifier">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="staffModule.deleteStaff('${member.id}')" title="Supprimer">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    renderStaffModal() {
        return `
            <div class="modal fade" id="staffModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content modal-neomorphic">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-user me-2"></i>
                                <span id="staffModalTitle">Nouveau Membre</span>
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="staffForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Prénom *</label>
                                            <input type="text" class="form-control" id="staffFirstName" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Nom *</label>
                                            <input type="text" class="form-control" id="staffLastName" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Poste *</label>
                                            <select class="form-select" id="staffPosition" required>
                                                <option value="">Sélectionner un poste</option>
                                                <option value="manager">Manager</option>
                                                <option value="chef">Chef</option>
                                                <option value="cook">Cuisinier</option>
                                                <option value="server">Serveur</option>
                                                <option value="cleaner">Agent d'entretien</option>
                                                <option value="other">Autre</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Statut</label>
                                            <select class="form-select" id="staffStatus">
                                                <option value="active">Actif</option>
                                                <option value="inactive">Inactif</option>
                                                <option value="vacation">En congé</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Téléphone</label>
                                            <input type="tel" class="form-control" id="staffPhone">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Email</label>
                                            <input type="email" class="form-control" id="staffEmail">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Date d'embauche</label>
                                            <input type="date" class="form-control" id="staffHireDate">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Salaire horaire (€)</label>
                                            <input type="number" class="form-control" id="staffHourlyRate" min="0" step="0.01">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Adresse</label>
                                    <textarea class="form-control" id="staffAddress" rows="2"></textarea>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Notes</label>
                                    <textarea class="form-control" id="staffNotes" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="button" class="btn btn-primary" onclick="staffModule.saveStaff()">
                                <i class="fas fa-save me-2"></i>Enregistrer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    postRender() {
        this.attachEventListeners();
        window.staffModule = this;
    }

    attachEventListeners() {
        const searchInput = document.getElementById('searchStaff');
        if (searchInput) {
            searchInput.addEventListener('input', Utils.debounce((e) => {
                this.searchTerm = e.target.value;
                this.refreshList();
            }, 300));
        }

        const statusFilter = document.getElementById('filterStatus');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filterStatus = e.target.value;
                this.refreshList();
            });
        }

        const positionFilter = document.getElementById('filterPosition');
        if (positionFilter) {
            positionFilter.addEventListener('change', (e) => {
                this.filterPosition = e.target.value;
                this.refreshList();
            });
        }
    }

    // Méthodes utilitaires
    getActiveStaffCount() {
        return this.staff.filter(s => s.status === 'active').length;
    }

    getManagersCount() {
        return this.staff.filter(s => s.position === 'manager' && s.status === 'active').length;
    }

    getCooksCount() {
        return this.staff.filter(s => ['chef', 'cook'].includes(s.position) && s.status === 'active').length;
    }

    getServersCount() {
        return this.staff.filter(s => s.position === 'server' && s.status === 'active').length;
    }

    getFilteredStaff() {
        let filtered = [...this.staff];

        if (this.searchTerm) {
            const term = this.searchTerm.toLowerCase();
            filtered = filtered.filter(member => 
                member.firstName.toLowerCase().includes(term) ||
                member.lastName.toLowerCase().includes(term) ||
                (member.email && member.email.toLowerCase().includes(term))
            );
        }

        if (this.filterStatus) {
            filtered = filtered.filter(member => member.status === this.filterStatus);
        }

        if (this.filterPosition) {
            filtered = filtered.filter(member => member.position === this.filterPosition);
        }

        return filtered.sort((a, b) => `${a.firstName} ${a.lastName}`.localeCompare(`${b.firstName} ${b.lastName}`));
    }

    getStatusBadge(status) {
        const badges = {
            active: '<span class="badge bg-success">Actif</span>',
            inactive: '<span class="badge bg-secondary">Inactif</span>',
            vacation: '<span class="badge bg-warning">En congé</span>'
        };
        return badges[status] || badges.active;
    }

    getPositionIcon(position) {
        const icons = {
            manager: 'fas fa-user-tie',
            chef: 'fas fa-chef-hat',
            cook: 'fas fa-utensils',
            server: 'fas fa-concierge-bell',
            cleaner: 'fas fa-broom',
            other: 'fas fa-user'
        };
        return icons[position] || icons.other;
    }

    getPositionName(position) {
        const names = {
            manager: 'Manager',
            chef: 'Chef',
            cook: 'Cuisinier',
            server: 'Serveur',
            cleaner: 'Agent d\'entretien',
            other: 'Autre'
        };
        return names[position] || 'Autre';
    }

    // Actions
    showAddModal() {
        this.currentStaff = null;
        document.getElementById('staffModalTitle').textContent = 'Nouveau Membre';
        this.resetForm();
        new bootstrap.Modal(document.getElementById('staffModal')).show();
    }

    editStaff(id) {
        this.currentStaff = this.staff.find(s => s.id === id);
        if (!this.currentStaff) return;

        document.getElementById('staffModalTitle').textContent = 'Modifier le Membre';
        this.fillForm(this.currentStaff);
        new bootstrap.Modal(document.getElementById('staffModal')).show();
    }

    async saveStaff() {
        const formData = this.getFormData();
        
        if (!this.validateForm(formData)) {
            return;
        }

        try {
            if (this.currentStaff) {
                storage.update('staff', this.currentStaff.id, formData);
                Utils.showToast('Membre mis à jour avec succès', 'success');
            } else {
                storage.add('staff', formData);
                Utils.showToast('Membre ajouté avec succès', 'success');
            }

            this.loadData();
            this.refreshList();
            bootstrap.Modal.getInstance(document.getElementById('staffModal')).hide();

        } catch (error) {
            console.error('Erreur lors de la sauvegarde:', error);
            Utils.showToast('Erreur lors de la sauvegarde', 'error');
        }
    }

    getFormData() {
        return {
            firstName: document.getElementById('staffFirstName').value.trim(),
            lastName: document.getElementById('staffLastName').value.trim(),
            position: document.getElementById('staffPosition').value,
            status: document.getElementById('staffStatus').value,
            phone: document.getElementById('staffPhone').value.trim(),
            email: document.getElementById('staffEmail').value.trim(),
            hireDate: document.getElementById('staffHireDate').value,
            hourlyRate: parseFloat(document.getElementById('staffHourlyRate').value) || 0,
            address: document.getElementById('staffAddress').value.trim(),
            notes: document.getElementById('staffNotes').value.trim()
        };
    }

    validateForm(data) {
        if (!data.firstName || !data.lastName || !data.position) {
            Utils.showToast('Veuillez remplir tous les champs obligatoires', 'warning');
            return false;
        }
        return true;
    }

    fillForm(member) {
        document.getElementById('staffFirstName').value = member.firstName || '';
        document.getElementById('staffLastName').value = member.lastName || '';
        document.getElementById('staffPosition').value = member.position || '';
        document.getElementById('staffStatus').value = member.status || 'active';
        document.getElementById('staffPhone').value = member.phone || '';
        document.getElementById('staffEmail').value = member.email || '';
        document.getElementById('staffHireDate').value = member.hireDate || '';
        document.getElementById('staffHourlyRate').value = member.hourlyRate || '';
        document.getElementById('staffAddress').value = member.address || '';
        document.getElementById('staffNotes').value = member.notes || '';
    }

    resetForm() {
        document.getElementById('staffForm').reset();
    }

    async deleteStaff(id) {
        const member = this.staff.find(s => s.id === id);
        if (!member) return;

        const confirmed = await Utils.confirm(
            `Supprimer ${member.firstName} ${member.lastName} du personnel ?`,
            'Confirmer la suppression'
        );

        if (confirmed) {
            if (storage.delete('staff', id)) {
                Utils.showToast('Membre supprimé avec succès', 'success');
                this.loadData();
                this.refreshList();
            }
        }
    }

    refreshList() {
        const container = document.querySelector('.staff-container .card-neomorphic .card-body');
        if (container) {
            container.innerHTML = this.renderStaffList();
        }
    }

    exportStaff() {
        const data = this.getFilteredStaff().map(member => ({
            prenom: member.firstName,
            nom: member.lastName,
            poste: this.getPositionName(member.position),
            statut: member.status,
            telephone: member.phone || '',
            email: member.email || '',
            date_embauche: member.hireDate || '',
            salaire_horaire: member.hourlyRate || 0,
            adresse: member.address || '',
            notes: member.notes || ''
        }));

        const filename = `personnel_${new Date().toISOString().split('T')[0]}.csv`;
        Utils.exportToCSV(data, filename);
        Utils.showToast('Export réalisé avec succès', 'success');
    }

    destroy() {
        if (window.staffModule === this) {
            delete window.staffModule;
        }
    }
}

window.StaffModule = StaffModule;
