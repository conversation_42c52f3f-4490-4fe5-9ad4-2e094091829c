<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Dropdown Fix</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Test Correction Dropdown</h1>
        
        <div class="alert alert-info">
            <p>Cette page teste la correction du problème de limitation du dropdown des matières premières.</p>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Actions</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary mb-2" onclick="createTestMaterials()">1. Créer 20 Matières</button>
                        <button class="btn btn-success mb-2" onclick="initPurchaseModule()">2. Init Module Achats</button>
                        <button class="btn btn-info mb-2" onclick="testDropdownContent()">3. Tester Dropdown</button>
                        <button class="btn btn-warning mb-2" onclick="openPurchaseModal()">4. Ouvrir Modal</button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Résultats</h5>
                    </div>
                    <div class="card-body">
                        <div id="results">
                            <p class="text-muted">Aucun test effectué</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test direct du dropdown -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Direct Dropdown</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">Matière première (Test Direct)</label>
                                <select class="form-select" id="directTestSelect" onchange="testDirectSelection()">
                                    <option value="">Sélectionner</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Prix auto-rempli</label>
                                <input type="text" class="form-control" id="directTestPrice" readonly>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Nombre d'options</label>
                                <input type="text" class="form-control" id="optionsCount" readonly>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal d'achat pour test -->
        <div class="modal fade" id="testPurchaseModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Test Modal Achat</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-4">
                                <label class="form-label">Date</label>
                                <input type="date" class="form-control" id="testDate">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Fournisseur</label>
                                <select class="form-select" id="testSupplier">
                                    <option value="">Sélectionner</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6>Articles achetés</h6>
                                <div id="testPurchaseItems">
                                    <!-- Items will be added here -->
                                </div>
                                <button type="button" class="btn btn-success btn-sm" onclick="addTestItem()">
                                    <i class="fas fa-plus me-1"></i>
                                    Ajouter un article
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/core/storage.js"></script>
    <script src="assets/js/core/utils.js"></script>
    <script src="assets/js/modules/purchases.js"></script>

    <script>
        let purchaseModule = null;

        window.addEventListener('load', () => {
            storage.init();
            updateResults('✅ Storage initialisé');
        });

        function createTestMaterials() {
            // Créer 20 matières premières avec prix
            const materials = [
                { name: 'Ail', unit: 'kilogramme', price: 25.50 },
                { name: 'Aubergine', unit: 'kilogramme', price: 8.75 },
                { name: 'Betterave', unit: 'kilogramme', price: 6.25 },
                { name: 'Tomates', unit: 'kilogramme', price: 12.50 },
                { name: 'Oignons', unit: 'kilogramme', price: 8.00 },
                { name: 'Carottes', unit: 'kilogramme', price: 6.50 },
                { name: 'Pommes de terre', unit: 'kilogramme', price: 5.00 },
                { name: 'Courgettes', unit: 'kilogramme', price: 9.00 },
                { name: 'Poivrons', unit: 'kilogramme', price: 15.00 },
                { name: 'Concombres', unit: 'kilogramme', price: 7.50 },
                { name: 'Salade', unit: 'pièce', price: 3.00 },
                { name: 'Persil', unit: 'botte', price: 2.50 },
                { name: 'Coriandre', unit: 'botte', price: 3.00 },
                { name: 'Menthe', unit: 'botte', price: 4.00 },
                { name: 'Basilic', unit: 'botte', price: 5.00 },
                { name: 'Poulet entier', unit: 'kilogramme', price: 45.00 },
                { name: 'Escalope de poulet', unit: 'kilogramme', price: 55.00 },
                { name: 'Bœuf haché', unit: 'kilogramme', price: 65.00 },
                { name: 'Saumon', unit: 'kilogramme', price: 120.00 },
                { name: 'Crevettes', unit: 'kilogramme', price: 150.00 }
            ];

            // Nettoyer les données existantes
            storage.set('rawMaterials', []);
            storage.set('inventory', []);

            let created = 0;
            materials.forEach(material => {
                // Créer la matière première
                const newMaterial = storage.add('rawMaterials', {
                    name: material.name,
                    unit: material.unit,
                    categoryId: 1,
                    description: 'Article de test',
                    createdAt: new Date().toISOString()
                });

                // Créer l'entrée d'inventaire
                storage.add('inventory', {
                    materialId: newMaterial.id,
                    quantity: Math.floor(Math.random() * 50) + 10,
                    averagePrice: material.price,
                    unitPrice: material.price,
                    totalValue: (Math.floor(Math.random() * 50) + 10) * material.price,
                    lastUpdated: new Date().toISOString()
                });

                created++;
            });

            // Créer un fournisseur de test
            storage.add('suppliers', {
                name: 'SALIPRO',
                type: 'Alimentation générale',
                phone: '0522-123456',
                email: '<EMAIL>',
                address: 'Casablanca, Maroc',
                status: 'active'
            });

            updateResults(`✅ ${created} matières premières créées avec succès`);
            updateDirectDropdown();
        }

        async function initPurchaseModule() {
            try {
                purchaseModule = new PurchasesModule();
                await purchaseModule.init();
                
                // Rendre disponible globalement
                window.purchasesModule = purchaseModule;
                
                updateResults(`✅ Module achats initialisé avec ${purchaseModule.rawMaterials.length} matières premières`);
                
            } catch (error) {
                updateResults(`❌ Erreur initialisation: ${error.message}`);
                console.error(error);
            }
        }

        function testDropdownContent() {
            if (!purchaseModule) {
                updateResults('❌ Module non initialisé');
                return;
            }

            const rawMaterials = purchaseModule.rawMaterials;
            updateResults(`🔍 Module contient ${rawMaterials.length} matières premières`);
            
            // Tester la méthode getInventoryPrice
            let pricesFound = 0;
            rawMaterials.forEach(material => {
                const priceData = purchaseModule.getInventoryPrice(material.id);
                if (priceData.unitPrice > 0) {
                    pricesFound++;
                }
            });
            
            updateResults(`💰 ${pricesFound} matières avec prix trouvées`);
            updateDirectDropdown();
        }

        function updateDirectDropdown() {
            const select = document.getElementById('directTestSelect');
            const rawMaterials = storage.get('rawMaterials') || [];
            const inventory = storage.get('inventory') || [];
            
            select.innerHTML = '<option value="">Sélectionner</option>';
            
            rawMaterials.forEach(material => {
                const inventoryItem = inventory.find(item => item.materialId === material.id);
                const unitPrice = inventoryItem ? (inventoryItem.unitPrice || inventoryItem.averagePrice || 0) : 0;
                const priceInfo = unitPrice > 0 ? ` - ${Utils.formatPrice(unitPrice)}` : '';
                
                const option = document.createElement('option');
                option.value = material.id;
                option.dataset.unitPrice = unitPrice;
                option.textContent = `${material.name} (${material.unit})${priceInfo}`;
                select.appendChild(option);
            });
            
            document.getElementById('optionsCount').value = rawMaterials.length;
        }

        function testDirectSelection() {
            const select = document.getElementById('directTestSelect');
            const selectedOption = select.selectedOptions[0];
            const priceInput = document.getElementById('directTestPrice');
            
            if (selectedOption && selectedOption.dataset.unitPrice) {
                const unitPrice = parseFloat(selectedOption.dataset.unitPrice);
                priceInput.value = Utils.formatPrice(unitPrice);
                updateResults(`✅ Prix auto-rempli: ${Utils.formatPrice(unitPrice)}`);
            } else {
                priceInput.value = '';
                updateResults('❌ Aucun prix trouvé');
            }
        }

        function openPurchaseModal() {
            if (!purchaseModule) {
                updateResults('❌ Module non initialisé');
                return;
            }

            // Préparer le modal
            document.getElementById('testDate').value = new Date().toISOString().split('T')[0];
            
            // Remplir les fournisseurs
            const supplierSelect = document.getElementById('testSupplier');
            supplierSelect.innerHTML = '<option value="">Sélectionner</option>';
            purchaseModule.suppliers.forEach(supplier => {
                const option = document.createElement('option');
                option.value = supplier.id;
                option.textContent = supplier.name;
                supplierSelect.appendChild(option);
            });
            
            // Vider les articles et en ajouter un
            document.getElementById('testPurchaseItems').innerHTML = '';
            addTestItem();
            
            // Afficher le modal
            new bootstrap.Modal(document.getElementById('testPurchaseModal')).show();
            
            updateResults('✅ Modal de test ouvert');
        }

        function addTestItem() {
            if (!purchaseModule) return;
            
            const container = document.getElementById('testPurchaseItems');
            const itemIndex = container.children.length;
            
            const itemHtml = `
                <div class="purchase-item mb-3 p-3 border rounded">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">Matière première</label>
                            <select class="form-select" name="materialId" onchange="testItemSelection(this)">
                                <option value="">Sélectionner</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Quantité</label>
                            <input type="number" class="form-control" name="quantity" value="1">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Prix unitaire (DH)</label>
                            <input type="number" class="form-control" name="unitPrice" readonly>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Total (DH)</label>
                            <input type="number" class="form-control" name="total" readonly>
                        </div>
                    </div>
                </div>
            `;
            
            container.insertAdjacentHTML('beforeend', itemHtml);
            
            // Remplir le select
            const newItem = container.lastElementChild;
            const materialSelect = newItem.querySelector('[name="materialId"]');
            purchaseModule.populateMaterialSelect(materialSelect);
            
            updateResults(`✅ Article ajouté avec ${purchaseModule.rawMaterials.length} options disponibles`);
        }

        function testItemSelection(selectElement) {
            const selectedOption = selectElement.selectedOptions[0];
            const purchaseItem = selectElement.closest('.purchase-item');
            const priceInput = purchaseItem.querySelector('[name="unitPrice"]');
            const quantityInput = purchaseItem.querySelector('[name="quantity"]');
            const totalInput = purchaseItem.querySelector('[name="total"]');
            
            if (selectedOption && selectedOption.dataset.unitPrice) {
                const unitPrice = parseFloat(selectedOption.dataset.unitPrice);
                priceInput.value = unitPrice.toFixed(2);
                
                const quantity = parseFloat(quantityInput.value) || 0;
                const total = quantity * unitPrice;
                totalInput.value = total.toFixed(2);
                
                updateResults(`✅ Article sélectionné: ${selectedOption.textContent}`);
            }
        }

        function updateResults(message) {
            const results = document.getElementById('results');
            results.innerHTML += `<p class="mb-1">${message}</p>`;
            results.scrollTop = results.scrollHeight;
        }
    </script>
</body>
</html>
