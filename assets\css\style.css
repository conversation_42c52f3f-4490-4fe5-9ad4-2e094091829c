/* RestoManager - <PERSON> principaux */

:root {
  /* Couleurs principales */
  --primary-color: #2c3e50;
  --secondary-color: #3498db;
  --success-color: #27ae60;
  --warning-color: #f39c12;
  --danger-color: #e74c3c;
  --info-color: #17a2b8;
  --light-color: #ecf0f1;
  --dark-color: #2c3e50;
  
  /* Couleurs Neumorphic */
  --bg-color: #e0e5ec;
  --shadow-light: #ffffff;
  --shadow-dark: #a3b1c6;
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  
  /* Espacements */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  
  /* Bordures */
  --border-radius: 12px;
  --border-radius-sm: 8px;
  --border-radius-lg: 20px;
  
  /* Transitions */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* Reset et base */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--bg-color);
  color: var(--text-primary);
  line-height: 1.6;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

/* Loading Screen */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--bg-color) 0%, #f8f9fa 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  transition: opacity var(--transition-slow), visibility var(--transition-slow);
}

.loading-screen.hidden {
  opacity: 0;
  visibility: hidden;
}

.loading-content {
  text-align: center;
  animation: fadeInUp 1s ease-out;
}

.loading-logo {
  width: 80px;
  height: 80px;
  margin: 0 auto var(--spacing-lg);
  background: var(--bg-color);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 
    8px 8px 16px var(--shadow-dark),
    -8px -8px 16px var(--shadow-light);
  animation: pulse 2s infinite;
}

.loading-logo i {
  font-size: 2rem;
  color: var(--primary-color);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  margin: var(--spacing-lg) auto;
  border: 4px solid var(--light-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Navigation */
.navbar {
  background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%) !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.nav-link {
  font-weight: 500;
  transition: all var(--transition-fast);
  border-radius: var(--border-radius-sm);
  margin: 0 var(--spacing-xs);
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.dropdown-menu {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dropdown-item {
  transition: all var(--transition-fast);
  border-radius: var(--border-radius-sm);
  margin: var(--spacing-xs);
}

.dropdown-item:hover {
  background-color: var(--light-color);
  transform: translateX(4px);
}

/* Main Content */
.main-content {
  margin-top: 76px;
  padding: var(--spacing-lg);
  min-height: calc(100vh - 76px);
}

/* Cards et conteneurs */
.card {
  background: var(--bg-color);
  border: none;
  border-radius: var(--border-radius);
  box-shadow: 
    8px 8px 16px var(--shadow-dark),
    -8px -8px 16px var(--shadow-light);
  transition: all var(--transition-normal);
  overflow: hidden;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 
    12px 12px 24px var(--shadow-dark),
    -12px -12px 24px var(--shadow-light);
}

.card-header {
  background: linear-gradient(135deg, var(--light-color) 0%, var(--bg-color) 100%);
  border-bottom: 1px solid rgba(163, 177, 198, 0.2);
  font-weight: 600;
  color: var(--text-primary);
}

.card-body {
  padding: var(--spacing-lg);
}

/* Boutons */
.btn {
  border-radius: var(--border-radius);
  font-weight: 500;
  padding: var(--spacing-sm) var(--spacing-lg);
  transition: all var(--transition-fast);
  border: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-normal);
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
  box-shadow: 
    4px 4px 8px var(--shadow-dark),
    -4px -4px 8px var(--shadow-light);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 
    6px 6px 12px var(--shadow-dark),
    -6px -6px 12px var(--shadow-light);
}

.btn-success {
  background: linear-gradient(135deg, var(--success-color) 0%, #2ecc71 100%);
}

.btn-warning {
  background: linear-gradient(135deg, var(--warning-color) 0%, #e67e22 100%);
}

.btn-danger {
  background: linear-gradient(135deg, var(--danger-color) 0%, #c0392b 100%);
}

.btn-info {
  background: linear-gradient(135deg, var(--info-color) 0%, #3498db 100%);
}

/* Formulaires */
.form-control {
  background: var(--bg-color);
  border: none;
  border-radius: var(--border-radius);
  box-shadow: 
    inset 4px 4px 8px var(--shadow-dark),
    inset -4px -4px 8px var(--shadow-light);
  transition: all var(--transition-fast);
  padding: var(--spacing-sm) var(--spacing-md);
}

.form-control:focus {
  box-shadow: 
    inset 6px 6px 12px var(--shadow-dark),
    inset -6px -6px 12px var(--shadow-light),
    0 0 0 0.2rem rgba(52, 144, 220, 0.25);
  background: var(--bg-color);
  border-color: transparent;
}

.form-select {
  background: var(--bg-color);
  border: none;
  border-radius: var(--border-radius);
  box-shadow: 
    inset 4px 4px 8px var(--shadow-dark),
    inset -4px -4px 8px var(--shadow-light);
}

/* Tables */
.table {
  background: var(--bg-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: 
    8px 8px 16px var(--shadow-dark),
    -8px -8px 16px var(--shadow-light);
}

.table thead th {
  background: linear-gradient(135deg, var(--light-color) 0%, var(--bg-color) 100%);
  border-bottom: 2px solid rgba(163, 177, 198, 0.2);
  font-weight: 600;
  color: var(--text-primary);
}

.table tbody tr {
  transition: all var(--transition-fast);
}

.table tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.01);
}

/* Badges */
.badge {
  border-radius: var(--border-radius-sm);
  font-weight: 500;
  padding: var(--spacing-xs) var(--spacing-sm);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
  .main-content {
    padding: var(--spacing-md);
  }
  
  .card-body {
    padding: var(--spacing-md);
  }
  
  .btn {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.9rem;
  }
}

/* Utilitaires */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.fade-in {
  animation: fadeInUp 0.6s ease-out;
}

.hover-lift {
  transition: transform var(--transition-fast);
}

.hover-lift:hover {
  transform: translateY(-2px);
}
