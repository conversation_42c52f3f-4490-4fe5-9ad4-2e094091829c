<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Créer Base Complète Matières Premières</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Créer Base Complète de Matières Premières</h1>
        
        <div class="alert alert-info">
            <p>Cette page va créer une base de données complète avec toutes les matières premières typiques d'un restaurant.</p>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Actions</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary mb-2" onclick="createFullDatabase()">Créer Base Complète</button>
                        <button class="btn btn-success mb-2" onclick="addCategories()">Ajouter Catégories</button>
                        <button class="btn btn-info mb-2" onclick="addSuppliers()">Ajouter Fournisseurs</button>
                        <button class="btn btn-warning mb-2" onclick="clearAll()">Tout Vider</button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Progression</h5>
                    </div>
                    <div class="card-body">
                        <div id="progress">
                            <p class="text-muted">Prêt à créer la base de données</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Résumé Final</h5>
                    </div>
                    <div class="card-body">
                        <div id="summary">
                            <p class="text-muted">Aucune donnée créée</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/core/storage.js"></script>
    <script src="assets/js/core/utils.js"></script>

    <script>
        window.addEventListener('load', () => {
            storage.init();
            updateProgress('✅ Storage initialisé');
        });

        function createFullDatabase() {
            updateProgress('🚀 Début de la création de la base complète...');
            
            // Vider les données existantes
            storage.set('rawMaterials', []);
            storage.set('inventory', []);
            storage.set('materialCategories', []);
            storage.set('suppliers', []);
            
            // Créer les catégories
            addCategories();
            
            // Créer les fournisseurs
            addSuppliers();
            
            // Créer toutes les matières premières
            createAllMaterials();
            
            // Résumé final
            showFinalSummary();
        }

        function addCategories() {
            const categories = [
                { name: 'Légumes', color: '#28a745', description: 'Légumes frais et conserves' },
                { name: 'Fruits', color: '#ffc107', description: 'Fruits frais et secs' },
                { name: 'Viandes', color: '#dc3545', description: 'Viandes fraîches et charcuterie' },
                { name: 'Poissons', color: '#17a2b8', description: 'Poissons et fruits de mer' },
                { name: 'Produits laitiers', color: '#6f42c1', description: 'Lait, fromages, yaourts' },
                { name: 'Épicerie', color: '#fd7e14', description: 'Produits secs et conserves' },
                { name: 'Boissons', color: '#20c997', description: 'Boissons et liquides' },
                { name: 'Condiments', color: '#6c757d', description: 'Épices, sauces, condiments' },
                { name: 'Boulangerie', color: '#e83e8c', description: 'Pain, viennoiseries, pâtisserie' },
                { name: 'Surgelés', color: '#007bff', description: 'Produits surgelés' }
            ];

            categories.forEach(category => {
                storage.add('materialCategories', {
                    ...category,
                    createdAt: new Date().toISOString()
                });
            });

            updateProgress(`✅ ${categories.length} catégories créées`);
        }

        function addSuppliers() {
            const suppliers = [
                { name: 'SALIPRO', type: 'Alimentation générale', phone: '0522-123456', email: '<EMAIL>' },
                { name: 'Marché Central', type: 'Légumes et fruits', phone: '0522-234567', email: '<EMAIL>' },
                { name: 'Boucherie Moderne', type: 'Viandes', phone: '0522-345678', email: '<EMAIL>' },
                { name: 'Poissonnerie Atlantique', type: 'Poissons', phone: '0522-456789', email: '<EMAIL>' },
                { name: 'Laiterie du Nord', type: 'Produits laitiers', phone: '0522-567890', email: '<EMAIL>' }
            ];

            suppliers.forEach(supplier => {
                storage.add('suppliers', {
                    ...supplier,
                    address: 'Casablanca, Maroc',
                    status: 'active',
                    deliveryTime: Math.floor(Math.random() * 3) + 1,
                    createdAt: new Date().toISOString()
                });
            });

            updateProgress(`✅ ${suppliers.length} fournisseurs créés`);
        }

        function createAllMaterials() {
            const categories = storage.get('materialCategories') || [];
            
            const allMaterials = [
                // Légumes (catégorie 1)
                { name: 'Ail', unit: 'kilogramme', categoryName: 'Légumes', price: 25.50 },
                { name: 'Aubergine', unit: 'kilogramme', categoryName: 'Légumes', price: 8.75 },
                { name: 'Betterave', unit: 'kilogramme', categoryName: 'Légumes', price: 6.25 },
                { name: 'Tomates', unit: 'kilogramme', categoryName: 'Légumes', price: 12.50 },
                { name: 'Oignons', unit: 'kilogramme', categoryName: 'Légumes', price: 8.00 },
                { name: 'Carottes', unit: 'kilogramme', categoryName: 'Légumes', price: 6.50 },
                { name: 'Pommes de terre', unit: 'kilogramme', categoryName: 'Légumes', price: 5.00 },
                { name: 'Courgettes', unit: 'kilogramme', categoryName: 'Légumes', price: 9.00 },
                { name: 'Poivrons', unit: 'kilogramme', categoryName: 'Légumes', price: 15.00 },
                { name: 'Concombres', unit: 'kilogramme', categoryName: 'Légumes', price: 7.50 },
                { name: 'Salade verte', unit: 'pièce', categoryName: 'Légumes', price: 3.00 },
                { name: 'Épinards', unit: 'kilogramme', categoryName: 'Légumes', price: 12.00 },
                { name: 'Brocolis', unit: 'kilogramme', categoryName: 'Légumes', price: 18.00 },
                { name: 'Chou-fleur', unit: 'pièce', categoryName: 'Légumes', price: 8.00 },
                { name: 'Radis', unit: 'botte', categoryName: 'Légumes', price: 4.00 },
                
                // Fruits (catégorie 2)
                { name: 'Pommes', unit: 'kilogramme', categoryName: 'Fruits', price: 12.00 },
                { name: 'Bananes', unit: 'kilogramme', categoryName: 'Fruits', price: 8.50 },
                { name: 'Oranges', unit: 'kilogramme', categoryName: 'Fruits', price: 10.00 },
                { name: 'Citrons', unit: 'kilogramme', categoryName: 'Fruits', price: 15.00 },
                { name: 'Fraises', unit: 'kilogramme', categoryName: 'Fruits', price: 25.00 },
                { name: 'Raisins', unit: 'kilogramme', categoryName: 'Fruits', price: 20.00 },
                { name: 'Pêches', unit: 'kilogramme', categoryName: 'Fruits', price: 18.00 },
                { name: 'Abricots', unit: 'kilogramme', categoryName: 'Fruits', price: 22.00 },
                { name: 'Melons', unit: 'pièce', categoryName: 'Fruits', price: 15.00 },
                { name: 'Pastèques', unit: 'pièce', categoryName: 'Fruits', price: 25.00 },
                
                // Viandes (catégorie 3)
                { name: 'Poulet entier', unit: 'kilogramme', categoryName: 'Viandes', price: 45.00 },
                { name: 'Escalope de poulet', unit: 'kilogramme', categoryName: 'Viandes', price: 55.00 },
                { name: 'Cuisses de poulet', unit: 'kilogramme', categoryName: 'Viandes', price: 35.00 },
                { name: 'Bœuf haché', unit: 'kilogramme', categoryName: 'Viandes', price: 65.00 },
                { name: 'Côte de bœuf', unit: 'kilogramme', categoryName: 'Viandes', price: 85.00 },
                { name: 'Agneau', unit: 'kilogramme', categoryName: 'Viandes', price: 95.00 },
                { name: 'Merguez', unit: 'kilogramme', categoryName: 'Viandes', price: 55.00 },
                { name: 'Kefta', unit: 'kilogramme', categoryName: 'Viandes', price: 60.00 },
                
                // Poissons (catégorie 4)
                { name: 'Saumon', unit: 'kilogramme', categoryName: 'Poissons', price: 120.00 },
                { name: 'Dorade', unit: 'kilogramme', categoryName: 'Poissons', price: 80.00 },
                { name: 'Sole', unit: 'kilogramme', categoryName: 'Poissons', price: 100.00 },
                { name: 'Sardines', unit: 'kilogramme', categoryName: 'Poissons', price: 25.00 },
                { name: 'Crevettes', unit: 'kilogramme', categoryName: 'Poissons', price: 150.00 },
                { name: 'Calamars', unit: 'kilogramme', categoryName: 'Poissons', price: 85.00 },
                
                // Produits laitiers (catégorie 5)
                { name: 'Lait', unit: 'litre', categoryName: 'Produits laitiers', price: 6.50 },
                { name: 'Beurre', unit: 'kilogramme', categoryName: 'Produits laitiers', price: 45.00 },
                { name: 'Fromage blanc', unit: 'kilogramme', categoryName: 'Produits laitiers', price: 25.00 },
                { name: 'Mozzarella', unit: 'kilogramme', categoryName: 'Produits laitiers', price: 85.00 },
                { name: 'Parmesan', unit: 'kilogramme', categoryName: 'Produits laitiers', price: 180.00 },
                { name: 'Crème fraîche', unit: 'litre', categoryName: 'Produits laitiers', price: 35.00 },
                { name: 'Yaourt nature', unit: 'kilogramme', categoryName: 'Produits laitiers', price: 15.00 },
                
                // Épicerie (catégorie 6)
                { name: 'Riz basmati', unit: 'kilogramme', categoryName: 'Épicerie', price: 18.00 },
                { name: 'Pâtes', unit: 'kilogramme', categoryName: 'Épicerie', price: 12.00 },
                { name: 'Farine', unit: 'kilogramme', categoryName: 'Épicerie', price: 8.00 },
                { name: 'Sucre', unit: 'kilogramme', categoryName: 'Épicerie', price: 7.50 },
                { name: 'Sel', unit: 'kilogramme', categoryName: 'Épicerie', price: 3.00 },
                { name: 'Huile d\'olive', unit: 'litre', categoryName: 'Épicerie', price: 65.00 },
                { name: 'Huile de tournesol', unit: 'litre', categoryName: 'Épicerie', price: 25.00 },
                { name: 'Vinaigre', unit: 'litre', categoryName: 'Épicerie', price: 15.00 },
                
                // Condiments (catégorie 8)
                { name: 'Persil', unit: 'botte', categoryName: 'Condiments', price: 2.50 },
                { name: 'Coriandre', unit: 'botte', categoryName: 'Condiments', price: 3.00 },
                { name: 'Menthe', unit: 'botte', categoryName: 'Condiments', price: 4.00 },
                { name: 'Basilic', unit: 'botte', categoryName: 'Condiments', price: 5.00 },
                { name: 'Cumin', unit: 'kilogramme', categoryName: 'Condiments', price: 45.00 },
                { name: 'Paprika', unit: 'kilogramme', categoryName: 'Condiments', price: 35.00 },
                { name: 'Poivre noir', unit: 'kilogramme', categoryName: 'Condiments', price: 85.00 },
                { name: 'Cannelle', unit: 'kilogramme', categoryName: 'Condiments', price: 120.00 }
            ];

            let created = 0;
            allMaterials.forEach(material => {
                // Trouver la catégorie
                const category = categories.find(c => c.name === material.categoryName);
                const categoryId = category ? category.id : 1;

                // Créer la matière première
                const newMaterial = storage.add('rawMaterials', {
                    name: material.name,
                    unit: material.unit,
                    categoryId: categoryId,
                    description: `${material.name} - ${material.categoryName}`,
                    createdAt: new Date().toISOString()
                });

                // Créer l'entrée d'inventaire
                const quantity = Math.floor(Math.random() * 100) + 20;
                storage.add('inventory', {
                    materialId: newMaterial.id,
                    quantity: quantity,
                    averagePrice: material.price,
                    unitPrice: material.price,
                    totalValue: quantity * material.price,
                    minQuantity: Math.floor(Math.random() * 20) + 5,
                    lastPurchaseDate: new Date().toISOString(),
                    lastUpdated: new Date().toISOString()
                });

                created++;
            });

            updateProgress(`✅ ${created} matières premières créées avec inventaire`);
        }

        function showFinalSummary() {
            const rawMaterials = storage.get('rawMaterials') || [];
            const inventory = storage.get('inventory') || [];
            const categories = storage.get('materialCategories') || [];
            const suppliers = storage.get('suppliers') || [];

            let html = `
                <div class="row">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h3>${rawMaterials.length}</h3>
                                <small>Matières Premières</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h3>${inventory.length}</h3>
                                <small>Articles en Stock</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h3>${categories.length}</h3>
                                <small>Catégories</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h3>${suppliers.length}</h3>
                                <small>Fournisseurs</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-success mt-3">
                    <h5>🎉 Base de données créée avec succès !</h5>
                    <p>Vous pouvez maintenant utiliser l'application avec une base complète de matières premières.</p>
                    <button class="btn btn-success" onclick="window.open('/', '_blank')">
                        <i class="fas fa-external-link-alt me-2"></i>Ouvrir l'Application
                    </button>
                </div>
            `;

            document.getElementById('summary').innerHTML = html;
            updateProgress('🎉 Base de données complète créée !');
        }

        function clearAll() {
            if (confirm('Êtes-vous sûr de vouloir supprimer toutes les données ?')) {
                storage.set('rawMaterials', []);
                storage.set('inventory', []);
                storage.set('materialCategories', []);
                storage.set('suppliers', []);
                storage.set('purchases', []);
                
                updateProgress('🗑️ Toutes les données supprimées');
                document.getElementById('summary').innerHTML = '<p class="text-muted">Aucune donnée</p>';
            }
        }

        function updateProgress(message) {
            const progress = document.getElementById('progress');
            progress.innerHTML += `<p class="mb-1">${message}</p>`;
            progress.scrollTop = progress.scrollHeight;
        }
    </script>
</body>
</html>
