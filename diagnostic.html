<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnostic RestoManager</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
        .step { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Diagnostic RestoManager</h1>
    <div id="diagnostic-output"></div>

    <script>
        const output = document.getElementById('diagnostic-output');
        
        function log(message, type = 'info', step = '') {
            const div = document.createElement('div');
            div.className = `step ${type}`;
            div.innerHTML = `<strong>${step}</strong> ${message}`;
            output.appendChild(div);
        }

        function testScript(src, name) {
            return new Promise((resolve) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = () => {
                    log(`✅ ${name} chargé avec succès`, 'success', `[${name}]`);
                    resolve(true);
                };
                script.onerror = () => {
                    log(`❌ Erreur de chargement de ${name}`, 'error', `[${name}]`);
                    resolve(false);
                };
                document.head.appendChild(script);
            });
        }

        async function runDiagnostic() {
            log('Début du diagnostic...', 'info', '[INIT]');

            // Test 1: Chargement des dépendances
            log('Test des dépendances externes...', 'info', '[DEPS]');
            
            // Bootstrap CSS
            const bootstrapCSS = document.createElement('link');
            bootstrapCSS.rel = 'stylesheet';
            bootstrapCSS.href = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css';
            document.head.appendChild(bootstrapCSS);
            
            // Font Awesome
            const fontAwesome = document.createElement('link');
            fontAwesome.rel = 'stylesheet';
            fontAwesome.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css';
            document.head.appendChild(fontAwesome);

            // Test 2: Chargement des scripts core
            log('Test des scripts core...', 'info', '[CORE]');
            
            const coreScripts = [
                { src: 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js', name: 'Bootstrap JS' },
                { src: 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js', name: 'SheetJS' },
                { src: 'assets/js/core/storage.js', name: 'Storage' },
                { src: 'assets/js/core/utils.js', name: 'Utils' }
            ];

            for (const script of coreScripts) {
                await testScript(script.src, script.name);
                await new Promise(resolve => setTimeout(resolve, 100)); // Petit délai
            }

            // Test 3: Vérification des objets globaux
            log('Vérification des objets globaux...', 'info', '[GLOBALS]');
            
            setTimeout(() => {
                if (typeof storage !== 'undefined') {
                    log('✅ Storage disponible', 'success', '[STORAGE]');
                } else {
                    log('❌ Storage non disponible', 'error', '[STORAGE]');
                }

                if (typeof Utils !== 'undefined') {
                    log('✅ Utils disponible', 'success', '[UTILS]');
                } else {
                    log('❌ Utils non disponible', 'error', '[UTILS]');
                }

                if (typeof bootstrap !== 'undefined') {
                    log('✅ Bootstrap JS disponible', 'success', '[BOOTSTRAP]');
                } else {
                    log('❌ Bootstrap JS non disponible', 'error', '[BOOTSTRAP]');
                }

                if (typeof XLSX !== 'undefined') {
                    log('✅ SheetJS disponible', 'success', '[XLSX]');
                } else {
                    log('❌ SheetJS non disponible', 'error', '[XLSX]');
                }

                // Test 4: Chargement des modules
                testModules();
            }, 1000);
        }

        async function testModules() {
            log('Test des modules...', 'info', '[MODULES]');
            
            const modules = [
                { src: 'assets/js/modules/dashboard.js', name: 'Dashboard', className: 'DashboardModule' },
                { src: 'assets/js/modules/suppliers.js', name: 'Suppliers', className: 'SuppliersModule' },
                { src: 'assets/js/modules/purchases.js', name: 'Purchases', className: 'PurchasesModule' },
                { src: 'assets/js/modules/inventory.js', name: 'Inventory', className: 'InventoryModule' },
                { src: 'assets/js/modules/daily-outputs.js', name: 'DailyOutputs', className: 'DailyOutputsModule' }
            ];

            for (const module of modules) {
                await testScript(module.src, module.name);
                
                // Vérifier si la classe est disponible
                setTimeout(() => {
                    if (typeof window[module.className] !== 'undefined') {
                        log(`✅ Classe ${module.className} disponible`, 'success', `[${module.name}]`);
                        
                        // Test d'instanciation
                        try {
                            const instance = new window[module.className]();
                            log(`✅ Instance ${module.className} créée`, 'success', `[${module.name}]`);
                        } catch (error) {
                            log(`❌ Erreur d'instanciation ${module.className}: ${error.message}`, 'error', `[${module.name}]`);
                        }
                    } else {
                        log(`❌ Classe ${module.className} non disponible`, 'error', `[${module.name}]`);
                    }
                }, 200);
            }

            // Test 5: Application principale
            setTimeout(() => {
                testApp();
            }, 2000);
        }

        async function testApp() {
            log('Test de l\'application principale...', 'info', '[APP]');
            
            await testScript('assets/js/core/app.js', 'App');
            
            setTimeout(() => {
                if (typeof RestoManagerApp !== 'undefined') {
                    log('✅ RestoManagerApp disponible', 'success', '[APP]');
                    
                    try {
                        const app = new RestoManagerApp();
                        log('✅ Instance RestoManagerApp créée', 'success', '[APP]');
                        
                        // Test d'initialisation
                        app.init().then(() => {
                            log('✅ Application initialisée avec succès', 'success', '[APP]');
                        }).catch(error => {
                            log(`❌ Erreur d'initialisation: ${error.message}`, 'error', '[APP]');
                        });
                        
                    } catch (error) {
                        log(`❌ Erreur de création de l'app: ${error.message}`, 'error', '[APP]');
                    }
                } else {
                    log('❌ RestoManagerApp non disponible', 'error', '[APP]');
                }

                // Test final
                setTimeout(() => {
                    log('Diagnostic terminé. Vérifiez les erreurs ci-dessus.', 'info', '[FIN]');
                }, 1000);
            }, 500);
        }

        // Capturer les erreurs JavaScript
        window.addEventListener('error', (event) => {
            log(`❌ Erreur JavaScript: ${event.message} (${event.filename}:${event.lineno})`, 'error', '[JS ERROR]');
        });

        // Démarrer le diagnostic
        runDiagnostic();
    </script>
</body>
</html>
