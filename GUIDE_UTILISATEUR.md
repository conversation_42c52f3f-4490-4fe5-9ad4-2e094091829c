# Guide Utilisateur - RestoManager 🍽️

## Démarrage Rapide

### 1. Premier Lancement
1. Ouvrez `index.html` dans votre navigateur
2. L'application se charge automatiquement
3. Vous arrivez sur le **Tableau de Bord**

### 2. Navigation
- Utilisez le menu principal en haut pour naviguer entre les modules
- Chaque module a ses propres fonctionnalités
- Les données sont sauvegardées automatiquement dans votre navigateur

## Modules Principaux

### 📦 Stock & Achats

#### **Fournisseurs**
- **Ajouter** : Cliquez sur "Nouveau Fournisseur"
- **Modifier** : Cliquez sur l'icône crayon
- **Informations** : Nom, type, contact, délais de livraison
- **Statuts** : Actif, Inactif, Suspendu

#### **Achats & Matières Premières**
- **Créer matière première** : Définissez nom, catégorie, unité
- **Enregistrer achat** : Sélectionnez fournisseur et articles
- **Calcul automatique** : Prix total et mise à jour du stock

#### **Inventaire**
- **Vue d'ensemble** : Stock actuel avec alertes
- **Ajustements** : Corriger les quantités manuellement
- **Alertes** : Ruptures et stocks faibles automatiques
- **Rapports** : Valeur par catégorie, répartition

#### **Sorties Journalières**
- **Enregistrement manuel** : Sortie article par article
- **Génération automatique** : À partir des plats préparés
- **Consolidation** : Calcul des besoins selon les fiches techniques

### 🍽️ Menu & Plats

#### **Gestion des Plats**
- **Créer plat** : Nom, catégorie, prix, description
- **Statut** : Disponible/Indisponible
- **Informations** : Temps de préparation, allergènes

#### **Fiches Techniques**
- **Ingrédients** : Liste des matières premières nécessaires
- **Quantités** : Portions et quantités par ingrédient
- **Coûts** : Calcul automatique du coût de revient
- **Instructions** : Étapes de préparation

### 👥 Personnel

#### **Gestion du Personnel**
- **Fiches complètes** : Informations personnelles et professionnelles
- **Postes** : Manager, Chef, Cuisinier, Serveur, etc.
- **Statuts** : Actif, Inactif, En congé
- **Salaires** : Taux horaire pour calculs

#### **Planning**
- **Vue hebdomadaire** : Planning par personne et par jour
- **Shifts** : Matin, Après-midi, Nuit, Jour complet
- **Navigation** : Semaine précédente/suivante
- **Modification** : Clic sur une case pour modifier

#### **Pointage**
- **Pointage rapide** : Entrée/Sortie en un clic
- **Historique** : Suivi de tous les pointages
- **Calculs** : Heures travaillées et coûts automatiques
- **Rapports** : Analyse hebdomadaire et mensuelle

### 🧮 Consolidation

#### **Calcul Automatique**
- **Sélection plats** : Choisissez les plats à préparer
- **Quantités** : Définissez le nombre de portions
- **Calcul besoins** : Matières premières nécessaires
- **Vérification stock** : Disponibilité automatique
- **Coûts** : Estimation des coûts de production

## Fonctionnalités Avancées

### 💾 Sauvegarde & Export

#### **Sauvegarde Automatique**
- Toutes les données sont sauvegardées localement
- Aucune connexion internet requise
- Sauvegarde à chaque modification

#### **Export/Import**
- **Export JSON** : Sauvegarde complète des données
- **Export CSV** : Pour chaque module (comptabilité, etc.)
- **Import** : Restauration depuis une sauvegarde

### 🔍 Recherche & Filtres
- **Recherche textuelle** : Dans tous les modules
- **Filtres** : Par catégorie, statut, date
- **Tri** : Personnalisable selon vos besoins

### 📊 Rapports & Statistiques
- **Tableau de bord** : Vue d'ensemble en temps réel
- **Alertes** : Notifications automatiques
- **Tendances** : Évolution des données
- **Analyses** : Coûts, rentabilité, performance

## Workflow Recommandé

### Configuration Initiale
1. **Catégories** : Vérifiez les catégories par défaut
2. **Fournisseurs** : Ajoutez vos fournisseurs
3. **Matières premières** : Créez votre catalogue
4. **Personnel** : Enregistrez votre équipe
5. **Plats** : Créez votre menu
6. **Fiches techniques** : Définissez les recettes

### Utilisation Quotidienne
1. **Matin** : Vérifiez les alertes de stock
2. **Réceptions** : Enregistrez les livraisons
3. **Production** : Notez les sorties ou utilisez la consolidation
4. **Personnel** : Gérez les pointages
5. **Soir** : Vérifiez les statistiques du jour

### Gestion Hebdomadaire
1. **Planning** : Créez les plannings de la semaine
2. **Inventaire** : Vérifiez et ajustez si nécessaire
3. **Commandes** : Analysez les besoins pour la semaine suivante
4. **Rapports** : Consultez les analyses de performance

## Conseils & Bonnes Pratiques

### 📋 Organisation
- **Nommage cohérent** : Utilisez des noms clairs
- **Catégorisation** : Organisez vos données
- **Mise à jour régulière** : Maintenez les informations à jour

### 🔒 Sécurité des Données
- **Sauvegardes régulières** : Exportez vos données
- **Plusieurs copies** : Gardez plusieurs sauvegardes
- **Test de restauration** : Vérifiez vos sauvegardes

### ⚡ Performance
- **Nettoyage périodique** : Supprimez les anciennes données
- **Navigation efficace** : Utilisez les raccourcis clavier
- **Filtres** : Utilisez les filtres pour les grandes listes

## Dépannage

### Problèmes Courants

#### **L'application ne se charge pas**
- Vérifiez que JavaScript est activé
- Utilisez un navigateur récent (Chrome, Firefox, Safari, Edge)
- Videz le cache du navigateur

#### **Données perdues**
- Vérifiez les sauvegardes automatiques
- Utilisez la fonction d'import pour restaurer
- Les données sont stockées localement dans le navigateur

#### **Performance lente**
- Trop de données stockées : exportez et nettoyez
- Fermez les autres onglets du navigateur
- Redémarrez le navigateur

#### **Erreurs de calcul**
- Vérifiez les fiches techniques
- Contrôlez les prix moyens dans l'inventaire
- Assurez-vous que les unités sont cohérentes

### Support
- Consultez le fichier README.md pour plus d'informations techniques
- Vérifiez la console du navigateur pour les erreurs (F12)
- Exportez vos données avant toute manipulation importante

## Raccourcis Clavier
- **Ctrl+S** : Sauvegarde manuelle
- **Ctrl+E** : Export des données
- **F5** : Actualiser l'application

---

**RestoManager** - Votre solution complète de gestion de restaurant 🍽️

*Version 1.0.0 - Application web progressive*
