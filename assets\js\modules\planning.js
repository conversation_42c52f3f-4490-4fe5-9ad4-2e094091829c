/**
 * RestoManager - Module Planning du Personnel
 * Création et gestion des plannings avec shifts
 */

class PlanningModule {
    constructor() {
        this.schedules = [];
        this.staff = [];
        this.shifts = [];
        this.currentWeek = this.getCurrentWeek();
    }

    async init() {
        this.loadData();
    }

    loadData() {
        this.schedules = storage.get('schedules') || [];
        this.staff = storage.get('staff') || [];
        this.shifts = storage.get('shifts') || [];
    }

    async render() {
        return `
            <div class="planning-container">
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card-neomorphic">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <h2 class="mb-0">
                                            <i class="fas fa-calendar-alt text-primary me-2"></i>
                                            Planning du Personnel
                                        </h2>
                                        <p class="text-muted mb-0">Semaine du <PERSON>{this.getWeekRange()}</p>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <div class="btn-group me-2">
                                            <button class="btn btn-neomorphic btn-outline-primary" onclick="planningModule.previousWeek()">
                                                <i class="fas fa-chevron-left"></i>
                                            </button>
                                            <button class="btn btn-neomorphic btn-outline-primary" onclick="planningModule.currentWeekView()">
                                                Aujourd'hui
                                            </button>
                                            <button class="btn btn-neomorphic btn-outline-primary" onclick="planningModule.nextWeek()">
                                                <i class="fas fa-chevron-right"></i>
                                            </button>
                                        </div>
                                        <button class="btn btn-neomorphic btn-primary" onclick="planningModule.showAddModal()">
                                            <i class="fas fa-plus me-2"></i>Nouveau Planning
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Planning hebdomadaire -->
                <div class="row">
                    <div class="col-12">
                        <div class="card-neomorphic">
                            <div class="card-body">
                                ${this.renderWeeklyPlanning()}
                            </div>
                        </div>
                    </div>
                </div>

                ${this.renderPlanningModal()}
            </div>
        `;
    }

    renderWeeklyPlanning() {
        const days = ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche'];
        const activeStaff = this.staff.filter(s => s.status === 'active');

        if (activeStaff.length === 0) {
            return `
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Aucun personnel actif</h5>
                    <p class="text-muted">Ajoutez du personnel pour créer des plannings</p>
                </div>
            `;
        }

        return `
            <div class="table-responsive">
                <table class="table table-bordered planning-table">
                    <thead>
                        <tr>
                            <th style="width: 150px;">Personnel</th>
                            ${days.map(day => `<th class="text-center">${day}</th>`).join('')}
                        </tr>
                    </thead>
                    <tbody>
                        ${activeStaff.map(member => this.renderStaffPlanningRow(member, days)).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    renderStaffPlanningRow(member, days) {
        return `
            <tr>
                <td>
                    <div class="fw-bold">${member.firstName} ${member.lastName}</div>
                    <small class="text-muted">${this.getPositionName(member.position)}</small>
                </td>
                ${days.map((day, index) => {
                    const daySchedule = this.getDaySchedule(member.id, index);
                    return `<td class="text-center planning-cell" onclick="planningModule.editDaySchedule('${member.id}', ${index})">
                        ${daySchedule ? this.renderScheduleCell(daySchedule) : '<span class="text-muted">Repos</span>'}
                    </td>`;
                }).join('')}
            </tr>
        `;
    }

    renderScheduleCell(schedule) {
        const shift = this.shifts.find(s => s.id === schedule.shiftId);
        if (!shift) return '<span class="text-muted">-</span>';

        return `
            <div class="schedule-cell" style="background-color: ${shift.color}20; border-left: 3px solid ${shift.color};">
                <div class="fw-bold">${shift.name}</div>
                <small>${shift.startTime} - ${shift.endTime}</small>
            </div>
        `;
    }

    renderPlanningModal() {
        return `
            <div class="modal fade" id="planningModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content modal-neomorphic">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-calendar-plus me-2"></i>
                                Planning
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="planningForm">
                                <div class="mb-3">
                                    <label class="form-label">Personnel *</label>
                                    <select class="form-select" id="planningStaff" required>
                                        <option value="">Sélectionner un membre</option>
                                        ${this.staff.filter(s => s.status === 'active').map(s => `
                                            <option value="${s.id}">${s.firstName} ${s.lastName}</option>
                                        `).join('')}
                                    </select>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Date *</label>
                                            <input type="date" class="form-control" id="planningDate" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Shift *</label>
                                            <select class="form-select" id="planningShift" required>
                                                <option value="">Sélectionner un shift</option>
                                                ${this.shifts.map(s => `
                                                    <option value="${s.id}">${s.name} (${s.startTime} - ${s.endTime})</option>
                                                `).join('')}
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Notes</label>
                                    <textarea class="form-control" id="planningNotes" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="button" class="btn btn-primary" onclick="planningModule.savePlanning()">
                                <i class="fas fa-save me-2"></i>Enregistrer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    postRender() {
        window.planningModule = this;
        this.addPlanningStyles();
    }

    addPlanningStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .planning-table th, .planning-table td {
                vertical-align: middle;
                padding: 0.75rem 0.5rem;
            }
            
            .planning-cell {
                cursor: pointer;
                transition: background-color 0.2s;
                min-height: 60px;
            }
            
            .planning-cell:hover {
                background-color: rgba(0, 123, 255, 0.1);
            }
            
            .schedule-cell {
                padding: 0.5rem;
                border-radius: 4px;
                margin: 2px;
            }
        `;
        document.head.appendChild(style);
    }

    // Méthodes utilitaires
    getCurrentWeek() {
        const now = new Date();
        const monday = new Date(now);
        monday.setDate(now.getDate() - now.getDay() + 1);
        return monday;
    }

    getWeekRange() {
        const monday = new Date(this.currentWeek);
        const sunday = new Date(monday);
        sunday.setDate(monday.getDate() + 6);
        
        return `${Utils.formatDate(monday)} - ${Utils.formatDate(sunday)}`;
    }

    getDaySchedule(staffId, dayIndex) {
        const targetDate = new Date(this.currentWeek);
        targetDate.setDate(this.currentWeek.getDate() + dayIndex);
        const dateStr = targetDate.toISOString().split('T')[0];
        
        return this.schedules.find(s => 
            s.staffId === staffId && 
            s.date === dateStr
        );
    }

    getPositionName(position) {
        const names = {
            manager: 'Manager',
            chef: 'Chef',
            cook: 'Cuisinier',
            server: 'Serveur',
            cleaner: 'Agent d\'entretien',
            other: 'Autre'
        };
        return names[position] || 'Autre';
    }

    // Actions
    previousWeek() {
        this.currentWeek.setDate(this.currentWeek.getDate() - 7);
        this.refreshPlanning();
    }

    nextWeek() {
        this.currentWeek.setDate(this.currentWeek.getDate() + 7);
        this.refreshPlanning();
    }

    currentWeekView() {
        this.currentWeek = this.getCurrentWeek();
        this.refreshPlanning();
    }

    showAddModal() {
        this.resetForm();
        new bootstrap.Modal(document.getElementById('planningModal')).show();
    }

    editDaySchedule(staffId, dayIndex) {
        const targetDate = new Date(this.currentWeek);
        targetDate.setDate(this.currentWeek.getDate() + dayIndex);
        
        document.getElementById('planningStaff').value = staffId;
        document.getElementById('planningDate').value = targetDate.toISOString().split('T')[0];
        
        const existingSchedule = this.getDaySchedule(staffId, dayIndex);
        if (existingSchedule) {
            document.getElementById('planningShift').value = existingSchedule.shiftId;
            document.getElementById('planningNotes').value = existingSchedule.notes || '';
        }
        
        new bootstrap.Modal(document.getElementById('planningModal')).show();
    }

    async savePlanning() {
        const formData = this.getPlanningFormData();
        
        if (!this.validatePlanningForm(formData)) {
            return;
        }

        try {
            // Vérifier s'il existe déjà un planning pour cette date/personne
            const existing = this.schedules.find(s => 
                s.staffId === formData.staffId && 
                s.date === formData.date
            );

            if (existing) {
                storage.update('schedules', existing.id, formData);
                Utils.showToast('Planning mis à jour', 'success');
            } else {
                storage.add('schedules', formData);
                Utils.showToast('Planning créé avec succès', 'success');
            }

            this.loadData();
            this.refreshPlanning();
            bootstrap.Modal.getInstance(document.getElementById('planningModal')).hide();

        } catch (error) {
            console.error('Erreur lors de la sauvegarde:', error);
            Utils.showToast('Erreur lors de la sauvegarde', 'error');
        }
    }

    getPlanningFormData() {
        return {
            staffId: document.getElementById('planningStaff').value,
            date: document.getElementById('planningDate').value,
            shiftId: parseInt(document.getElementById('planningShift').value),
            notes: document.getElementById('planningNotes').value.trim()
        };
    }

    validatePlanningForm(data) {
        if (!data.staffId || !data.date || !data.shiftId) {
            Utils.showToast('Veuillez remplir tous les champs obligatoires', 'warning');
            return false;
        }
        return true;
    }

    resetForm() {
        document.getElementById('planningForm').reset();
    }

    refreshPlanning() {
        const container = document.querySelector('.planning-container .card-neomorphic .card-body');
        if (container) {
            // Mettre à jour la semaine affichée
            const weekRange = container.closest('.planning-container').querySelector('.text-muted');
            if (weekRange) {
                weekRange.textContent = `Semaine du ${this.getWeekRange()}`;
            }
            
            container.innerHTML = this.renderWeeklyPlanning();
        }
    }

    destroy() {
        if (window.planningModule === this) {
            delete window.planningModule;
        }
    }
}

window.PlanningModule = PlanningModule;
