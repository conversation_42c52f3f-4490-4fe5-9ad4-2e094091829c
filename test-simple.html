<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simple - RestoManager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Test Simple RestoManager</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Modules</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary me-2" onclick="testInventory()">Test Inventory</button>
                        <button class="btn btn-success me-2" onclick="testDailyOutputs()">Test Daily Outputs</button>
                        <button class="btn btn-info" onclick="testApp()">Test App</button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Résultats</h5>
                    </div>
                    <div class="card-body">
                        <div id="results"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Contenu Module</h5>
                    </div>
                    <div class="card-body">
                        <div id="moduleContent"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Core JavaScript -->
    <script src="assets/js/core/storage.js"></script>
    <script src="assets/js/core/utils.js"></script>
    
    <!-- Module Scripts -->
    <script src="assets/js/modules/inventory.js"></script>
    <script src="assets/js/modules/daily-outputs.js"></script>
    
    <!-- Application principale -->
    <script src="assets/js/core/app.js"></script>

    <script>
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} py-1 px-2 mb-1`;
            div.innerHTML = message;
            results.appendChild(div);
        }

        function testInventory() {
            log('=== Test Inventory Module ===');
            
            try {
                if (window.InventoryModule) {
                    log('✅ InventoryModule trouvé', 'success');
                    
                    const inventory = new InventoryModule();
                    log('✅ Instance créée', 'success');
                    
                    // Test init
                    inventory.init().then(() => {
                        log('✅ Init réussi', 'success');
                        
                        // Test render
                        inventory.render().then(content => {
                            log('✅ Render réussi', 'success');
                            document.getElementById('moduleContent').innerHTML = content;
                            
                            // Test postRender
                            if (typeof inventory.postRender === 'function') {
                                inventory.postRender();
                                log('✅ PostRender réussi', 'success');
                            }
                        }).catch(error => {
                            log(`❌ Erreur render: ${error.message}`, 'error');
                        });
                    }).catch(error => {
                        log(`❌ Erreur init: ${error.message}`, 'error');
                    });
                } else {
                    log('❌ InventoryModule non trouvé', 'error');
                }
            } catch (error) {
                log(`❌ Erreur: ${error.message}`, 'error');
            }
        }

        function testDailyOutputs() {
            log('=== Test Daily Outputs Module ===');
            
            try {
                if (window.DailyOutputsModule) {
                    log('✅ DailyOutputsModule trouvé', 'success');
                    
                    const dailyOutputs = new DailyOutputsModule();
                    log('✅ Instance créée', 'success');
                    
                    // Test init
                    dailyOutputs.init().then(() => {
                        log('✅ Init réussi', 'success');
                        
                        // Test render
                        dailyOutputs.render().then(content => {
                            log('✅ Render réussi', 'success');
                            document.getElementById('moduleContent').innerHTML = content;
                            
                            // Test postRender
                            if (typeof dailyOutputs.postRender === 'function') {
                                dailyOutputs.postRender();
                                log('✅ PostRender réussi', 'success');
                            }
                        }).catch(error => {
                            log(`❌ Erreur render: ${error.message}`, 'error');
                        });
                    }).catch(error => {
                        log(`❌ Erreur init: ${error.message}`, 'error');
                    });
                } else {
                    log('❌ DailyOutputsModule non trouvé', 'error');
                }
            } catch (error) {
                log(`❌ Erreur: ${error.message}`, 'error');
            }
        }

        function testApp() {
            log('=== Test Application ===');
            
            try {
                if (window.RestoManagerApp) {
                    log('✅ RestoManagerApp trouvé', 'success');
                    
                    const app = new RestoManagerApp();
                    log('✅ App créée', 'success');
                    
                    // Test des noms de modules
                    const tests = [
                        { input: 'inventory', expected: 'InventoryModule' },
                        { input: 'daily-outputs', expected: 'DailyOutputsModule' }
                    ];
                    
                    tests.forEach(test => {
                        const result = app.getModuleClassName(test.input);
                        if (result === test.expected) {
                            log(`✅ ${test.input} → ${result}`, 'success');
                        } else {
                            log(`❌ ${test.input} → ${result} (attendu: ${test.expected})`, 'error');
                        }
                    });
                    
                    // Test de chargement de module
                    setTimeout(() => {
                        log('Test de chargement du module inventory...');
                        app.loadModule('inventory').then(() => {
                            log('✅ Module inventory chargé', 'success');
                        }).catch(error => {
                            log(`❌ Erreur chargement inventory: ${error.message}`, 'error');
                        });
                    }, 1000);
                    
                } else {
                    log('❌ RestoManagerApp non trouvé', 'error');
                }
            } catch (error) {
                log(`❌ Erreur: ${error.message}`, 'error');
            }
        }

        // Test automatique au chargement
        window.addEventListener('load', () => {
            log('Page chargée, modules disponibles:');
            if (window.InventoryModule) log('✅ InventoryModule', 'success');
            if (window.DailyOutputsModule) log('✅ DailyOutputsModule', 'success');
            if (window.RestoManagerApp) log('✅ RestoManagerApp', 'success');
            if (window.storage) log('✅ Storage', 'success');
            if (window.Utils) log('✅ Utils', 'success');
        });
    </script>
</body>
</html>
