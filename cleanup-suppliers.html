<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nettoyage Fournisseurs</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/css/neomorphic.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <div class="row">
            <div class="col-12">
                <div class="card-neomorphic">
                    <div class="card-body">
                        <h4 class="mb-4">
                            <i class="fas fa-broom me-2"></i>
                            Nettoyage des Fournisseurs Dupliqués
                        </h4>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6>Avant nettoyage</h6>
                                        <div id="beforeStats"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6>Après nettoyage</h6>
                                        <div id="afterStats"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center">
                            <button class="btn btn-primary btn-lg" onclick="cleanupSuppliers()">
                                <i class="fas fa-magic me-2"></i>
                                Nettoyer les Doublons
                            </button>
                        </div>

                        <div class="mt-4">
                            <h6>Détails des fusions</h6>
                            <div id="mergeDetails" class="bg-light p-3 rounded" style="max-height: 400px; overflow-y: auto;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/core/storage.js"></script>
    <script src="assets/js/core/utils.js"></script>

    <script>
        window.addEventListener('load', () => {
            storage.init();
            showBeforeStats();
        });

        function showBeforeStats() {
            const suppliers = storage.get('suppliers') || [];
            const uniqueNames = new Set(suppliers.map(s => s.name.toLowerCase().trim()));
            
            document.getElementById('beforeStats').innerHTML = `
                <p><strong>Total fournisseurs:</strong> ${suppliers.length}</p>
                <p><strong>Noms uniques:</strong> ${uniqueNames.size}</p>
                <p><strong>Doublons:</strong> ${suppliers.length - uniqueNames.size}</p>
            `;
        }

        function cleanupSuppliers() {
            const suppliers = storage.get('suppliers') || [];
            const mergeDetails = document.getElementById('mergeDetails');
            mergeDetails.innerHTML = '<p>Nettoyage en cours...</p>';

            console.log('Début du nettoyage, fournisseurs avant:', suppliers.length);

            const supplierMap = new Map();
            const merges = [];

            suppliers.forEach(supplier => {
                const key = supplier.name.toLowerCase().trim();

                if (supplierMap.has(key)) {
                    // Fournisseur existant - combiner les types
                    const existing = supplierMap.get(key);
                    const existingTypes = existing.type ? existing.type.split(',').map(t => t.trim()) : [];
                    const newTypes = supplier.type ? supplier.type.split(',').map(t => t.trim()) : [];

                    // Combiner et dédupliquer les types
                    const allTypes = [...new Set([...existingTypes, ...newTypes])].filter(t => t);
                    const oldType = existing.type;
                    existing.type = allTypes.join(', ');

                    merges.push({
                        name: supplier.name,
                        oldTypes: [oldType, supplier.type],
                        newType: existing.type
                    });

                    // Garder les informations les plus récentes
                    if (supplier.createdAt > existing.createdAt) {
                        existing.phone = supplier.phone || existing.phone;
                        existing.email = supplier.email || existing.email;
                        existing.address = supplier.address || existing.address;
                        existing.company = supplier.company || existing.company;
                        existing.createdAt = supplier.createdAt;
                    }
                } else {
                    // Nouveau fournisseur
                    supplierMap.set(key, { ...supplier });
                }
            });

            const cleanedSuppliers = Array.from(supplierMap.values());
            
            // Sauvegarder
            storage.set('suppliers', cleanedSuppliers);

            console.log('Nettoyage terminé, fournisseurs après:', cleanedSuppliers.length);

            // Afficher les résultats
            showAfterStats(cleanedSuppliers);
            showMergeDetails(merges);

            Utils.showToast(`Nettoyage terminé ! ${suppliers.length - cleanedSuppliers.length} doublons supprimés.`, 'success');
        }

        function showAfterStats(suppliers) {
            const uniqueNames = new Set(suppliers.map(s => s.name.toLowerCase().trim()));
            
            document.getElementById('afterStats').innerHTML = `
                <p><strong>Total fournisseurs:</strong> ${suppliers.length}</p>
                <p><strong>Noms uniques:</strong> ${uniqueNames.size}</p>
                <p><strong>Doublons:</strong> ${suppliers.length - uniqueNames.size}</p>
            `;
        }

        function showMergeDetails(merges) {
            const detailsDiv = document.getElementById('mergeDetails');
            
            if (merges.length === 0) {
                detailsDiv.innerHTML = '<p class="text-muted">Aucune fusion nécessaire.</p>';
                return;
            }

            let html = '<h6>Fusions effectuées:</h6>';
            merges.forEach(merge => {
                html += `
                    <div class="border-bottom py-2">
                        <strong>${merge.name}</strong><br>
                        <small class="text-muted">Types fusionnés: ${merge.oldTypes.join(' + ')} → ${merge.newType}</small>
                    </div>
                `;
            });

            detailsDiv.innerHTML = html;
        }
    </script>
</body>
</html>
