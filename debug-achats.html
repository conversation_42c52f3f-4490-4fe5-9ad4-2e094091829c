<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Achats - RestoManager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Debug Module Achats</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Actions</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary mb-2" onclick="createDebugData()">Créer Données Debug</button>
                        <button class="btn btn-success mb-2" onclick="testAutoFill()">Tester Auto-remplissage</button>
                        <button class="btn btn-info mb-2" onclick="showData()">Afficher Données</button>
                        <button class="btn btn-warning mb-2" onclick="clearData()">Vider Données</button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Résultats</h5>
                    </div>
                    <div class="card-body">
                        <div id="results">
                            <p class="text-muted">Aucun test effectué</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Auto-remplissage</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <label class="form-label">Matière première</label>
                                <select class="form-select" id="testMaterialSelect" onchange="testMaterialSelection()">
                                    <option value="">Sélectionner</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Quantité</label>
                                <input type="number" class="form-control" id="testQuantity" value="1" onchange="calculateTestTotal()">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Prix unitaire (DH)</label>
                                <input type="number" class="form-control" id="testUnitPrice" readonly>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Total (DH)</label>
                                <input type="number" class="form-control" id="testTotal" readonly>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/core/storage.js"></script>
    <script src="assets/js/core/utils.js"></script>

    <script>
        // Initialiser le storage
        window.addEventListener('load', () => {
            storage.init();
            showData();
            loadMaterialsInSelect();
        });

        function createDebugData() {
            // Nettoyer les données existantes
            storage.set('rawMaterials', []);
            storage.set('inventory', []);
            
            // Créer des matières premières
            const materials = [
                { name: 'Ail', unit: 'kilogramme', categoryId: 1 },
                { name: 'Aubergine', unit: 'kilogramme', categoryId: 1 },
                { name: 'Betterave', unit: 'kilogramme', categoryId: 1 }
            ];
            
            const createdMaterials = [];
            materials.forEach(material => {
                const created = storage.add('rawMaterials', {
                    ...material,
                    description: 'Article de debug',
                    createdAt: new Date().toISOString()
                });
                createdMaterials.push(created);
            });
            
            // Créer l'inventaire avec prix
            const inventoryData = [
                { materialName: 'Ail', quantity: 50, unitPrice: 25.50 },
                { materialName: 'Aubergine', quantity: 30, unitPrice: 8.75 },
                { materialName: 'Betterave', quantity: 40, unitPrice: 6.25 }
            ];
            
            inventoryData.forEach(invItem => {
                const material = createdMaterials.find(m => m.name === invItem.materialName);
                if (material) {
                    storage.add('inventory', {
                        materialId: material.id,
                        quantity: invItem.quantity,
                        unitPrice: invItem.unitPrice,
                        totalValue: invItem.quantity * invItem.unitPrice,
                        lastUpdated: new Date().toISOString()
                    });
                }
            });
            
            updateResults('✅ Données de debug créées');
            showData();
            loadMaterialsInSelect();
        }

        function testAutoFill() {
            const rawMaterials = storage.get('rawMaterials') || [];
            const inventory = storage.get('inventory') || [];
            
            let results = '<h6>Test de récupération des prix :</h6>';
            
            rawMaterials.forEach(material => {
                const inventoryItem = inventory.find(item => item.materialId === material.id);
                results += `<p><strong>${material.name}:</strong> `;
                if (inventoryItem) {
                    results += `${Utils.formatPrice(inventoryItem.unitPrice)} ✅`;
                } else {
                    results += 'Prix non trouvé ❌';
                }
                results += '</p>';
            });
            
            updateResults(results);
        }

        function showData() {
            const rawMaterials = storage.get('rawMaterials') || [];
            const inventory = storage.get('inventory') || [];
            
            let html = `
                <h6>Données actuelles :</h6>
                <p><strong>Matières premières:</strong> ${rawMaterials.length}</p>
                <p><strong>Articles en inventaire:</strong> ${inventory.length}</p>
            `;
            
            if (rawMaterials.length > 0) {
                html += '<h6>Matières premières :</h6><ul>';
                rawMaterials.forEach(material => {
                    const invItem = inventory.find(i => i.materialId === material.id);
                    const price = invItem ? Utils.formatPrice(invItem.unitPrice) : 'Pas de prix';
                    html += `<li>${material.name} (${material.unit}) - ${price}</li>`;
                });
                html += '</ul>';
            }
            
            updateResults(html);
        }

        function clearData() {
            storage.set('rawMaterials', []);
            storage.set('inventory', []);
            updateResults('🗑️ Données supprimées');
            loadMaterialsInSelect();
        }

        function loadMaterialsInSelect() {
            const select = document.getElementById('testMaterialSelect');
            const rawMaterials = storage.get('rawMaterials') || [];
            const inventory = storage.get('inventory') || [];
            
            select.innerHTML = '<option value="">Sélectionner</option>';
            
            rawMaterials.forEach(material => {
                const inventoryItem = inventory.find(item => item.materialId === material.id);
                const unitPrice = inventoryItem ? inventoryItem.unitPrice : 0;
                const priceInfo = unitPrice > 0 ? ` - ${Utils.formatPrice(unitPrice)}` : '';
                
                const option = document.createElement('option');
                option.value = material.id;
                option.dataset.unitPrice = unitPrice;
                option.textContent = `${material.name} (${material.unit})${priceInfo}`;
                select.appendChild(option);
            });
        }

        function testMaterialSelection() {
            const select = document.getElementById('testMaterialSelect');
            const selectedOption = select.selectedOptions[0];
            const priceInput = document.getElementById('testUnitPrice');
            
            if (selectedOption && selectedOption.dataset.unitPrice) {
                const unitPrice = parseFloat(selectedOption.dataset.unitPrice);
                priceInput.value = unitPrice.toFixed(2);
                calculateTestTotal();
                updateResults(`✅ Prix auto-rempli: ${Utils.formatPrice(unitPrice)}`);
            } else {
                priceInput.value = '';
                document.getElementById('testTotal').value = '';
                updateResults('❌ Aucun prix trouvé');
            }
        }

        function calculateTestTotal() {
            const quantity = parseFloat(document.getElementById('testQuantity').value) || 0;
            const unitPrice = parseFloat(document.getElementById('testUnitPrice').value) || 0;
            const total = quantity * unitPrice;
            
            document.getElementById('testTotal').value = total.toFixed(2);
        }

        function updateResults(message) {
            document.getElementById('results').innerHTML = message;
        }
    </script>
</body>
</html>
