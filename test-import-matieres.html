<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Import Matières Premières - RestoManager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Test Import Matières Premières</h1>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>Test de la Fonctionnalité d'Import</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Format requis</h6>
                            <p><strong>Colonnes :</strong> Article, Unité, Prix HT, TVA, Fournisseur, Catégorie</p>
                            <p><strong>Exemple :</strong> Tomates cerises, kg, 3.50, 5.5, Maraîcher Bio, Légumes</p>
                        </div>
                        
                        <div class="mb-3">
                            <button class="btn btn-success" onclick="downloadTemplate()">
                                <i class="fas fa-download me-2"></i>Télécharger Template
                            </button>
                            
                            <button class="btn btn-primary ms-2" onclick="testImport()">
                                <i class="fas fa-upload me-2"></i>Test Import
                            </button>
                            
                            <button class="btn btn-info ms-2" onclick="showData()">
                                <i class="fas fa-eye me-2"></i>Voir Données
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6>Statistiques</h6>
                    </div>
                    <div class="card-body">
                        <div id="stats">
                            <p>Matières premières: <span id="materialsCount">0</span></p>
                            <p>Fournisseurs: <span id="suppliersCount">0</span></p>
                            <p>Catégories: <span id="categoriesCount">0</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6>Données Actuelles</h6>
                    </div>
                    <div class="card-body">
                        <div id="dataContent">
                            <p class="text-muted">Aucune donnée</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="assets/js/core/storage.js"></script>
    <script src="assets/js/core/utils.js"></script>
    <script src="assets/js/modules/purchases.js"></script>

    <script>
        let purchasesModule;

        // Initialiser le module
        window.addEventListener('load', () => {
            purchasesModule = new PurchasesModule();
            purchasesModule.init().then(() => {
                updateStats();
                showData();
            });
        });

        function testImport() {
            if (!purchasesModule) {
                alert('Module non initialisé');
                return;
            }
            
            // Créer et afficher le modal d'import
            const modalHtml = purchasesModule.renderImportMaterialsModal();
            
            // Ajouter le modal au DOM s'il n'existe pas
            if (!document.getElementById('importMaterialsModal')) {
                document.body.insertAdjacentHTML('beforeend', modalHtml);
            }
            
            // Attacher les événements
            purchasesModule.postRender();
            
            // Afficher le modal
            purchasesModule.showImportMaterialsModal();
        }

        function showData() {
            if (!purchasesModule) return;
            
            const rawMaterials = storage.get('rawMaterials') || [];
            const suppliers = storage.get('suppliers') || [];
            const categories = storage.get('materialCategories') || [];
            
            let html = '<div class="row">';
            
            // Matières premières
            html += '<div class="col-md-4"><h6>Matières Premières</h6>';
            if (rawMaterials.length === 0) {
                html += '<p class="text-muted">Aucune matière première</p>';
            } else {
                html += '<ul class="list-group list-group-flush">';
                rawMaterials.slice(0, 10).forEach(material => {
                    const category = categories.find(c => c.id === material.categoryId);
                    html += `<li class="list-group-item d-flex justify-content-between">
                        <span>${material.name}</span>
                        <small class="text-muted">${material.unit} - ${category ? category.name : 'N/A'}</small>
                    </li>`;
                });
                if (rawMaterials.length > 10) {
                    html += `<li class="list-group-item text-muted">... et ${rawMaterials.length - 10} autres</li>`;
                }
                html += '</ul>';
            }
            html += '</div>';
            
            // Fournisseurs
            html += '<div class="col-md-4"><h6>Fournisseurs</h6>';
            if (suppliers.length === 0) {
                html += '<p class="text-muted">Aucun fournisseur</p>';
            } else {
                html += '<ul class="list-group list-group-flush">';
                suppliers.slice(0, 10).forEach(supplier => {
                    html += `<li class="list-group-item d-flex justify-content-between">
                        <span>${supplier.name}</span>
                        <small class="text-muted">${supplier.type}</small>
                    </li>`;
                });
                if (suppliers.length > 10) {
                    html += `<li class="list-group-item text-muted">... et ${suppliers.length - 10} autres</li>`;
                }
                html += '</ul>';
            }
            html += '</div>';
            
            // Catégories
            html += '<div class="col-md-4"><h6>Catégories</h6>';
            html += '<ul class="list-group list-group-flush">';
            categories.forEach(category => {
                html += `<li class="list-group-item d-flex justify-content-between">
                    <span>${category.name}</span>
                    <span class="badge" style="background-color: ${category.color};">${category.id}</span>
                </li>`;
            });
            html += '</ul></div>';
            
            html += '</div>';
            
            document.getElementById('dataContent').innerHTML = html;
        }

        function updateStats() {
            const rawMaterials = storage.get('rawMaterials') || [];
            const suppliers = storage.get('suppliers') || [];
            const categories = storage.get('materialCategories') || [];
            
            document.getElementById('materialsCount').textContent = rawMaterials.length;
            document.getElementById('suppliersCount').textContent = suppliers.length;
            document.getElementById('categoriesCount').textContent = categories.length;
        }

        function downloadTemplate() {
            // Créer le contenu CSV
            const csvContent = `Article,Unité,Prix HT,TVA,Fournisseur,Catégorie
Tomates cerises,kg,3.50,5.5,Maraîcher Bio,Légumes
Filet de bœuf,kg,28.90,5.5,Boucherie Martin,Viandes
Poulet fermier,kg,12.50,5.5,Ferme Dubois,Volailles
Saumon frais,kg,24.80,5.5,Poissonnerie Océan,Poissons
Mozzarella di Bufala,kg,18.90,5.5,Fromagerie Italienne,Produits laitiers
Huile d'olive extra vierge,litre,15.20,20,Épicerie Fine,Épicerie
Pain de campagne,pièce,2.80,5.5,Boulangerie Artisanale,Boulangerie
Tarte aux pommes,pièce,12.50,5.5,Pâtisserie Gourmande,Pâtisserie
Jambon de Bayonne,kg,32.50,5.5,Charcuterie Basque,Charcuterie
Sauce soja,litre,8.90,20,Épicerie Asie,Produits asiatiques
Barquettes plastique,paquet,15.60,20,Emballages Pro,Emballage
Plat cuisiné lasagnes,pièce,8.50,5.5,Traiteur Express,Produits finis
Vin rouge Bordeaux,bouteille,12.80,20,Cave à Vins,Boissons
Produit d'entretien,litre,4.20,20,Fournitures Resto,Achats divers
Lait entier,litre,1.15,5.5,Laiterie Locale,Alimentation générale`;

            // Créer et télécharger le fichier
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', 'template_import_matieres_premieres.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Écouter les changements de stockage pour mettre à jour l'affichage
        window.addEventListener('storage', () => {
            updateStats();
            showData();
        });

        // Simuler l'événement storage pour les changements locaux
        const originalSetItem = localStorage.setItem;
        localStorage.setItem = function(key, value) {
            originalSetItem.apply(this, arguments);
            if (key.startsWith('restomanager_')) {
                setTimeout(() => {
                    updateStats();
                    showData();
                }, 100);
            }
        };
    </script>
</body>
</html>
