<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Corrections Fournisseurs</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Test Corrections Fournisseurs</h1>
        
        <div class="alert alert-info">
            <h6>Tests à effectuer :</h6>
            <ul class="mb-0">
                <li>✅ Déduplication des fournisseurs dans le module fournisseur</li>
                <li>✅ Affichage de tous les types d'un fournisseur (ex: GASTROMIXTE)</li>
                <li>✅ Sélection de type spécifique dans les achats</li>
                <li>✅ Filtrage des matières premières par type sélectionné</li>
            </ul>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Fournisseurs Uniques</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary mb-2" onclick="testSupplierDeduplication()">Tester Déduplication</button>
                        <div id="supplierResults">
                            <p class="text-muted">Cliquer pour tester</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Filtrage Matières</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-success mb-2" onclick="testMaterialFiltering()">Tester Filtrage</button>
                        <div id="filteringResults">
                            <p class="text-muted">Cliquer pour tester</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test de sélection de type -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Sélection de Type</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <label class="form-label">Fournisseur</label>
                                <select class="form-select" id="testSupplier" onchange="onTestSupplierChange()">
                                    <option value="">Sélectionner un fournisseur</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Type *</label>
                                <select class="form-select" id="testSupplierType" onchange="onTestTypeChange()">
                                    <option value="">Sélectionner un type</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Matières filtrées</label>
                                <input type="text" class="form-control" id="testFilteredCount" readonly>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">Matières premières disponibles</label>
                                <select class="form-select" id="testMaterials" size="8">
                                    <option value="">Sélectionner un type d'abord</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Catégories correspondantes</label>
                                <div id="testCategories" class="border rounded p-3" style="height: 200px; overflow-y: auto;">
                                    <p class="text-muted">Sélectionner un type</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Résultats des tests -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Résultats des Tests</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults" style="max-height: 300px; overflow-y: auto;">
                            <p class="text-muted">Aucun test effectué</p>
                        </div>
                        <button class="btn btn-warning btn-sm" onclick="clearResults()">Effacer</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/core/storage.js"></script>
    <script src="assets/js/core/utils.js"></script>
    <script src="assets/js/modules/suppliers.js"></script>
    <script src="assets/js/modules/purchases.js"></script>

    <script>
        let suppliersModule = null;
        let purchasesModule = null;

        window.addEventListener('load', async () => {
            storage.init();
            
            try {
                // Initialiser les modules
                suppliersModule = new SuppliersModule();
                await suppliersModule.init();
                
                purchasesModule = new PurchasesModule();
                await purchasesModule.init();
                
                // Rendre disponibles globalement
                window.suppliersModule = suppliersModule;
                window.purchasesModule = purchasesModule;
                
                // Remplir le select de test
                populateTestSupplierSelect();
                
                addResult('✅ Modules initialisés avec succès');
                
            } catch (error) {
                addResult(`❌ Erreur d'initialisation: ${error.message}`);
                console.error(error);
            }
        });

        function populateTestSupplierSelect() {
            const testSupplier = document.getElementById('testSupplier');
            
            // Utiliser la méthode getUniqueSuppliers du module purchases
            const uniqueSuppliers = purchasesModule.getUniqueSuppliers();
            
            testSupplier.innerHTML = '<option value="">Sélectionner un fournisseur</option>';
            uniqueSuppliers.forEach(supplier => {
                const option = document.createElement('option');
                option.value = supplier.id;
                option.dataset.type = supplier.type || '';
                option.textContent = `${supplier.name} (${supplier.type || 'Sans type'})`;
                testSupplier.appendChild(option);
            });
            
            addResult(`✅ ${uniqueSuppliers.length} fournisseurs uniques chargés dans le test`);
        }

        function testSupplierDeduplication() {
            const allSuppliers = storage.get('suppliers') || [];
            const uniqueSuppliers = suppliersModule.deduplicateSuppliers(allSuppliers);
            
            const duplicateCount = allSuppliers.length - uniqueSuppliers.length;
            
            let html = `
                <div class="alert alert-info">
                    <strong>Résultats de déduplication :</strong><br>
                    • Fournisseurs totaux : ${allSuppliers.length}<br>
                    • Fournisseurs uniques : ${uniqueSuppliers.length}<br>
                    • Doublons éliminés : ${duplicateCount}
                </div>
            `;
            
            if (uniqueSuppliers.length > 0) {
                html += '<h6>Fournisseurs avec types multiples :</h6><ul>';
                uniqueSuppliers.forEach(supplier => {
                    const types = supplier.type ? supplier.type.split(',').map(t => t.trim()) : [];
                    if (types.length > 1) {
                        html += `<li><strong>${supplier.name}</strong> : ${types.join(', ')}</li>`;
                    }
                });
                html += '</ul>';
            }
            
            document.getElementById('supplierResults').innerHTML = html;
            addResult(`✅ Test déduplication : ${duplicateCount} doublons éliminés`);
        }

        function testMaterialFiltering() {
            const typeMapping = {
                'alimentation générale': ['Épicerie', 'Condiments', 'Huiles et vinaigres', 'Conserves'],
                'légumes et fruits': ['Légumes', 'Fruits'],
                'viandes': ['Viandes', 'Volailles'],
                'produits laitiers': ['Produits laitiers', 'Fromages'],
                'charcuterie': ['Charcuterie', 'Viandes'],
                'boulangerie': ['Boulangerie', 'Pâtisserie']
            };
            
            let html = '<h6>Test de filtrage par type :</h6><ul>';
            
            Object.keys(typeMapping).forEach(type => {
                const allowedCategories = typeMapping[type];
                const allowedCategoryIds = purchasesModule.categories
                    .filter(cat => allowedCategories.some(allowedCat => 
                        cat.name.toLowerCase().includes(allowedCat.toLowerCase()) ||
                        allowedCat.toLowerCase().includes(cat.name.toLowerCase())
                    ))
                    .map(cat => cat.id);
                
                const filteredCount = purchasesModule.rawMaterials.filter(material => 
                    allowedCategoryIds.includes(material.categoryId)
                ).length;
                
                html += `<li><strong>${type}</strong> : ${filteredCount} matières disponibles</li>`;
            });
            
            html += '</ul>';
            document.getElementById('filteringResults').innerHTML = html;
            addResult('✅ Test filtrage matières effectué');
        }

        function onTestSupplierChange() {
            const supplierSelect = document.getElementById('testSupplier');
            const typeSelect = document.getElementById('testSupplierType');
            const selectedOption = supplierSelect.selectedOptions[0];
            
            // Réinitialiser le select des types
            typeSelect.innerHTML = '<option value="">Sélectionner un type</option>';
            
            if (selectedOption && selectedOption.value) {
                const supplierTypes = selectedOption.dataset.type || '';
                
                if (supplierTypes) {
                    const types = supplierTypes.split(',').map(t => t.trim()).filter(t => t);
                    
                    types.forEach(type => {
                        const option = document.createElement('option');
                        option.value = type;
                        option.textContent = type;
                        typeSelect.appendChild(option);
                    });
                    
                    // Si un seul type, le sélectionner automatiquement
                    if (types.length === 1) {
                        typeSelect.value = types[0];
                        onTestTypeChange();
                    }
                    
                    addResult(`✅ Fournisseur sélectionné : ${selectedOption.textContent} (${types.length} type(s))`);
                }
            }
            
            // Réinitialiser l'affichage
            document.getElementById('testFilteredCount').value = '';
            document.getElementById('testMaterials').innerHTML = '<option value="">Sélectionner un type d\'abord</option>';
            document.getElementById('testCategories').innerHTML = '<p class="text-muted">Sélectionner un type</p>';
        }

        function onTestTypeChange() {
            const typeSelect = document.getElementById('testSupplierType');
            const selectedType = typeSelect.value;
            const countInput = document.getElementById('testFilteredCount');
            const materialsSelect = document.getElementById('testMaterials');
            const categoriesDiv = document.getElementById('testCategories');
            
            if (!selectedType) {
                countInput.value = '';
                materialsSelect.innerHTML = '<option value="">Sélectionner un type d\'abord</option>';
                categoriesDiv.innerHTML = '<p class="text-muted">Sélectionner un type</p>';
                return;
            }
            
            // Utiliser la méthode de filtrage du module
            testFilteringLogic(selectedType, materialsSelect, countInput, categoriesDiv);
            addResult(`✅ Type sélectionné : ${selectedType}`);
        }

        function testFilteringLogic(supplierType, materialsSelect, countInput, categoriesDiv) {
            // Mapping des types vers les catégories (même que dans le module)
            const typeMapping = {
                'alimentation générale': ['Épicerie', 'Condiments', 'Huiles et vinaigres', 'Conserves'],
                'légumes et fruits': ['Légumes', 'Fruits'],
                'viandes': ['Viandes', 'Volailles'],
                'volailles': ['Volailles', 'Viandes'],
                'poissons': ['Poissons', 'Fruits de mer'],
                'produits laitiers': ['Produits laitiers', 'Fromages'],
                'charcuterie': ['Charcuterie', 'Viandes'],
                'boulangerie': ['Boulangerie', 'Pâtisserie'],
                'pâtisserie': ['Pâtisserie', 'Boulangerie'],
                'boissons': ['Boissons'],
                'surgelés': ['Surgelés'],
                'épicerie': ['Épicerie', 'Condiments'],
                'achats divers': ['Épicerie', 'Condiments', 'Autres'],
                'produits asiatiques': ['Épicerie', 'Condiments', 'Sauces'],
                'emballage': ['Emballage', 'Autres'],
                'produits finis': ['Produits finis', 'Autres']
            };
            
            const allowedCategories = typeMapping[supplierType.toLowerCase()] || [];
            
            // Afficher les catégories correspondantes
            if (allowedCategories.length > 0) {
                categoriesDiv.innerHTML = `
                    <h6>Catégories autorisées :</h6>
                    <ul>
                        ${allowedCategories.map(cat => `<li><span class="badge bg-primary">${cat}</span></li>`).join('')}
                    </ul>
                `;
            } else {
                categoriesDiv.innerHTML = '<p class="text-warning">Aucune catégorie définie pour ce type</p>';
            }
            
            // Filtrer les matières premières
            const allowedCategoryIds = purchasesModule.categories
                .filter(cat => allowedCategories.some(allowedCat => 
                    cat.name.toLowerCase().includes(allowedCat.toLowerCase()) ||
                    allowedCat.toLowerCase().includes(cat.name.toLowerCase())
                ))
                .map(cat => cat.id);
            
            const filteredMaterials = purchasesModule.rawMaterials.filter(material => 
                allowedCategoryIds.includes(material.categoryId)
            );
            
            countInput.value = `${filteredMaterials.length} / ${purchasesModule.rawMaterials.length}`;
            
            // Remplir le select
            materialsSelect.innerHTML = '';
            if (filteredMaterials.length === 0) {
                materialsSelect.innerHTML = '<option value="">Aucune matière correspondante</option>';
            } else {
                filteredMaterials.forEach(material => {
                    const category = purchasesModule.categories.find(c => c.id === material.categoryId);
                    const inventory = storage.get('inventory') || [];
                    const invItem = inventory.find(i => i.materialId === material.id);
                    const price = invItem ? (invItem.unitPrice || invItem.averagePrice || 0) : 0;
                    const priceInfo = price > 0 ? ` - ${Utils.formatPrice(price)}` : '';
                    
                    const option = document.createElement('option');
                    option.value = material.id;
                    option.textContent = `${material.name} (${category?.name || 'N/A'})${priceInfo}`;
                    materialsSelect.appendChild(option);
                });
            }
            
            addResult(`✅ Filtrage effectué : ${filteredMaterials.length} matières pour "${supplierType}"`);
        }

        function addResult(message) {
            const results = document.getElementById('testResults');
            const time = new Date().toLocaleTimeString();
            results.innerHTML += `<p class="mb-1"><small class="text-muted">[${time}]</small> ${message}</p>`;
            results.scrollTop = results.scrollHeight;
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '<p class="text-muted">Aucun test effectué</p>';
        }
    </script>
</body>
</html>
