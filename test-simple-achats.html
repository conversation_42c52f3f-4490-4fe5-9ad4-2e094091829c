<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simple Achats</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Test Simple Module Achats</h1>
        
        <div class="alert alert-info">
            <p>Cette page teste directement le module achats avec des données simples.</p>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <button class="btn btn-primary mb-3" onclick="setupTestData()">1. Créer Données Test</button>
                <button class="btn btn-success mb-3" onclick="initModule()">2. Initialiser Module</button>
                <button class="btn btn-info mb-3" onclick="testModule()">3. Tester Module</button>
                <button class="btn btn-warning mb-3" onclick="showPurchaseModal()">4. Ouvrir Modal Achat</button>
            </div>
            
            <div class="col-md-6">
                <div id="results">
                    <p class="text-muted">Aucun test effectué</p>
                </div>
            </div>
        </div>
        
        <!-- Modal d'achat (copié du module purchases) -->
        <div class="modal fade" id="purchaseModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="purchaseModalTitle">Nouvel Achat</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="purchaseForm">
                            <div class="row">
                                <div class="col-md-4">
                                    <label class="form-label">Date *</label>
                                    <input type="date" class="form-control" id="purchaseDate" required>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Fournisseur *</label>
                                    <select class="form-select" id="purchaseSupplierId" required>
                                        <option value="">Sélectionner un fournisseur</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Référence</label>
                                    <input type="text" class="form-control" id="purchaseReference">
                                </div>
                            </div>
                            
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6>Articles achetés</h6>
                                    <div id="purchaseItems">
                                        <!-- Items will be added here -->
                                    </div>
                                    <button type="button" class="btn btn-neomorphic btn-success btn-sm" onclick="testModule.addPurchaseItem()">
                                        <i class="fas fa-plus me-1"></i>
                                        Ajouter un article
                                    </button>
                                </div>
                            </div>
                            
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <label class="form-label">Notes</label>
                                    <textarea class="form-control" id="purchaseNotes" rows="3"></textarea>
                                </div>
                                <div class="col-md-6">
                                    <h6>Récapitulatif</h6>
                                    <div class="d-flex justify-content-between">
                                        <span>Total HT:</span>
                                        <span id="purchaseTotalHT">0,00 DH</span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>TVA:</span>
                                        <span id="purchaseTVA">0,00 DH</span>
                                    </div>
                                    <hr>
                                    <div class="d-flex justify-content-between fw-bold">
                                        <span>Total TTC:</span>
                                        <span id="purchaseTotalTTC">0,00 DH</span>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        <button type="button" class="btn btn-info me-2" onclick="testModule.generatePurchaseOrder()" id="generateOrderBtn" style="display: none;">
                            <i class="fas fa-download me-2"></i>
                            Bon de Commande
                        </button>
                        <button type="button" class="btn btn-primary" onclick="testModule.savePurchase()">
                            <i class="fas fa-save me-2"></i>
                            Enregistrer
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/core/storage.js"></script>
    <script src="assets/js/core/utils.js"></script>
    <script src="assets/js/modules/purchases.js"></script>

    <script>
        let testModule = null;

        function setupTestData() {
            // Initialiser le storage
            storage.init();
            
            // Nettoyer les données existantes
            storage.set('rawMaterials', []);
            storage.set('inventory', []);
            storage.set('suppliers', []);
            
            // Créer des matières premières
            const materials = [
                { name: 'Ail', unit: 'kilogramme', categoryId: 1 },
                { name: 'Aubergine', unit: 'kilogramme', categoryId: 1 }
            ];
            
            const createdMaterials = [];
            materials.forEach(material => {
                const created = storage.add('rawMaterials', {
                    ...material,
                    description: 'Article de test',
                    createdAt: new Date().toISOString()
                });
                createdMaterials.push(created);
                updateResults(`✅ Matière première créée: ${created.name} (ID: ${created.id})`);
            });
            
            // Créer l'inventaire avec prix
            createdMaterials.forEach((material, index) => {
                const prices = [25.50, 8.75];
                const quantities = [50, 30];
                
                const inventoryItem = storage.add('inventory', {
                    materialId: material.id,
                    quantity: quantities[index],
                    averagePrice: prices[index],
                    unitPrice: prices[index], // Pour compatibilité
                    totalValue: quantities[index] * prices[index],
                    lastUpdated: new Date().toISOString()
                });
                
                updateResults(`✅ Inventaire créé: ${material.name} - ${Utils.formatPrice(prices[index])}`);
            });
            
            // Créer un fournisseur
            const supplier = storage.add('suppliers', {
                name: 'SALIPRO',
                type: 'Alimentation générale',
                phone: '0522-123456',
                email: '<EMAIL>',
                address: 'Casablanca, Maroc',
                status: 'active'
            });
            
            updateResults(`✅ Fournisseur créé: ${supplier.name} (ID: ${supplier.id})`);
            updateResults('🎉 Toutes les données de test créées!');
        }

        async function initModule() {
            try {
                testModule = new PurchasesModule();
                await testModule.init();
                
                // Rendre le module disponible globalement pour les événements
                window.testModule = testModule;
                
                updateResults('✅ Module purchases initialisé');
                
                // Debug des données
                testModule.debugData();
                
            } catch (error) {
                updateResults(`❌ Erreur initialisation: ${error.message}`);
                console.error(error);
            }
        }

        function testModule() {
            if (!testModule) {
                updateResults('❌ Module non initialisé');
                return;
            }
            
            updateResults('🔍 Test du module...');
            
            // Tester getInventoryPrice
            const rawMaterials = testModule.rawMaterials || [];
            rawMaterials.forEach(material => {
                const priceData = testModule.getInventoryPrice(material.id);
                updateResults(`${material.name}: Prix = ${Utils.formatPrice(priceData.unitPrice)}`);
            });
        }

        function showPurchaseModal() {
            if (!testModule) {
                updateResults('❌ Module non initialisé');
                return;
            }
            
            // Préparer le modal
            document.getElementById('purchaseDate').value = new Date().toISOString().split('T')[0];
            
            // Remplir les fournisseurs
            const supplierSelect = document.getElementById('purchaseSupplierId');
            supplierSelect.innerHTML = '<option value="">Sélectionner un fournisseur</option>';
            testModule.suppliers.forEach(supplier => {
                const option = document.createElement('option');
                option.value = supplier.id;
                option.textContent = supplier.name;
                supplierSelect.appendChild(option);
            });
            
            // Vider les articles et en ajouter un
            document.getElementById('purchaseItems').innerHTML = '';
            testModule.addPurchaseItem();
            
            // Afficher le modal
            new bootstrap.Modal(document.getElementById('purchaseModal')).show();
            
            updateResults('✅ Modal d\'achat ouvert');
        }

        function updateResults(message) {
            const results = document.getElementById('results');
            results.innerHTML += `<p>${message}</p>`;
            results.scrollTop = results.scrollHeight;
        }
    </script>
</body>
</html>
