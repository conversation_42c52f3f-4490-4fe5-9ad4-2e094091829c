/**
 * RestoManager - Module Gestion des Plats (Menu)
 * Gestion du menu avec catégories et liens vers fiches techniques
 */

class DishesModule {
    constructor() {
        this.dishes = [];
        this.categories = [];
        this.recipes = [];
        this.currentDish = null;
        this.searchTerm = '';
        this.filterCategory = '';
    }

    async init() {
        this.loadData();
    }

    loadData() {
        this.dishes = storage.get('dishes') || [];
        this.categories = storage.get('dishCategories') || [];
        this.recipes = storage.get('recipes') || [];
    }

    async render() {
        return `
            <div class="dishes-container">
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card-neomorphic">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <h2 class="mb-0">
                                            <i class="fas fa-utensils text-primary me-2"></i>
                                            Gestion des Plats
                                        </h2>
                                        <p class="text-muted mb-0">${this.dishes.length} plat(s) au menu</p>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <button class="btn btn-neomorphic btn-primary" onclick="dishesModule.showAddModal()">
                                            <i class="fas fa-plus me-2"></i>Nouveau Plat
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" placeholder="Rechercher un plat..." 
                                   id="searchDishes" value="${this.searchTerm}">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <select class="form-select" id="filterCategory">
                            <option value="">Toutes les catégories</option>
                            ${this.categories.map(c => `
                                <option value="${c.id}" ${this.filterCategory === c.id ? 'selected' : ''}>
                                    ${c.name}
                                </option>
                            `).join('')}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-neomorphic btn-info w-100" onclick="dishesModule.exportDishes()">
                            <i class="fas fa-download me-1"></i>Export
                        </button>
                    </div>
                </div>

                <div class="row">
                    ${this.renderDishesList()}
                </div>

                ${this.renderDishModal()}
            </div>
        `;
    }

    renderDishesList() {
        const filteredDishes = this.getFilteredDishes();

        if (filteredDishes.length === 0) {
            return `
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-utensils fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucun plat trouvé</h5>
                        <p class="text-muted">Commencez par ajouter votre premier plat</p>
                        <button class="btn btn-neomorphic btn-primary" onclick="dishesModule.showAddModal()">
                            <i class="fas fa-plus me-2"></i>Nouveau Plat
                        </button>
                    </div>
                </div>
            `;
        }

        return filteredDishes.map(dish => this.renderDishCard(dish)).join('');
    }

    renderDishCard(dish) {
        const category = this.categories.find(c => c.id === dish.categoryId);
        const recipe = this.recipes.find(r => r.dishId === dish.id);
        const statusBadge = dish.available ? 
            '<span class="badge bg-success">Disponible</span>' : 
            '<span class="badge bg-secondary">Indisponible</span>';

        return `
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card-neomorphic h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h5 class="card-title mb-0">${dish.name}</h5>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="dishesModule.editDish('${dish.id}')">
                                        <i class="fas fa-edit me-1"></i>Modifier
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="dishesModule.toggleAvailability('${dish.id}')">
                                        <i class="fas fa-toggle-${dish.available ? 'on' : 'off'} me-1"></i>
                                        ${dish.available ? 'Désactiver' : 'Activer'}
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-danger" href="#" onclick="dishesModule.deleteDish('${dish.id}')">
                                        <i class="fas fa-trash me-1"></i>Supprimer
                                    </a></li>
                                </ul>
                            </div>
                        </div>

                        ${category ? `
                            <div class="mb-2">
                                <span class="badge" style="background-color: ${category.color}">
                                    ${category.name}
                                </span>
                            </div>
                        ` : ''}

                        <p class="card-text text-muted">${dish.description || 'Aucune description'}</p>

                        <div class="row mb-3">
                            <div class="col-6">
                                <small class="text-muted">Prix</small>
                                <div class="fw-bold text-success">${Utils.formatPrice(dish.price || 0)}</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">Statut</small>
                                <div>${statusBadge}</div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                ${recipe ? `
                                    <small class="text-success">
                                        <i class="fas fa-file-alt me-1"></i>
                                        Fiche technique
                                    </small>
                                ` : `
                                    <small class="text-warning">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        Pas de fiche technique
                                    </small>
                                `}
                            </div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-info" onclick="dishesModule.viewDish('${dish.id}')" title="Voir détails">
                                    <i class="fas fa-eye"></i>
                                </button>
                                ${recipe ? `
                                    <button class="btn btn-outline-success" onclick="dishesModule.viewRecipe('${dish.id}')" title="Voir fiche technique">
                                        <i class="fas fa-file-alt"></i>
                                    </button>
                                ` : `
                                    <button class="btn btn-outline-warning" onclick="dishesModule.createRecipe('${dish.id}')" title="Créer fiche technique">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                `}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    renderDishModal() {
        return `
            <div class="modal fade" id="dishModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content modal-neomorphic">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-utensils me-2"></i>
                                <span id="dishModalTitle">Nouveau Plat</span>
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="dishForm">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label class="form-label">Nom du plat *</label>
                                            <input type="text" class="form-control" id="dishName" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Prix de vente</label>
                                            <input type="number" class="form-control" id="dishPrice" min="0" step="0.01">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Catégorie *</label>
                                            <select class="form-select" id="dishCategory" required>
                                                <option value="">Sélectionner une catégorie</option>
                                                ${this.categories.map(c => `
                                                    <option value="${c.id}">${c.name}</option>
                                                `).join('')}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Statut</label>
                                            <select class="form-select" id="dishStatus">
                                                <option value="true">Disponible</option>
                                                <option value="false">Indisponible</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Description</label>
                                    <textarea class="form-control" id="dishDescription" rows="3"></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Temps de préparation (min)</label>
                                            <input type="number" class="form-control" id="dishPrepTime" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Allergènes</label>
                                            <input type="text" class="form-control" id="dishAllergens" 
                                                   placeholder="Ex: Gluten, Lactose, Noix...">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Notes</label>
                                    <textarea class="form-control" id="dishNotes" rows="2"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="button" class="btn btn-primary" onclick="dishesModule.saveDish()">
                                <i class="fas fa-save me-2"></i>Enregistrer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    postRender() {
        this.attachEventListeners();
        window.dishesModule = this;
    }

    attachEventListeners() {
        const searchInput = document.getElementById('searchDishes');
        if (searchInput) {
            searchInput.addEventListener('input', Utils.debounce((e) => {
                this.searchTerm = e.target.value;
                this.refreshList();
            }, 300));
        }

        const categoryFilter = document.getElementById('filterCategory');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', (e) => {
                this.filterCategory = e.target.value;
                this.refreshList();
            });
        }
    }

    getFilteredDishes() {
        let filtered = [...this.dishes];

        if (this.searchTerm) {
            const term = this.searchTerm.toLowerCase();
            filtered = filtered.filter(dish => 
                dish.name.toLowerCase().includes(term) ||
                (dish.description && dish.description.toLowerCase().includes(term))
            );
        }

        if (this.filterCategory) {
            filtered = filtered.filter(dish => dish.categoryId == this.filterCategory);
        }

        return filtered.sort((a, b) => a.name.localeCompare(b.name));
    }

    showAddModal() {
        this.currentDish = null;
        document.getElementById('dishModalTitle').textContent = 'Nouveau Plat';
        this.resetForm();
        new bootstrap.Modal(document.getElementById('dishModal')).show();
    }

    editDish(id) {
        this.currentDish = this.dishes.find(d => d.id === id);
        if (!this.currentDish) return;

        document.getElementById('dishModalTitle').textContent = 'Modifier le Plat';
        this.fillForm(this.currentDish);
        new bootstrap.Modal(document.getElementById('dishModal')).show();
    }

    async saveDish() {
        const formData = this.getFormData();
        
        if (!this.validateForm(formData)) {
            return;
        }

        try {
            if (this.currentDish) {
                storage.update('dishes', this.currentDish.id, formData);
                Utils.showToast('Plat mis à jour avec succès', 'success');
            } else {
                storage.add('dishes', formData);
                Utils.showToast('Plat créé avec succès', 'success');
            }

            this.loadData();
            this.refreshList();
            bootstrap.Modal.getInstance(document.getElementById('dishModal')).hide();

        } catch (error) {
            console.error('Erreur lors de la sauvegarde:', error);
            Utils.showToast('Erreur lors de la sauvegarde', 'error');
        }
    }

    getFormData() {
        return {
            name: document.getElementById('dishName').value.trim(),
            categoryId: parseInt(document.getElementById('dishCategory').value),
            price: parseFloat(document.getElementById('dishPrice').value) || 0,
            description: document.getElementById('dishDescription').value.trim(),
            prepTime: parseInt(document.getElementById('dishPrepTime').value) || 0,
            allergens: document.getElementById('dishAllergens').value.trim(),
            notes: document.getElementById('dishNotes').value.trim(),
            available: document.getElementById('dishStatus').value === 'true'
        };
    }

    validateForm(data) {
        if (!data.name || !data.categoryId) {
            Utils.showToast('Veuillez remplir tous les champs obligatoires', 'warning');
            return false;
        }
        return true;
    }

    fillForm(dish) {
        document.getElementById('dishName').value = dish.name || '';
        document.getElementById('dishCategory').value = dish.categoryId || '';
        document.getElementById('dishPrice').value = dish.price || '';
        document.getElementById('dishDescription').value = dish.description || '';
        document.getElementById('dishPrepTime').value = dish.prepTime || '';
        document.getElementById('dishAllergens').value = dish.allergens || '';
        document.getElementById('dishNotes').value = dish.notes || '';
        document.getElementById('dishStatus').value = dish.available ? 'true' : 'false';
    }

    resetForm() {
        document.getElementById('dishForm').reset();
    }

    async toggleAvailability(id) {
        const dish = this.dishes.find(d => d.id === id);
        if (!dish) return;

        const newStatus = !dish.available;
        storage.update('dishes', id, { ...dish, available: newStatus });
        
        Utils.showToast(
            `Plat ${newStatus ? 'activé' : 'désactivé'} avec succès`, 
            'success'
        );
        
        this.loadData();
        this.refreshList();
    }

    async deleteDish(id) {
        const dish = this.dishes.find(d => d.id === id);
        if (!dish) return;

        const confirmed = await Utils.confirm(
            `Êtes-vous sûr de vouloir supprimer le plat "${dish.name}" ?`,
            'Confirmer la suppression'
        );

        if (confirmed) {
            if (storage.delete('dishes', id)) {
                Utils.showToast('Plat supprimé avec succès', 'success');
                this.loadData();
                this.refreshList();
            } else {
                Utils.showToast('Erreur lors de la suppression', 'error');
            }
        }
    }

    refreshList() {
        const container = document.querySelector('.dishes-container .row:last-child');
        if (container) {
            container.innerHTML = this.renderDishesList();
        }
    }

    exportDishes() {
        const data = this.getFilteredDishes().map(dish => {
            const category = this.categories.find(c => c.id === dish.categoryId);
            return {
                nom: dish.name,
                categorie: category ? category.name : 'Sans catégorie',
                prix: dish.price || 0,
                description: dish.description || '',
                temps_preparation: dish.prepTime || 0,
                allergenes: dish.allergens || '',
                disponible: dish.available ? 'Oui' : 'Non',
                notes: dish.notes || ''
            };
        });

        const filename = `plats_${new Date().toISOString().split('T')[0]}.csv`;
        Utils.exportToCSV(data, filename);
        Utils.showToast('Export réalisé avec succès', 'success');
    }

    destroy() {
        if (window.dishesModule === this) {
            delete window.dishesModule;
        }
    }
}

window.DishesModule = DishesModule;
