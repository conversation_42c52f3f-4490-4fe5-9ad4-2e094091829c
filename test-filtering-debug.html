<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Filtrage Matières Premières</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/neomorphic.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="card-neomorphic">
                    <div class="card-body">
                        <h4 class="mb-4">
                            <i class="fas fa-bug me-2"></i>
                            Test Filtrage Matières Premières
                        </h4>

                        <!-- Test de sélection -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <label class="form-label">Fournisseur</label>
                                <select class="form-select" id="testSupplier" onchange="onSupplierChange()">
                                    <option value="">Sélectionner un fournisseur</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Type</label>
                                <select class="form-select" id="testType" onchange="onTypeChange()">
                                    <option value="">Sélectionner un type</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Matières Premières</label>
                                <select class="form-select" id="testMaterials">
                                    <option value="">Sélectionner une matière</option>
                                </select>
                            </div>
                        </div>

                        <!-- Résultats -->
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Informations de Debug</h6>
                                <div id="debugInfo" class="bg-light p-3 rounded"></div>
                            </div>
                            <div class="col-md-6">
                                <h6>Catégories et Matières</h6>
                                <div id="categoriesInfo" class="bg-light p-3 rounded"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/core/storage.js"></script>
    <script src="assets/js/core/utils.js"></script>
    <script src="assets/js/modules/purchases.js"></script>

    <script>
        let purchasesModule;

        window.addEventListener('load', async () => {
            // Initialiser le storage
            storage.init();
            
            // Initialiser le module achats
            purchasesModule = new PurchasesModule();
            await purchasesModule.init();
            
            // Charger les fournisseurs
            loadSuppliers();
            
            // Afficher les informations de base
            showCategoriesInfo();
        });

        function loadSuppliers() {
            const suppliers = purchasesModule.getUniqueSuppliers();
            const supplierSelect = document.getElementById('testSupplier');
            
            supplierSelect.innerHTML = '<option value="">Sélectionner un fournisseur</option>';
            
            suppliers.forEach(supplier => {
                const option = document.createElement('option');
                option.value = supplier.id;
                option.dataset.type = supplier.type || '';
                option.textContent = supplier.name;
                supplierSelect.appendChild(option);
            });
            
            updateDebugInfo(`${suppliers.length} fournisseurs chargés`);
        }

        function onSupplierChange() {
            const supplierSelect = document.getElementById('testSupplier');
            const typeSelect = document.getElementById('testType');
            const selectedOption = supplierSelect.selectedOptions[0];

            // Réinitialiser le select des types
            typeSelect.innerHTML = '<option value="">Sélectionner un type</option>';

            if (selectedOption && selectedOption.value) {
                const supplierTypes = selectedOption.dataset.type || '';
                updateDebugInfo(`Fournisseur sélectionné: ${selectedOption.textContent}, Types: ${supplierTypes}`);

                // Si le fournisseur a plusieurs types, les afficher dans le select
                if (supplierTypes) {
                    const types = supplierTypes.split(',').map(t => t.trim()).filter(t => t);

                    types.forEach(type => {
                        const option = document.createElement('option');
                        option.value = type;
                        option.textContent = type;
                        typeSelect.appendChild(option);
                    });

                    // Si un seul type, le sélectionner automatiquement
                    if (types.length === 1) {
                        typeSelect.value = types[0];
                        onTypeChange();
                    }
                }
            }
        }

        function onTypeChange() {
            const typeSelect = document.getElementById('testType');
            const materialsSelect = document.getElementById('testMaterials');
            const selectedType = typeSelect.value;

            updateDebugInfo(`Type sélectionné: ${selectedType}`);

            if (selectedType) {
                // Utiliser la méthode du module pour filtrer
                purchasesModule.populateMaterialSelectFiltered(materialsSelect, selectedType);
            } else {
                // Réinitialiser
                purchasesModule.populateMaterialSelect(materialsSelect);
            }
        }

        function updateDebugInfo(message) {
            const debugDiv = document.getElementById('debugInfo');
            const timestamp = new Date().toLocaleTimeString();
            debugDiv.innerHTML += `<div><small class="text-muted">${timestamp}</small> ${message}</div>`;
            debugDiv.scrollTop = debugDiv.scrollHeight;
        }

        function showCategoriesInfo() {
            const categories = storage.get('materialCategories') || [];
            const rawMaterials = storage.get('rawMaterials') || [];
            
            let html = '<h6>Catégories disponibles:</h6><ul>';
            categories.forEach(cat => {
                const materialsCount = rawMaterials.filter(m => m.categoryId === cat.id).length;
                html += `<li>${cat.name} (ID: ${cat.id}) - ${materialsCount} matières</li>`;
            });
            html += '</ul>';
            
            html += `<p><strong>Total:</strong> ${categories.length} catégories, ${rawMaterials.length} matières premières</p>`;
            
            document.getElementById('categoriesInfo').innerHTML = html;
        }
    </script>
</body>
</html>
