/**
 * RestoManager - Module Consolidation Automatique
 * Calcul automatique des besoins en matières premières
 */

class ConsolidationModule {
    constructor() {
        this.consolidations = [];
        this.dishes = [];
        this.recipes = [];
        this.rawMaterials = [];
        this.selectedDishes = [];
        this.currentConsolidation = null;
    }

    async init() {
        this.loadData();
    }

    loadData() {
        this.consolidations = storage.get('consolidations') || [];
        this.dishes = storage.get('dishes') || [];
        this.recipes = storage.get('recipes') || [];
        this.rawMaterials = storage.get('rawMaterials') || [];
    }

    async render() {
        return `
            <div class="consolidation-container">
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card-neomorphic">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <h2 class="mb-0">
                                            <i class="fas fa-calculator text-primary me-2"></i>
                                            Consolidation Automatique
                                        </h2>
                                        <p class="text-muted mb-0">Calcul des besoins en matières premières</p>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <button class="btn btn-neomorphic btn-success me-2" onclick="consolidationModule.showNewConsolidation()">
                                            <i class="fas fa-plus me-2"></i>Nouvelle Consolidation
                                        </button>
                                        <button class="btn btn-neomorphic btn-info" onclick="consolidationModule.exportConsolidations()">
                                            <i class="fas fa-download me-2"></i>Export
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Onglets -->
                <div class="row">
                    <div class="col-12">
                        <div class="card-neomorphic">
                            <div class="card-header">
                                <ul class="nav nav-tabs-neomorphic" role="tablist">
                                    <li class="nav-item">
                                        <a class="nav-link active" data-bs-toggle="tab" href="#newConsolidationTab">
                                            <i class="fas fa-plus me-1"></i>
                                            Nouvelle Consolidation
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-bs-toggle="tab" href="#historyTab">
                                            <i class="fas fa-history me-1"></i>
                                            Historique
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <div class="card-body">
                                <div class="tab-content">
                                    <div class="tab-pane fade show active" id="newConsolidationTab">
                                        ${this.renderNewConsolidation()}
                                    </div>
                                    <div class="tab-pane fade" id="historyTab">
                                        ${this.renderHistory()}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    renderNewConsolidation() {
        return `
            <div class="row">
                <div class="col-md-6">
                    <div class="card-neomorphic mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-utensils me-2"></i>
                                Sélection des Plats
                            </h6>
                        </div>
                        <div class="card-body">
                            ${this.renderDishSelection()}
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card-neomorphic mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-list me-2"></i>
                                Plats Sélectionnés
                            </h6>
                        </div>
                        <div class="card-body">
                            <div id="selectedDishesContainer">
                                ${this.renderSelectedDishes()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <div class="card-neomorphic">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="fas fa-calculator me-2"></i>
                                    Consolidation des Besoins
                                </h6>
                                <button class="btn btn-neomorphic btn-primary" onclick="consolidationModule.saveConsolidation()" 
                                        ${this.selectedDishes.length === 0 ? 'disabled' : ''}>
                                    <i class="fas fa-save me-2"></i>Sauvegarder
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="consolidationResults">
                                ${this.renderConsolidationResults()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    renderDishSelection() {
        if (this.dishes.length === 0) {
            return `
                <div class="text-center py-3">
                    <i class="fas fa-utensils fa-2x text-muted mb-2"></i>
                    <p class="text-muted">Aucun plat disponible</p>
                </div>
            `;
        }

        return `
            <div class="dish-selection">
                ${this.dishes.map(dish => {
                    const recipe = this.recipes.find(r => r.dishId === dish.id);
                    const isSelected = this.selectedDishes.some(s => s.dishId === dish.id);
                    
                    return `
                        <div class="dish-item mb-2 p-2 border rounded ${isSelected ? 'border-primary bg-light' : ''}">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" 
                                       id="dish_${dish.id}" 
                                       ${isSelected ? 'checked' : ''}
                                       ${!recipe ? 'disabled' : ''}
                                       onchange="consolidationModule.toggleDish('${dish.id}')">
                                <label class="form-check-label w-100" for="dish_${dish.id}">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="fw-bold">${dish.name}</div>
                                            ${recipe ? 
                                                `<small class="text-success">Fiche technique disponible</small>` :
                                                `<small class="text-warning">Pas de fiche technique</small>`
                                            }
                                        </div>
                                        ${isSelected ? `
                                            <input type="number" class="form-control form-control-sm" 
                                                   style="width: 80px;" min="1" value="1"
                                                   id="quantity_${dish.id}"
                                                   onchange="consolidationModule.updateQuantity('${dish.id}', this.value)"
                                                   onclick="event.stopPropagation()">
                                        ` : ''}
                                    </div>
                                </label>
                            </div>
                        </div>
                    `;
                }).join('')}
            </div>
        `;
    }

    renderSelectedDishes() {
        if (this.selectedDishes.length === 0) {
            return `
                <div class="text-center py-3">
                    <i class="fas fa-list fa-2x text-muted mb-2"></i>
                    <p class="text-muted">Aucun plat sélectionné</p>
                </div>
            `;
        }

        return this.selectedDishes.map(selected => {
            const dish = this.dishes.find(d => d.id === selected.dishId);
            const recipe = this.recipes.find(r => r.dishId === selected.dishId);
            
            return `
                <div class="selected-dish-item mb-2 p-2 border rounded">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="fw-bold">${dish ? dish.name : 'Plat supprimé'}</div>
                            <small class="text-muted">
                                ${recipe ? `${recipe.ingredients.length} ingrédient(s)` : 'Pas de fiche technique'}
                            </small>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-primary me-2">${selected.quantity}</span>
                            <button class="btn btn-sm btn-outline-danger" 
                                    onclick="consolidationModule.removeDish('${selected.dishId}')">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }).join('');
    }

    renderConsolidationResults() {
        if (this.selectedDishes.length === 0) {
            return `
                <div class="text-center py-5">
                    <i class="fas fa-calculator fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Sélectionnez des plats</h5>
                    <p class="text-muted">La consolidation apparaîtra ici après sélection des plats</p>
                </div>
            `;
        }

        const consolidatedNeeds = this.calculateConsolidatedNeeds();
        const inventory = storage.get('inventory') || [];

        if (Object.keys(consolidatedNeeds).length === 0) {
            return `
                <div class="text-center py-5">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <h5 class="text-warning">Aucune fiche technique</h5>
                    <p class="text-muted">Les plats sélectionnés n'ont pas de fiches techniques</p>
                </div>
            `;
        }

        return `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Matière Première</th>
                            <th>Quantité Nécessaire</th>
                            <th>Unité</th>
                            <th>Stock Disponible</th>
                            <th>Statut</th>
                            <th>Coût Estimé</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${Object.entries(consolidatedNeeds).map(([materialId, need]) => {
                            const material = this.rawMaterials.find(m => m.id === materialId);
                            const stockItem = inventory.find(i => i.materialId === materialId);
                            const availableStock = stockItem ? stockItem.quantity : 0;
                            const isAvailable = availableStock >= need.quantity;
                            const estimatedCost = (stockItem ? stockItem.averagePrice : 0) * need.quantity;
                            
                            return `
                                <tr class="${!isAvailable ? 'table-warning' : ''}">
                                    <td>
                                        <div class="fw-bold">${material ? material.name : 'Article supprimé'}</div>
                                        <small class="text-muted">Utilisé dans: ${need.dishes.join(', ')}</small>
                                    </td>
                                    <td>
                                        <span class="fw-bold text-primary">${Utils.formatNumber(need.quantity, 2)}</span>
                                    </td>
                                    <td>${material ? material.unit : 'N/A'}</td>
                                    <td>
                                        <span class="fw-bold ${isAvailable ? 'text-success' : 'text-danger'}">
                                            ${Utils.formatNumber(availableStock, 2)}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-${isAvailable ? 'success' : 'danger'}">
                                            ${isAvailable ? 'Disponible' : 'Insuffisant'}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="text-success">${Utils.formatPrice(estimatedCost)}</span>
                                    </td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                    <tfoot>
                        <tr class="table-info">
                            <td colspan="5" class="fw-bold">Total Estimé</td>
                            <td class="fw-bold text-success">
                                ${Utils.formatPrice(this.calculateTotalCost(consolidatedNeeds))}
                            </td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        `;
    }

    renderHistory() {
        if (this.consolidations.length === 0) {
            return `
                <div class="text-center py-5">
                    <i class="fas fa-history fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Aucune consolidation</h5>
                    <p class="text-muted">L'historique des consolidations apparaîtra ici</p>
                </div>
            `;
        }

        return `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Plats</th>
                            <th>Articles</th>
                            <th>Coût Total</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${this.consolidations.map(consolidation => {
                            const dishNames = consolidation.dishes.map(d => {
                                const dish = this.dishes.find(dish => dish.id === d.dishId);
                                return dish ? `${dish.name} (${d.quantity})` : 'Plat supprimé';
                            });
                            
                            return `
                                <tr>
                                    <td>
                                        <div class="fw-bold">${Utils.formatDate(consolidation.createdAt)}</div>
                                        <small class="text-muted">${Utils.formatTime(consolidation.createdAt)}</small>
                                    </td>
                                    <td>
                                        <div class="fw-bold">${dishNames.length} plat(s)</div>
                                        <small class="text-muted">${dishNames.slice(0, 2).join(', ')}${dishNames.length > 2 ? '...' : ''}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">${Object.keys(consolidation.needs).length} article(s)</span>
                                    </td>
                                    <td>
                                        <span class="fw-bold text-success">${Utils.formatPrice(consolidation.totalCost)}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-info" onclick="consolidationModule.viewConsolidation('${consolidation.id}')" title="Voir détails">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="consolidationModule.deleteConsolidation('${consolidation.id}')" title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    postRender() {
        window.consolidationModule = this;
    }

    // Méthodes utilitaires
    calculateConsolidatedNeeds() {
        const needs = {};
        
        this.selectedDishes.forEach(selectedDish => {
            const dish = this.dishes.find(d => d.id === selectedDish.dishId);
            const recipe = this.recipes.find(r => r.dishId === selectedDish.dishId);
            
            if (recipe && recipe.ingredients) {
                recipe.ingredients.forEach(ingredient => {
                    const totalNeeded = ingredient.quantity * selectedDish.quantity;
                    
                    if (!needs[ingredient.materialId]) {
                        needs[ingredient.materialId] = {
                            quantity: 0,
                            dishes: []
                        };
                    }
                    
                    needs[ingredient.materialId].quantity += totalNeeded;
                    if (!needs[ingredient.materialId].dishes.includes(dish.name)) {
                        needs[ingredient.materialId].dishes.push(dish.name);
                    }
                });
            }
        });
        
        return needs;
    }

    calculateTotalCost(consolidatedNeeds) {
        const inventory = storage.get('inventory') || [];
        let totalCost = 0;
        
        Object.entries(consolidatedNeeds).forEach(([materialId, need]) => {
            const stockItem = inventory.find(i => i.materialId === materialId);
            if (stockItem) {
                totalCost += need.quantity * (stockItem.averagePrice || 0);
            }
        });
        
        return totalCost;
    }

    // Actions
    showNewConsolidation() {
        this.selectedDishes = [];
        this.refreshAll();
    }

    toggleDish(dishId) {
        const isSelected = this.selectedDishes.some(s => s.dishId === dishId);
        
        if (isSelected) {
            this.selectedDishes = this.selectedDishes.filter(s => s.dishId !== dishId);
        } else {
            this.selectedDishes.push({
                dishId: dishId,
                quantity: 1
            });
        }
        
        this.refreshAll();
    }

    updateQuantity(dishId, quantity) {
        const selected = this.selectedDishes.find(s => s.dishId === dishId);
        if (selected) {
            selected.quantity = parseInt(quantity) || 1;
            this.refreshConsolidationResults();
        }
    }

    removeDish(dishId) {
        this.selectedDishes = this.selectedDishes.filter(s => s.dishId !== dishId);
        this.refreshAll();
    }

    async saveConsolidation() {
        if (this.selectedDishes.length === 0) {
            Utils.showToast('Veuillez sélectionner au moins un plat', 'warning');
            return;
        }

        try {
            const consolidatedNeeds = this.calculateConsolidatedNeeds();
            const totalCost = this.calculateTotalCost(consolidatedNeeds);
            
            const consolidationData = {
                dishes: this.selectedDishes,
                needs: consolidatedNeeds,
                totalCost: totalCost,
                createdAt: new Date().toISOString()
            };

            storage.add('consolidations', consolidationData);
            Utils.showToast('Consolidation sauvegardée avec succès', 'success');
            
            this.loadData();
            this.selectedDishes = [];
            this.refreshAll();

        } catch (error) {
            console.error('Erreur lors de la sauvegarde:', error);
            Utils.showToast('Erreur lors de la sauvegarde', 'error');
        }
    }

    async deleteConsolidation(id) {
        const confirmed = await Utils.confirm(
            'Êtes-vous sûr de vouloir supprimer cette consolidation ?',
            'Confirmer la suppression'
        );

        if (confirmed) {
            if (storage.delete('consolidations', id)) {
                Utils.showToast('Consolidation supprimée', 'success');
                this.loadData();
                this.refreshHistory();
            }
        }
    }

    refreshAll() {
        this.refreshDishSelection();
        this.refreshSelectedDishes();
        this.refreshConsolidationResults();
    }

    refreshDishSelection() {
        const container = document.querySelector('.dish-selection');
        if (container) {
            container.innerHTML = this.renderDishSelection().match(/<div class="dish-selection">([\s\S]*)<\/div>/)[1];
        }
    }

    refreshSelectedDishes() {
        const container = document.getElementById('selectedDishesContainer');
        if (container) {
            container.innerHTML = this.renderSelectedDishes();
        }
    }

    refreshConsolidationResults() {
        const container = document.getElementById('consolidationResults');
        if (container) {
            container.innerHTML = this.renderConsolidationResults();
        }
    }

    refreshHistory() {
        const container = document.getElementById('historyTab');
        if (container) {
            container.innerHTML = this.renderHistory();
        }
    }

    exportConsolidations() {
        const data = this.consolidations.map(consolidation => {
            const dishNames = consolidation.dishes.map(d => {
                const dish = this.dishes.find(dish => dish.id === d.dishId);
                return dish ? `${dish.name} (${d.quantity})` : 'Plat supprimé';
            });
            
            return {
                date: Utils.formatDateTime(consolidation.createdAt),
                plats: dishNames.join(', '),
                nombre_articles: Object.keys(consolidation.needs).length,
                cout_total: consolidation.totalCost
            };
        });

        const filename = `consolidations_${new Date().toISOString().split('T')[0]}.csv`;
        Utils.exportToCSV(data, filename);
        Utils.showToast('Export réalisé avec succès', 'success');
    }

    destroy() {
        if (window.consolidationModule === this) {
            delete window.consolidationModule;
        }
    }
}

window.ConsolidationModule = ConsolidationModule;
