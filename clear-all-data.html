<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vider Toutes les Données</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Vider Toutes les Données
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-warning me-2"></i>Attention !</h6>
                            <p class="mb-0">Cette action va supprimer <strong>TOUTES</strong> les données de l'application :</p>
                            <ul class="mt-2 mb-0">
                                <li>Fournisseurs</li>
                                <li>Matières premières</li>
                                <li>Catégories de matières</li>
                                <li>Inventaire</li>
                                <li>Achats</li>
                                <li>Toutes les autres données</li>
                            </ul>
                        </div>
                        
                        <div class="mb-4">
                            <h6>État actuel des données :</h6>
                            <div id="dataStatus" class="border rounded p-3 bg-light">
                                <p class="text-muted">Chargement...</p>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button class="btn btn-danger btn-lg" onclick="clearAllData()">
                                <i class="fas fa-trash me-2"></i>
                                Vider Toutes les Données
                            </button>
                            <button class="btn btn-secondary" onclick="refreshStatus()">
                                <i class="fas fa-refresh me-2"></i>
                                Actualiser l'État
                            </button>
                            <a href="index.html" class="btn btn-primary">
                                <i class="fas fa-home me-2"></i>
                                Retour à l'Application
                            </a>
                        </div>
                        
                        <div class="mt-4">
                            <div id="results" class="alert alert-info" style="display: none;">
                                <!-- Résultats ici -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/core/storage.js"></script>

    <script>
        window.addEventListener('load', () => {
            storage.init();
            refreshStatus();
        });

        function refreshStatus() {
            const dataKeys = [
                'suppliers',
                'rawMaterials', 
                'materialCategories',
                'inventory',
                'purchases',
                'settings',
                'users'
            ];
            
            let statusHtml = '<table class="table table-sm"><thead><tr><th>Type de données</th><th>Nombre d\'éléments</th></tr></thead><tbody>';
            let totalItems = 0;
            
            dataKeys.forEach(key => {
                const data = storage.get(key) || [];
                const count = Array.isArray(data) ? data.length : (data ? 1 : 0);
                totalItems += count;
                
                const badgeClass = count > 0 ? 'bg-primary' : 'bg-secondary';
                statusHtml += `
                    <tr>
                        <td><strong>${getDataLabel(key)}</strong></td>
                        <td><span class="badge ${badgeClass}">${count}</span></td>
                    </tr>
                `;
            });
            
            statusHtml += `</tbody><tfoot><tr class="table-info"><td><strong>Total</strong></td><td><strong>${totalItems} éléments</strong></td></tr></tfoot></table>`;
            
            document.getElementById('dataStatus').innerHTML = statusHtml;
        }

        function getDataLabel(key) {
            const labels = {
                'suppliers': 'Fournisseurs',
                'rawMaterials': 'Matières premières',
                'materialCategories': 'Catégories de matières',
                'inventory': 'Inventaire',
                'purchases': 'Achats',
                'settings': 'Paramètres',
                'users': 'Utilisateurs'
            };
            return labels[key] || key;
        }

        function clearAllData() {
            if (!confirm('⚠️ ATTENTION ⚠️\n\nÊtes-vous ABSOLUMENT SÛR de vouloir supprimer TOUTES les données ?\n\nCette action est IRRÉVERSIBLE !')) {
                return;
            }
            
            if (!confirm('🚨 DERNIÈRE CONFIRMATION 🚨\n\nToutes les données seront perdues définitivement.\n\nContinuer ?')) {
                return;
            }
            
            try {
                // Obtenir toutes les clés du localStorage
                const allKeys = Object.keys(localStorage);
                let deletedCount = 0;
                
                // Supprimer toutes les clés liées à l'application
                allKeys.forEach(key => {
                    if (key.startsWith('restaurant_') || 
                        ['suppliers', 'rawMaterials', 'materialCategories', 'inventory', 'purchases', 'settings', 'users'].includes(key)) {
                        localStorage.removeItem(key);
                        deletedCount++;
                    }
                });
                
                // Vider complètement le localStorage pour être sûr
                localStorage.clear();
                
                // Réinitialiser le storage
                storage.init();
                
                // Afficher le résultat
                const resultsDiv = document.getElementById('results');
                resultsDiv.className = 'alert alert-success';
                resultsDiv.style.display = 'block';
                resultsDiv.innerHTML = `
                    <h6><i class="fas fa-check-circle me-2"></i>Données supprimées avec succès !</h6>
                    <p class="mb-0">
                        • ${deletedCount} clés supprimées du localStorage<br>
                        • Toutes les données ont été effacées<br>
                        • L'application est maintenant vide et prête pour de nouveaux tests
                    </p>
                `;
                
                // Actualiser l'état
                setTimeout(refreshStatus, 500);
                
            } catch (error) {
                console.error('Erreur lors de la suppression:', error);
                
                const resultsDiv = document.getElementById('results');
                resultsDiv.className = 'alert alert-danger';
                resultsDiv.style.display = 'block';
                resultsDiv.innerHTML = `
                    <h6><i class="fas fa-exclamation-circle me-2"></i>Erreur lors de la suppression</h6>
                    <p class="mb-0">Erreur : ${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
