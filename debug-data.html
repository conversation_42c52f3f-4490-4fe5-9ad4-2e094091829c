<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Données</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/css/neomorphic.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <div class="row">
            <div class="col-12">
                <div class="card-neomorphic">
                    <div class="card-body">
                        <h4 class="mb-4">
                            <i class="fas fa-bug me-2"></i>
                            Debug des Données
                        </h4>

                        <div class="row">
                            <div class="col-md-6">
                                <h6>Fournisseurs CASA VIANDE</h6>
                                <div id="casaViande" class="bg-light p-3 rounded" style="max-height: 400px; overflow-y: auto;"></div>
                            </div>
                            <div class="col-md-6">
                                <h6>Catégories et Matières Premières</h6>
                                <div id="categoriesData" class="bg-light p-3 rounded" style="max-height: 400px; overflow-y: auto;"></div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <h6>Test Filtrage</h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <label class="form-label">Type de fournisseur</label>
                                        <select class="form-select" id="testType" onchange="testFiltering()">
                                            <option value="">Sélectionner un type</option>
                                            <option value="viandes">viandes</option>
                                            <option value="volailles">volailles</option>
                                            <option value="alimentation générale">alimentation générale</option>
                                        </select>
                                    </div>
                                    <div class="col-md-8">
                                        <label class="form-label">Résultat du filtrage</label>
                                        <div id="filterResult" class="bg-light p-3 rounded"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/core/storage.js"></script>
    <script src="assets/js/core/utils.js"></script>

    <script>
        window.addEventListener('load', () => {
            storage.init();
            analyzeCasaViande();
            analyzeCategories();
        });

        function analyzeCasaViande() {
            const suppliers = storage.get('suppliers') || [];
            const casaSuppliers = suppliers.filter(s => 
                s.name.toLowerCase().includes('casa viande') || 
                s.name.toLowerCase().includes('casa') && s.name.toLowerCase().includes('viande')
            );

            let html = `<p><strong>Nombre d'entrées CASA VIANDE:</strong> ${casaSuppliers.length}</p>`;
            
            casaSuppliers.forEach((supplier, index) => {
                html += `
                    <div class="border-bottom py-2">
                        <strong>Entrée ${index + 1}:</strong><br>
                        <small>ID: ${supplier.id}</small><br>
                        <small>Nom: "${supplier.name}"</small><br>
                        <small>Type: "${supplier.type}"</small><br>
                        <small>Créé: ${supplier.createdAt}</small><br>
                        <small>JSON: ${JSON.stringify(supplier, null, 2)}</small>
                    </div>
                `;
            });

            document.getElementById('casaViande').innerHTML = html;
        }

        function analyzeCategories() {
            const categories = storage.get('materialCategories') || [];
            const rawMaterials = storage.get('rawMaterials') || [];

            let html = `<p><strong>Catégories (${categories.length}):</strong></p><ul>`;
            categories.forEach(cat => {
                const materialsCount = rawMaterials.filter(m => m.categoryId === cat.id).length;
                html += `<li>${cat.name} (ID: ${cat.id}) - ${materialsCount} matières</li>`;
            });
            html += '</ul>';

            html += `<p><strong>Exemples de matières premières:</strong></p>`;
            rawMaterials.slice(0, 10).forEach(material => {
                const category = categories.find(c => c.id === material.categoryId);
                html += `<div class="small border-bottom py-1">
                    ${material.article} - Catégorie: ${category ? category.name : 'Inconnue'} (ID: ${material.categoryId})
                </div>`;
            });

            document.getElementById('categoriesData').innerHTML = html;
        }

        function testFiltering() {
            const selectedType = document.getElementById('testType').value;
            const categories = storage.get('materialCategories') || [];
            const rawMaterials = storage.get('rawMaterials') || [];

            if (!selectedType) {
                document.getElementById('filterResult').innerHTML = 'Sélectionnez un type pour tester';
                return;
            }

            // Mapping des types (copié du code purchases.js)
            const typeMapping = {
                'alimentation générale': ['Épicerie', 'Alimentation générale'],
                'légumes et fruits': ['Légumes'],
                'viandes': ['Viandes'],
                'volailles': ['Volailles'],
                'poissons': ['Poissons'],
                'produits laitiers': ['Produits laitiers'],
                'charcuterie': ['Charcuterie', 'Viandes'],
                'boulangerie': ['Boulangerie'],
                'pâtisserie': ['Pâtisserie'],
                'boissons': ['Boissons'],
                'épicerie': ['Épicerie'],
                'achats divers': ['Achats divers', 'Épicerie'],
                'produits asiatiques': ['Produits asiatiques', 'Épicerie'],
                'emballage': ['Emballage'],
                'produits finis': ['Produits finis']
            };

            const allowedCategories = typeMapping[selectedType] || [];
            
            // Filtrer les catégories
            const allowedCategoryIds = categories
                .filter(cat => allowedCategories.some(allowedCat =>
                    cat.name.toLowerCase().includes(allowedCat.toLowerCase()) ||
                    allowedCat.toLowerCase().includes(cat.name.toLowerCase())
                ))
                .map(cat => cat.id);

            // Filtrer les matières premières
            const filteredMaterials = rawMaterials.filter(material =>
                allowedCategoryIds.includes(material.categoryId)
            );

            let html = `
                <p><strong>Type sélectionné:</strong> ${selectedType}</p>
                <p><strong>Catégories autorisées:</strong> ${allowedCategories.join(', ')}</p>
                <p><strong>IDs catégories trouvées:</strong> ${allowedCategoryIds.join(', ')}</p>
                <p><strong>Matières filtrées:</strong> ${filteredMaterials.length} / ${rawMaterials.length}</p>
            `;

            if (filteredMaterials.length > 0) {
                html += '<p><strong>Exemples de matières filtrées:</strong></p>';
                filteredMaterials.slice(0, 10).forEach(material => {
                    const category = categories.find(c => c.id === material.categoryId);
                    html += `<div class="small">${material.article} (${category ? category.name : 'Inconnue'})</div>`;
                });
            }

            document.getElementById('filterResult').innerHTML = html;
        }
    </script>
</body>
</html>
