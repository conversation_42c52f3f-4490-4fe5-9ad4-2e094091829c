<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Import Debug - RestoManager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Test Import Debug</h1>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>Test avec votre format de données</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Format testé</h6>
                            <p>Colonnes: Article, Quantité, Prix HT, TVA</p>
                            <p>Exemple: Ail, 0,00, 50,00, 0%</p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Fichier de test</label>
                            <input type="file" class="form-control" id="testFile" accept=".csv,.xlsx,.xls">
                        </div>
                        
                        <button class="btn btn-primary" onclick="testParsing()">
                            <i class="fas fa-cog me-2"></i>Test Parsing
                        </button>
                        
                        <button class="btn btn-success ms-2" onclick="downloadTestFile()">
                            <i class="fas fa-download me-2"></i>Télécharger fichier test
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6>Console Debug</h6>
                    </div>
                    <div class="card-body">
                        <div id="debugOutput" style="max-height: 400px; overflow-y: auto; font-family: monospace; font-size: 12px;">
                            Aucun test effectué
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6>Résultats du Parsing</h6>
                    </div>
                    <div class="card-body">
                        <div id="parsingResults">
                            <p class="text-muted">Aucun résultat</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- SheetJS pour lire les fichiers Excel -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    
    <!-- Core JavaScript -->
    <script src="assets/js/core/storage.js"></script>
    <script src="assets/js/core/utils.js"></script>
    
    <!-- Module Inventory -->
    <script src="assets/js/modules/inventory.js"></script>

    <script>
        let inventoryModule;
        let debugOutput = [];

        // Rediriger console.log vers notre debug
        const originalLog = console.log;
        const originalError = console.error;
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            debugOutput.push(`[LOG] ${args.join(' ')}`);
            updateDebugDisplay();
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            debugOutput.push(`[ERROR] ${args.join(' ')}`);
            updateDebugDisplay();
        };

        function updateDebugDisplay() {
            const debugDiv = document.getElementById('debugOutput');
            debugDiv.innerHTML = debugOutput.slice(-20).map(line => `<div>${line}</div>`).join('');
            debugDiv.scrollTop = debugDiv.scrollHeight;
        }

        // Initialiser le module
        window.addEventListener('load', () => {
            inventoryModule = new InventoryModule();
            inventoryModule.init().then(() => {
                console.log('Module inventory initialisé');
            });
        });

        async function testParsing() {
            const fileInput = document.getElementById('testFile');
            const file = fileInput.files[0];

            if (!file) {
                alert('Veuillez sélectionner un fichier');
                return;
            }

            debugOutput = [];
            console.log('=== DÉBUT DU TEST DE PARSING ===');
            console.log(`Fichier: ${file.name}, Taille: ${file.size} bytes, Type: ${file.type}`);

            try {
                // Test du parsing
                const data = await inventoryModule.parseExcelFile(file);
                console.log('Parsing réussi, données extraites:', data);
                
                // Test de la normalisation
                const normalizedData = inventoryModule.normalizeImportData(data);
                console.log('Normalisation réussie:', normalizedData);
                
                // Afficher les résultats
                displayResults(normalizedData);
                
            } catch (error) {
                console.error('Erreur lors du test:', error.message);
                document.getElementById('parsingResults').innerHTML = `
                    <div class="alert alert-danger">
                        <h6>Erreur détectée:</h6>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        function displayResults(data) {
            let totalHT = 0;
            let totalTTC = 0;
            
            const html = `
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Article</th>
                                <th>Quantité</th>
                                <th>Prix HT</th>
                                <th>TVA</th>
                                <th>Prix TTC</th>
                                <th>Montant HT</th>
                                <th>Montant TTC</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${data.map(item => {
                                totalHT += item.montantHT;
                                totalTTC += item.montantTTC;
                                
                                return `
                                    <tr>
                                        <td>${item.article}</td>
                                        <td>${item.quantite.toFixed(2)}</td>
                                        <td>${item.prixHT.toFixed(2)} €</td>
                                        <td>${item.tva.toFixed(1)}%</td>
                                        <td>${item.prixTTC.toFixed(2)} €</td>
                                        <td>${item.montantHT.toFixed(2)} €</td>
                                        <td>${item.montantTTC.toFixed(2)} €</td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                        <tfoot>
                            <tr class="table-info">
                                <td colspan="5"><strong>TOTAUX</strong></td>
                                <td><strong>${totalHT.toFixed(2)} €</strong></td>
                                <td><strong>${totalTTC.toFixed(2)} €</strong></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                
                <div class="alert alert-success">
                    <h6>✅ Parsing réussi!</h6>
                    <p>${data.length} article(s) traité(s) avec succès.</p>
                    <p><strong>Total HT:</strong> ${totalHT.toFixed(2)} € | <strong>Total TTC:</strong> ${totalTTC.toFixed(2)} €</p>
                </div>
            `;
            
            document.getElementById('parsingResults').innerHTML = html;
        }

        function downloadTestFile() {
            // Créer le contenu CSV
            const csvContent = `Article,Quantité,Prix HT,TVA
Ail,0,50,0%
Aubergine,1.37,7,0%
Betterave,4,6,0%
Brocoli,2.85,18,0%
Carotte,3.29,6,0%
Courgette,5.2,4.5,5.5%
Tomate,12.5,3.2,5.5%
Oignon,8,1.8,5.5%
Poivron rouge,6.75,5.4,5.5%
Salade verte,15,2.1,5.5%`;

            // Créer et télécharger le fichier
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', 'test_inventaire_format_utilisateur.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    </script>
</body>
</html>
