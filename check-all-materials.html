<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vérification Matières Premières</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Vérification Complète des Matières Premières</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Actions</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary mb-2" onclick="checkAllData()">Vérifier Toutes les Données</button>
                        <button class="btn btn-success mb-2" onclick="importFromInventory()">Importer depuis Inventaire</button>
                        <button class="btn btn-info mb-2" onclick="testPurchaseModule()">Tester Module Achats</button>
                        <button class="btn btn-warning mb-2" onclick="showRawStorage()">Voir Storage Brut</button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Statistiques</h5>
                    </div>
                    <div class="card-body">
                        <div id="stats">
                            <p class="text-muted">Cliquez sur "Vérifier Toutes les Données"</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Liste Complète des Matières Premières</h5>
                    </div>
                    <div class="card-body">
                        <div id="materialsList">
                            <p class="text-muted">Aucune donnée chargée</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Dropdown Réel</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <label class="form-label">Toutes les matières premières disponibles</label>
                                <select class="form-select" id="allMaterialsSelect" onchange="showSelectedMaterial()">
                                    <option value="">Sélectionner</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Matière sélectionnée</label>
                                <input type="text" class="form-control" id="selectedMaterial" readonly>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/core/storage.js"></script>
    <script src="assets/js/core/utils.js"></script>
    <script src="assets/js/modules/purchases.js"></script>

    <script>
        let purchaseModule = null;

        window.addEventListener('load', () => {
            storage.init();
            checkAllData();
        });

        function checkAllData() {
            const rawMaterials = storage.get('rawMaterials') || [];
            const inventory = storage.get('inventory') || [];
            const suppliers = storage.get('suppliers') || [];
            const purchases = storage.get('purchases') || [];
            
            // Afficher les statistiques
            let statsHtml = `
                <div class="row">
                    <div class="col-6">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h4>${rawMaterials.length}</h4>
                                <small>Matières Premières</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h4>${inventory.length}</h4>
                                <small>Articles en Stock</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-6">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h4>${suppliers.length}</h4>
                                <small>Fournisseurs</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h4>${purchases.length}</h4>
                                <small>Achats</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.getElementById('stats').innerHTML = statsHtml;
            
            // Afficher la liste complète
            displayAllMaterials();
            updateDropdown();
        }

        function displayAllMaterials() {
            const rawMaterials = storage.get('rawMaterials') || [];
            const inventory = storage.get('inventory') || [];
            
            if (rawMaterials.length === 0) {
                document.getElementById('materialsList').innerHTML = '<p class="text-warning">Aucune matière première trouvée dans le storage</p>';
                return;
            }
            
            let html = '<div class="table-responsive"><table class="table table-striped table-sm">';
            html += '<thead><tr><th>#</th><th>Nom</th><th>Unité</th><th>ID</th><th>Catégorie</th><th>Prix Inventaire</th><th>Stock</th></tr></thead><tbody>';
            
            rawMaterials.forEach((material, index) => {
                const invItem = inventory.find(i => i.materialId === material.id);
                const price = invItem ? (invItem.unitPrice || invItem.averagePrice || 0) : 0;
                const stock = invItem ? invItem.quantity || 0 : 0;
                
                html += `<tr>
                    <td>${index + 1}</td>
                    <td><strong>${material.name}</strong></td>
                    <td>${material.unit}</td>
                    <td><small>${material.id}</small></td>
                    <td>${material.categoryId || 'N/A'}</td>
                    <td>${price > 0 ? Utils.formatPrice(price) : '<span class="text-muted">Pas de prix</span>'}</td>
                    <td>${stock > 0 ? stock : '<span class="text-muted">0</span>'}</td>
                </tr>`;
            });
            
            html += '</tbody></table></div>';
            
            if (rawMaterials.length > 50) {
                html = `<div class="alert alert-info">Affichage des ${rawMaterials.length} matières premières</div>` + html;
            }
            
            document.getElementById('materialsList').innerHTML = html;
        }

        function updateDropdown() {
            const select = document.getElementById('allMaterialsSelect');
            const rawMaterials = storage.get('rawMaterials') || [];
            const inventory = storage.get('inventory') || [];
            
            select.innerHTML = '<option value="">Sélectionner</option>';
            
            rawMaterials.forEach(material => {
                const inventoryItem = inventory.find(item => item.materialId === material.id);
                const unitPrice = inventoryItem ? (inventoryItem.unitPrice || inventoryItem.averagePrice || 0) : 0;
                const priceInfo = unitPrice > 0 ? ` - ${Utils.formatPrice(unitPrice)}` : '';
                
                const option = document.createElement('option');
                option.value = material.id;
                option.dataset.materialName = material.name;
                option.dataset.unitPrice = unitPrice;
                option.textContent = `${material.name} (${material.unit})${priceInfo}`;
                select.appendChild(option);
            });
            
            console.log(`Dropdown mis à jour avec ${rawMaterials.length} matières premières`);
        }

        function showSelectedMaterial() {
            const select = document.getElementById('allMaterialsSelect');
            const selectedOption = select.selectedOptions[0];
            const input = document.getElementById('selectedMaterial');
            
            if (selectedOption) {
                input.value = selectedOption.textContent;
            } else {
                input.value = '';
            }
        }

        function importFromInventory() {
            // Vérifier s'il y a des articles dans l'inventaire sans matière première correspondante
            const inventory = storage.get('inventory') || [];
            const rawMaterials = storage.get('rawMaterials') || [];
            
            let orphanItems = 0;
            let createdMaterials = 0;
            
            inventory.forEach(invItem => {
                const existingMaterial = rawMaterials.find(m => m.id === invItem.materialId);
                if (!existingMaterial) {
                    orphanItems++;
                    // Créer une matière première basique pour cet article orphelin
                    const newMaterial = storage.add('rawMaterials', {
                        name: `Article ${invItem.materialId}`,
                        unit: 'unité',
                        categoryId: 1,
                        description: 'Importé depuis inventaire',
                        createdAt: new Date().toISOString()
                    });
                    
                    // Mettre à jour l'ID dans l'inventaire
                    storage.update('inventory', invItem.id, {
                        ...invItem,
                        materialId: newMaterial.id
                    });
                    
                    createdMaterials++;
                }
            });
            
            if (orphanItems > 0) {
                alert(`${orphanItems} articles orphelins trouvés dans l'inventaire. ${createdMaterials} matières premières créées.`);
                checkAllData();
            } else {
                alert('Aucun article orphelin trouvé. Toutes les matières premières sont correctement liées.');
            }
        }

        async function testPurchaseModule() {
            try {
                purchaseModule = new PurchasesModule();
                await purchaseModule.init();
                
                const moduleCount = purchaseModule.rawMaterials.length;
                const storageCount = storage.get('rawMaterials').length;
                
                let message = `Module Achats initialisé:\n`;
                message += `• Matières dans le module: ${moduleCount}\n`;
                message += `• Matières dans le storage: ${storageCount}\n`;
                
                if (moduleCount !== storageCount) {
                    message += `⚠️ PROBLÈME: Le module ne charge pas toutes les matières premières!`;
                } else {
                    message += `✅ Le module charge correctement toutes les matières premières`;
                }
                
                alert(message);
                
                // Debug du module
                purchaseModule.debugData();
                
            } catch (error) {
                alert(`Erreur lors de l'initialisation du module: ${error.message}`);
                console.error(error);
            }
        }

        function showRawStorage() {
            const allData = {
                rawMaterials: storage.get('rawMaterials'),
                inventory: storage.get('inventory'),
                suppliers: storage.get('suppliers'),
                purchases: storage.get('purchases'),
                config: storage.get('config')
            };
            
            console.log('=== DONNÉES COMPLÈTES DU STORAGE ===');
            console.log(allData);
            
            // Afficher dans une nouvelle fenêtre
            const newWindow = window.open('', '_blank');
            newWindow.document.write(`
                <html>
                <head><title>Storage Brut</title></head>
                <body>
                    <h1>Données du Storage</h1>
                    <pre>${JSON.stringify(allData, null, 2)}</pre>
                </body>
                </html>
            `);
        }
    </script>
</body>
</html>
