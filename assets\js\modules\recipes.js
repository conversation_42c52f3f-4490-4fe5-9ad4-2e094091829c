/**
 * RestoManager - Module Fiches Techniques de Plats
 * Gestion des fiches techniques avec ingrédients, coûts et calculs automatiques
 */

class RecipesModule {
    constructor() {
        this.recipes = [];
        this.dishes = [];
        this.rawMaterials = [];
        this.currentRecipe = null;
    }

    async init() {
        this.loadData();
    }

    loadData() {
        this.recipes = storage.get('recipes') || [];
        this.dishes = storage.get('dishes') || [];
        this.rawMaterials = storage.get('rawMaterials') || [];
    }

    async render() {
        return `
            <div class="recipes-container">
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card-neomorphic">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <h2 class="mb-0">
                                            <i class="fas fa-file-alt text-primary me-2"></i>
                                            Fiches Techniques
                                        </h2>
                                        <p class="text-muted mb-0">${this.recipes.length} fiche(s) technique(s)</p>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <button class="btn btn-neomorphic btn-primary" onclick="recipesModule.showAddModal()">
                                            <i class="fas fa-plus me-2"></i>Nouvelle Fiche
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    ${this.renderRecipesList()}
                </div>

                ${this.renderRecipeModal()}
            </div>
        `;
    }

    renderRecipesList() {
        if (this.recipes.length === 0) {
            return `
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucune fiche technique</h5>
                        <p class="text-muted">Créez vos premières fiches techniques</p>
                        <button class="btn btn-neomorphic btn-primary" onclick="recipesModule.showAddModal()">
                            <i class="fas fa-plus me-2"></i>Nouvelle Fiche
                        </button>
                    </div>
                </div>
            `;
        }

        return this.recipes.map(recipe => this.renderRecipeCard(recipe)).join('');
    }

    renderRecipeCard(recipe) {
        const dish = this.dishes.find(d => d.id === recipe.dishId);
        const totalCost = this.calculateRecipeCost(recipe);

        return `
            <div class="col-lg-6 col-md-12 mb-4">
                <div class="card-neomorphic h-100">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">${dish ? dish.name : 'Plat supprimé'}</h5>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="recipesModule.editRecipe('${recipe.id}')">
                                        <i class="fas fa-edit me-1"></i>Modifier
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="recipesModule.duplicateRecipe('${recipe.id}')">
                                        <i class="fas fa-copy me-1"></i>Dupliquer
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-danger" href="#" onclick="recipesModule.deleteRecipe('${recipe.id}')">
                                        <i class="fas fa-trash me-1"></i>Supprimer
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-4">
                                <small class="text-muted">Portions</small>
                                <div class="fw-bold">${recipe.portions || 1}</div>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">Coût total</small>
                                <div class="fw-bold text-danger">${Utils.formatPrice(totalCost)}</div>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">Coût/portion</small>
                                <div class="fw-bold text-warning">${Utils.formatPrice(totalCost / (recipe.portions || 1))}</div>
                            </div>
                        </div>

                        <h6>Ingrédients (${recipe.ingredients ? recipe.ingredients.length : 0})</h6>
                        <div class="ingredients-list">
                            ${recipe.ingredients ? recipe.ingredients.slice(0, 5).map(ingredient => {
                                const material = this.rawMaterials.find(m => m.id === ingredient.materialId);
                                return `
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <small>${material ? material.name : 'Supprimé'}</small>
                                        <small class="text-muted">${ingredient.quantity} ${material ? material.unit : ''}</small>
                                    </div>
                                `;
                            }).join('') : ''}
                            ${recipe.ingredients && recipe.ingredients.length > 5 ? 
                                `<small class="text-muted">... et ${recipe.ingredients.length - 5} autre(s)</small>` : ''}
                        </div>

                        ${recipe.instructions ? `
                            <div class="mt-3">
                                <h6>Instructions</h6>
                                <p class="text-muted small">${Utils.truncate(recipe.instructions, 100)}</p>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    }

    renderRecipeModal() {
        return `
            <div class="modal fade" id="recipeModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content modal-neomorphic">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-file-alt me-2"></i>
                                <span id="recipeModalTitle">Nouvelle Fiche Technique</span>
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="recipeForm">
                                <div class="row mb-3">
                                    <div class="col-md-8">
                                        <label class="form-label">Plat *</label>
                                        <select class="form-select" id="recipeDish" required>
                                            <option value="">Sélectionner un plat</option>
                                            ${this.dishes.map(d => `
                                                <option value="${d.id}">${d.name}</option>
                                            `).join('')}
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Nombre de portions *</label>
                                        <input type="number" class="form-control" id="recipePortions" min="1" value="1" required>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-12">
                                        <h6>Ingrédients</h6>
                                        <div id="recipeIngredients">
                                            <!-- Ingrédients ajoutés dynamiquement -->
                                        </div>
                                        <button type="button" class="btn btn-neomorphic btn-success btn-sm" onclick="recipesModule.addIngredient()">
                                            <i class="fas fa-plus me-1"></i>Ajouter un ingrédient
                                        </button>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">Temps de préparation (min)</label>
                                        <input type="number" class="form-control" id="recipePrepTime" min="0">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Temps de cuisson (min)</label>
                                        <input type="number" class="form-control" id="recipeCookTime" min="0">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Instructions</label>
                                    <textarea class="form-control" id="recipeInstructions" rows="5"></textarea>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Notes</label>
                                    <textarea class="form-control" id="recipeNotes" rows="3"></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-12">
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <h6>Récapitulatif des coûts</h6>
                                                <div class="d-flex justify-content-between">
                                                    <span>Coût total:</span>
                                                    <span id="recipeTotalCost">0,00 €</span>
                                                </div>
                                                <div class="d-flex justify-content-between">
                                                    <span>Coût par portion:</span>
                                                    <span id="recipePortionCost">0,00 €</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="button" class="btn btn-primary" onclick="recipesModule.saveRecipe()">
                                <i class="fas fa-save me-2"></i>Enregistrer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    postRender() {
        window.recipesModule = this;
    }

    calculateRecipeCost(recipe) {
        if (!recipe.ingredients) return 0;
        
        const inventory = storage.get('inventory') || [];
        let totalCost = 0;

        recipe.ingredients.forEach(ingredient => {
            const stockItem = inventory.find(i => i.materialId === ingredient.materialId);
            if (stockItem) {
                totalCost += ingredient.quantity * (stockItem.averagePrice || 0);
            }
        });

        return totalCost;
    }

    showAddModal() {
        this.currentRecipe = null;
        document.getElementById('recipeModalTitle').textContent = 'Nouvelle Fiche Technique';
        this.resetForm();
        new bootstrap.Modal(document.getElementById('recipeModal')).show();
    }

    addIngredient() {
        const container = document.getElementById('recipeIngredients');
        const ingredientIndex = container.children.length;
        
        const ingredientHtml = `
            <div class="ingredient-item mb-3 p-3 border rounded">
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">Matière première</label>
                        <select class="form-select" name="materialId" required>
                            <option value="">Sélectionner</option>
                            ${this.rawMaterials.map(m => `
                                <option value="${m.id}">${m.name} (${m.unit})</option>
                            `).join('')}
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Quantité</label>
                        <input type="number" class="form-control" name="quantity" min="0" step="0.01" required>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button type="button" class="btn btn-danger w-100" onclick="this.parentElement.parentElement.parentElement.remove(); recipesModule.updateRecipeCost()">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        container.insertAdjacentHTML('beforeend', ingredientHtml);
        
        // Ajouter les événements pour le calcul automatique
        const newItem = container.lastElementChild;
        const inputs = newItem.querySelectorAll('select, input');
        inputs.forEach(input => {
            input.addEventListener('change', () => this.updateRecipeCost());
        });
    }

    updateRecipeCost() {
        const ingredients = document.querySelectorAll('#recipeIngredients .ingredient-item');
        const inventory = storage.get('inventory') || [];
        let totalCost = 0;
        
        ingredients.forEach(item => {
            const materialId = item.querySelector('[name="materialId"]').value;
            const quantity = parseFloat(item.querySelector('[name="quantity"]').value) || 0;
            
            if (materialId && quantity) {
                const stockItem = inventory.find(i => i.materialId === materialId);
                if (stockItem) {
                    totalCost += quantity * (stockItem.averagePrice || 0);
                }
            }
        });
        
        const portions = parseInt(document.getElementById('recipePortions').value) || 1;
        const costPerPortion = totalCost / portions;
        
        document.getElementById('recipeTotalCost').textContent = Utils.formatPrice(totalCost);
        document.getElementById('recipePortionCost').textContent = Utils.formatPrice(costPerPortion);
    }

    async saveRecipe() {
        const formData = this.getRecipeFormData();
        
        if (!this.validateRecipeForm(formData)) {
            return;
        }

        try {
            if (this.currentRecipe) {
                storage.update('recipes', this.currentRecipe.id, formData);
                Utils.showToast('Fiche technique mise à jour', 'success');
            } else {
                storage.add('recipes', formData);
                Utils.showToast('Fiche technique créée avec succès', 'success');
            }

            this.loadData();
            this.refreshList();
            bootstrap.Modal.getInstance(document.getElementById('recipeModal')).hide();

        } catch (error) {
            console.error('Erreur lors de la sauvegarde:', error);
            Utils.showToast('Erreur lors de la sauvegarde', 'error');
        }
    }

    getRecipeFormData() {
        const ingredients = [];
        document.querySelectorAll('#recipeIngredients .ingredient-item').forEach(item => {
            const materialId = item.querySelector('[name="materialId"]').value;
            const quantity = parseFloat(item.querySelector('[name="quantity"]').value);
            
            if (materialId && quantity) {
                ingredients.push({ materialId, quantity });
            }
        });

        return {
            dishId: document.getElementById('recipeDish').value,
            portions: parseInt(document.getElementById('recipePortions').value) || 1,
            ingredients: ingredients,
            prepTime: parseInt(document.getElementById('recipePrepTime').value) || 0,
            cookTime: parseInt(document.getElementById('recipeCookTime').value) || 0,
            instructions: document.getElementById('recipeInstructions').value.trim(),
            notes: document.getElementById('recipeNotes').value.trim()
        };
    }

    validateRecipeForm(data) {
        if (!data.dishId || data.ingredients.length === 0) {
            Utils.showToast('Veuillez sélectionner un plat et ajouter au moins un ingrédient', 'warning');
            return false;
        }
        return true;
    }

    resetForm() {
        document.getElementById('recipeForm').reset();
        document.getElementById('recipeIngredients').innerHTML = '';
        this.updateRecipeCost();
        this.addIngredient(); // Ajouter un premier ingrédient
    }

    refreshList() {
        const container = document.querySelector('.recipes-container .row:last-child');
        if (container) {
            container.innerHTML = this.renderRecipesList();
        }
    }

    async deleteRecipe(id) {
        const recipe = this.recipes.find(r => r.id === id);
        if (!recipe) return;

        const dish = this.dishes.find(d => d.id === recipe.dishId);
        const confirmed = await Utils.confirm(
            `Supprimer la fiche technique de "${dish ? dish.name : 'Plat supprimé'}" ?`,
            'Confirmer la suppression'
        );

        if (confirmed) {
            if (storage.delete('recipes', id)) {
                Utils.showToast('Fiche technique supprimée', 'success');
                this.loadData();
                this.refreshList();
            }
        }
    }

    destroy() {
        if (window.recipesModule === this) {
            delete window.recipesModule;
        }
    }
}

window.RecipesModule = RecipesModule;
