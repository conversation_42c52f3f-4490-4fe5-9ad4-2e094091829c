/**
 * RestoManager - Utilitaires et fonctions communes
 */

class Utils {
    /**
     * Formate une date en français
     */
    static formatDate(date, options = {}) {
        const defaultOptions = {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        
        const formatOptions = { ...defaultOptions, ...options };
        return new Date(date).toLocaleDateString('fr-FR', formatOptions);
    }

    /**
     * Formate une heure
     */
    static formatTime(date) {
        return new Date(date).toLocaleTimeString('fr-FR', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    /**
     * Formate une date et heure complète
     */
    static formatDateTime(date) {
        return new Date(date).toLocaleString('fr-FR');
    }

    /**
     * Formate un prix en euros
     */
    static formatPrice(amount, currency = 'EUR') {
        return new Intl.NumberFormat('fr-FR', {
            style: 'currency',
            currency: currency
        }).format(amount);
    }

    /**
     * Formate un nombre avec séparateurs
     */
    static formatNumber(number, decimals = 2) {
        return new Intl.NumberFormat('fr-FR', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        }).format(number);
    }

    /**
     * Génère une couleur aléatoire
     */
    static randomColor() {
        const colors = [
            '#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6',
            '#1abc9c', '#e67e22', '#34495e', '#95a5a6', '#16a085'
        ];
        return colors[Math.floor(Math.random() * colors.length)];
    }

    /**
     * Débounce une fonction
     */
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Valide un email
     */
    static isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * Valide un numéro de téléphone français
     */
    static isValidPhone(phone) {
        const phoneRegex = /^(?:(?:\+|00)33|0)\s*[1-9](?:[\s.-]*\d{2}){4}$/;
        return phoneRegex.test(phone);
    }

    /**
     * Génère un slug à partir d'un texte
     */
    static slugify(text) {
        return text
            .toString()
            .toLowerCase()
            .trim()
            .replace(/\s+/g, '-')
            .replace(/[^\w\-]+/g, '')
            .replace(/\-\-+/g, '-')
            .replace(/^-+/, '')
            .replace(/-+$/, '');
    }

    /**
     * Capitalise la première lettre
     */
    static capitalize(str) {
        return str.charAt(0).toUpperCase() + str.slice(1);
    }

    /**
     * Tronque un texte
     */
    static truncate(str, length = 100) {
        return str.length > length ? str.substring(0, length) + '...' : str;
    }

    /**
     * Calcule la différence entre deux dates en jours
     */
    static daysDifference(date1, date2) {
        const oneDay = 24 * 60 * 60 * 1000;
        return Math.round(Math.abs((new Date(date1) - new Date(date2)) / oneDay));
    }

    /**
     * Vérifie si une date est aujourd'hui
     */
    static isToday(date) {
        const today = new Date();
        const checkDate = new Date(date);
        return checkDate.toDateString() === today.toDateString();
    }

    /**
     * Vérifie si une date est cette semaine
     */
    static isThisWeek(date) {
        const today = new Date();
        const checkDate = new Date(date);
        const startOfWeek = new Date(today.setDate(today.getDate() - today.getDay()));
        const endOfWeek = new Date(today.setDate(today.getDate() - today.getDay() + 6));
        
        return checkDate >= startOfWeek && checkDate <= endOfWeek;
    }

    /**
     * Génère un ID unique
     */
    static generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * Clone profond d'un objet
     */
    static deepClone(obj) {
        return JSON.parse(JSON.stringify(obj));
    }

    /**
     * Mélange un tableau
     */
    static shuffle(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }

    /**
     * Groupe un tableau par une propriété
     */
    static groupBy(array, key) {
        return array.reduce((groups, item) => {
            const group = item[key];
            groups[group] = groups[group] || [];
            groups[group].push(item);
            return groups;
        }, {});
    }

    /**
     * Calcule la somme d'une propriété dans un tableau
     */
    static sumBy(array, key) {
        return array.reduce((sum, item) => sum + (item[key] || 0), 0);
    }

    /**
     * Trouve la valeur moyenne d'une propriété
     */
    static averageBy(array, key) {
        if (array.length === 0) return 0;
        return this.sumBy(array, key) / array.length;
    }

    /**
     * Exporte des données en CSV
     */
    static exportToCSV(data, filename = 'export.csv') {
        if (!data.length) return;

        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => 
                headers.map(header => {
                    const value = row[header];
                    return typeof value === 'string' && value.includes(',') 
                        ? `"${value}"` 
                        : value;
                }).join(',')
            )
        ].join('\n');

        this.downloadFile(csvContent, filename, 'text/csv');
    }

    /**
     * Exporte des données en JSON
     */
    static exportToJSON(data, filename = 'export.json') {
        const jsonContent = JSON.stringify(data, null, 2);
        this.downloadFile(jsonContent, filename, 'application/json');
    }

    /**
     * Télécharge un fichier
     */
    static downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }

    /**
     * Lit un fichier uploadé
     */
    static readFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = e => resolve(e.target.result);
            reader.onerror = reject;
            reader.readAsText(file);
        });
    }

    /**
     * Affiche une notification toast
     */
    static showToast(message, type = 'info', duration = 3000) {
        const toast = document.getElementById('liveToast');
        const toastBody = toast.querySelector('.toast-body');
        const toastHeader = toast.querySelector('.toast-header');
        
        // Icônes selon le type
        const icons = {
            success: 'fas fa-check-circle text-success',
            error: 'fas fa-exclamation-circle text-danger',
            warning: 'fas fa-exclamation-triangle text-warning',
            info: 'fas fa-info-circle text-info'
        };
        
        // Mise à jour de l'icône
        const icon = toastHeader.querySelector('i');
        icon.className = icons[type] || icons.info;
        
        // Mise à jour du message
        toastBody.textContent = message;
        
        // Affichage du toast
        const bsToast = new bootstrap.Toast(toast, { delay: duration });
        bsToast.show();
    }

    /**
     * Confirme une action avec une modal
     */
    static confirm(message, title = 'Confirmation') {
        return new Promise((resolve) => {
            const modalHtml = `
                <div class="modal fade" id="confirmModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">${title}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <p>${message}</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                <button type="button" class="btn btn-primary" id="confirmBtn">Confirmer</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            const modal = document.getElementById('confirmModal');
            const confirmBtn = document.getElementById('confirmBtn');
            
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();
            
            confirmBtn.addEventListener('click', () => {
                bsModal.hide();
                resolve(true);
            });
            
            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
                resolve(false);
            });
        });
    }

    /**
     * Anime l'apparition d'un élément
     */
    static animateIn(element, animation = 'fadeInUp') {
        element.classList.add('fade-in');
        element.style.animationName = animation;
    }

    /**
     * Valide les données d'un formulaire
     */
    static validateForm(formData, rules) {
        const errors = {};
        
        Object.keys(rules).forEach(field => {
            const value = formData[field];
            const rule = rules[field];
            
            if (rule.required && (!value || value.trim() === '')) {
                errors[field] = 'Ce champ est requis';
            } else if (value) {
                if (rule.email && !this.isValidEmail(value)) {
                    errors[field] = 'Email invalide';
                }
                if (rule.phone && !this.isValidPhone(value)) {
                    errors[field] = 'Numéro de téléphone invalide';
                }
                if (rule.min && value.length < rule.min) {
                    errors[field] = `Minimum ${rule.min} caractères`;
                }
                if (rule.max && value.length > rule.max) {
                    errors[field] = `Maximum ${rule.max} caractères`;
                }
            }
        });
        
        return {
            isValid: Object.keys(errors).length === 0,
            errors
        };
    }
}

// Rendre Utils disponible globalement
window.Utils = Utils;
