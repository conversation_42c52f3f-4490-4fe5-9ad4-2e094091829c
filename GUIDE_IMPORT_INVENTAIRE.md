# Guide d'Import d'Inventaire - RestoManager 📊

## Vue d'ensemble

La fonctionnalité d'import d'inventaire permet d'importer en masse les données de stock depuis un fichier Excel ou CSV. L'application calcule automatiquement les montants HT et TTC pour chaque article ainsi que la valeur totale du stock.

## 📋 Format de Fichier Requis

### Colonnes Obligatoires

Le fichier doit contenir les colonnes suivantes (l'ordre n'est pas important) :

| Colonne | Description | Exemple |
|---------|-------------|---------|
| **Article** | Nom de l'article | "Tomates cerises" |
| **Quantité** | Quantité en stock | 25 |
| **Prix HT** | Prix unitaire hors taxes | 3.50 |
| **TVA** | Taux de TVA en pourcentage | 5.5 |

### Formats de Fichiers Supportés

- ✅ **Excel (.xlsx, .xls)** - Recommandé
- ✅ **CSV (.csv)** - Alternative simple

### Exemple de Fichier CSV

```csv
Article,Quantité,Prix HT,TVA
Tomates cerises,25,3.50,5.5
Mozzarella di Bufala,12,8.90,5.5
Basilic frais,8,2.20,5.5
Huile d'olive extra vierge,15,12.50,20
```

## 🚀 Procédure d'Import

### Étape 1 : Accéder à l'Import
1. Allez dans le module **Inventaire**
2. Cliquez sur le bouton **"Import Excel"** (vert avec icône upload)

### Étape 2 : Configuration
1. **Sélectionner le fichier** : Choisissez votre fichier Excel/CSV
2. **Catégorie par défaut** : Sélectionnez une catégorie pour les nouveaux articles
3. **Unité par défaut** : Choisissez l'unité de mesure pour les nouveaux articles
4. **Options** :
   - ☑️ **Mettre à jour les articles existants** : Met à jour les articles déjà présents
   - ☐ **Créer uniquement de nouveaux articles** : Ignore les articles existants

### Étape 3 : Aperçu
1. Cliquez sur **"Aperçu"** pour voir les données analysées
2. Vérifiez les calculs automatiques :
   - **Prix TTC** = Prix HT × (1 + TVA/100)
   - **Montant HT** = Quantité × Prix HT
   - **Montant TTC** = Quantité × Prix TTC
3. Consultez les totaux :
   - **Total Stock HT** : Somme de tous les montants HT
   - **Total Stock TTC** : Somme de tous les montants TTC

### Étape 4 : Import Final
1. Si l'aperçu est correct, cliquez sur **"Importer"**
2. L'application traite les données et affiche un résumé
3. Les données sont automatiquement sauvegardées

## 📊 Calculs Automatiques

### Formules Utilisées

```javascript
Prix TTC = Prix HT × (1 + TVA/100)
Montant HT par article = Quantité × Prix HT
Montant TTC par article = Quantité × Prix TTC
Total Stock HT = Σ(Montant HT de tous les articles)
Total Stock TTC = Σ(Montant TTC de tous les articles)
```

### Exemple de Calcul

Pour un article "Tomates cerises" :
- Quantité : 25
- Prix HT : 3,50 €
- TVA : 5,5%

**Calculs :**
- Prix TTC = 3,50 × (1 + 5,5/100) = 3,50 × 1,055 = **3,69 €**
- Montant HT = 25 × 3,50 = **87,50 €**
- Montant TTC = 25 × 3,69 = **92,31 €**

## ⚙️ Gestion des Articles

### Nouveaux Articles
- Créés automatiquement avec la catégorie et l'unité par défaut
- Ajoutés à la liste des matières premières
- Entrée d'inventaire créée avec les quantités importées

### Articles Existants
- **Si "Mettre à jour" est coché** : Les quantités et prix sont mis à jour
- **Si "Mettre à jour" est décoché** : Les articles existants sont ignorés
- Identification par nom (insensible à la casse)

### Mouvements de Stock
- Chaque import génère un mouvement de stock de type "Entrée"
- Motif : "Import Excel"
- Notes incluent le prix HT et la TVA

## 🔍 Validation des Données

### Contrôles Automatiques
- ✅ Nom d'article non vide
- ✅ Quantité ≥ 0
- ✅ Prix HT ≥ 0
- ✅ TVA valide (généralement 0-100%)

### Gestion des Erreurs
- **Données manquantes** : Ligne ignorée avec message d'erreur
- **Format invalide** : Conversion automatique ou erreur
- **Fichier corrompu** : Message d'erreur explicite

## 📝 Bonnes Pratiques

### Préparation du Fichier
1. **Noms cohérents** : Utilisez des noms d'articles clairs et uniques
2. **Unités standardisées** : Assurez-vous que les quantités sont dans la même unité
3. **Prix actualisés** : Vérifiez que les prix sont à jour
4. **TVA correcte** : Utilisez les taux de TVA appropriés (5,5% pour alimentaire, 20% pour autres)

### Avant l'Import
1. **Sauvegarde** : Exportez votre inventaire actuel
2. **Test** : Testez avec un petit échantillon d'abord
3. **Vérification** : Contrôlez les données dans l'aperçu

### Après l'Import
1. **Vérification** : Consultez l'inventaire pour valider les données
2. **Ajustements** : Utilisez la fonction d'ajustement si nécessaire
3. **Sauvegarde** : Exportez les nouvelles données

## 🛠️ Dépannage

### Problèmes Courants

**"Erreur de lecture du fichier"**
- Vérifiez le format du fichier (.xlsx, .xls, .csv)
- Assurez-vous que le fichier n'est pas corrompu
- Essayez de sauvegarder le fichier dans un autre format

**"Données invalides pour l'article"**
- Vérifiez que toutes les colonnes obligatoires sont présentes
- Contrôlez les valeurs numériques (pas de texte dans Prix HT ou Quantité)
- Assurez-vous que les noms d'articles ne sont pas vides

**"Veuillez sélectionner une catégorie par défaut"**
- Créez d'abord des catégories dans le module Achats
- Sélectionnez une catégorie avant l'import des nouveaux articles

### Formats de Colonnes Acceptés

L'application reconnaît plusieurs variantes de noms de colonnes :

| Colonne Standard | Variantes Acceptées |
|------------------|-------------------|
| Article | nom, name, produit |
| Quantité | quantite, quantity, qty |
| Prix HT | prix_ht, prixht, price |
| TVA | tax, vat |

## 📊 Exemple Complet

### Fichier d'Entrée (CSV)
```csv
Article,Quantité,Prix HT,TVA
Saumon fumé,10,28.50,5.5
Avocat,30,1.80,5.5
Pain complet,20,2.40,5.5
```

### Résultat Calculé
| Article | Qté | Prix HT | TVA | Prix TTC | Montant HT | Montant TTC |
|---------|-----|---------|-----|----------|------------|-------------|
| Saumon fumé | 10 | 28,50 € | 5,5% | 30,07 € | 285,00 € | 300,68 € |
| Avocat | 30 | 1,80 € | 5,5% | 1,90 € | 54,00 € | 56,97 € |
| Pain complet | 20 | 2,40 € | 5,5% | 2,53 € | 48,00 € | 50,64 € |
| **TOTAL** | | | | | **387,00 €** | **408,29 €** |

---

## 📞 Support

Pour toute question ou problème avec l'import d'inventaire :
1. Consultez ce guide
2. Vérifiez le format de votre fichier
3. Testez avec le fichier template fourni
4. Contactez le support technique si nécessaire

**Template disponible** : `template_import_inventaire.csv`

---

*Guide d'Import d'Inventaire - RestoManager v1.0.0*
