<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Import Inventaire - RestoManager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Test Import Inventaire</h1>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>Test de la Fonctionnalité d'Import</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Instructions</h6>
                            <ol>
                                <li>Téléchargez le fichier template : <a href="template_import_inventaire.csv" download class="btn btn-sm btn-outline-primary"><i class="fas fa-download me-1"></i>template_import_inventaire.csv</a></li>
                                <li>Cliquez sur "Test Import" pour ouvrir le modal</li>
                                <li>Sélectionnez le fichier téléchargé</li>
                                <li>Cliquez sur "Aperçu" pour voir les calculs</li>
                                <li>Cliquez sur "Importer" pour finaliser</li>
                            </ol>
                        </div>
                        
                        <button class="btn btn-success" onclick="testImport()">
                            <i class="fas fa-upload me-2"></i>Test Import
                        </button>
                        
                        <button class="btn btn-info ms-2" onclick="showInventory()">
                            <i class="fas fa-warehouse me-2"></i>Voir Inventaire
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6>Statistiques</h6>
                    </div>
                    <div class="card-body">
                        <div id="stats">
                            <p>Articles en stock: <span id="articleCount">0</span></p>
                            <p>Valeur totale: <span id="totalValue">0,00 €</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6>Contenu de l'Inventaire</h6>
                    </div>
                    <div class="card-body">
                        <div id="inventoryContent">
                            <p class="text-muted">Aucun article en inventaire</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- SheetJS pour lire les fichiers Excel -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    
    <!-- Core JavaScript -->
    <script src="assets/js/core/storage.js"></script>
    <script src="assets/js/core/utils.js"></script>
    
    <!-- Module Inventory -->
    <script src="assets/js/modules/inventory.js"></script>

    <script>
        let inventoryModule;

        // Initialiser le module
        window.addEventListener('load', () => {
            inventoryModule = new InventoryModule();
            inventoryModule.init().then(() => {
                updateStats();
                showInventory();
            });
        });

        function testImport() {
            if (!inventoryModule) {
                alert('Module non initialisé');
                return;
            }
            
            // Créer et afficher le modal d'import
            const modalHtml = inventoryModule.renderImportModal();
            
            // Ajouter le modal au DOM s'il n'existe pas
            if (!document.getElementById('importModal')) {
                document.body.insertAdjacentHTML('beforeend', modalHtml);
            }
            
            // Attacher les événements
            inventoryModule.postRender();
            
            // Afficher le modal
            inventoryModule.showImportModal();
        }

        function showInventory() {
            if (!inventoryModule) return;
            
            const inventory = storage.get('inventory') || [];
            const rawMaterials = storage.get('rawMaterials') || [];
            
            if (inventory.length === 0) {
                document.getElementById('inventoryContent').innerHTML = '<p class="text-muted">Aucun article en inventaire</p>';
                return;
            }
            
            const html = `
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Article</th>
                                <th>Quantité</th>
                                <th>Prix Moyen</th>
                                <th>Valeur Totale</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${inventory.map(item => {
                                const material = rawMaterials.find(m => m.id === item.materialId);
                                return `
                                    <tr>
                                        <td>${material ? material.name : 'Article supprimé'}</td>
                                        <td>${Utils.formatNumber(item.quantity, 2)} ${material ? material.unit : ''}</td>
                                        <td>${Utils.formatPrice(item.averagePrice || 0)}</td>
                                        <td>${Utils.formatPrice(item.totalValue || 0)}</td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                </div>
            `;
            
            document.getElementById('inventoryContent').innerHTML = html;
        }

        function updateStats() {
            const inventory = storage.get('inventory') || [];
            const totalValue = inventory.reduce((sum, item) => sum + (item.totalValue || 0), 0);
            
            document.getElementById('articleCount').textContent = inventory.length;
            document.getElementById('totalValue').textContent = Utils.formatPrice(totalValue);
        }

        // Écouter les changements de stockage pour mettre à jour l'affichage
        window.addEventListener('storage', () => {
            updateStats();
            showInventory();
        });

        // Simuler l'événement storage pour les changements locaux
        const originalSetItem = localStorage.setItem;
        localStorage.setItem = function(key, value) {
            originalSetItem.apply(this, arguments);
            if (key.startsWith('restomanager_')) {
                setTimeout(() => {
                    updateStats();
                    showInventory();
                }, 100);
            }
        };
    </script>
</body>
</html>
