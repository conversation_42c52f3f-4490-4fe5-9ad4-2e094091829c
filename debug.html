<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug RestoManager</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Debug RestoManager</h1>
    <div id="debug-output"></div>

    <!-- Scripts JavaScript -->
    <script src="assets/js/core/storage.js"></script>
    <script src="assets/js/core/utils.js"></script>
    
    <!-- Modules -->
    <script src="assets/js/modules/dashboard.js"></script>
    <script src="assets/js/modules/suppliers.js"></script>
    <script src="assets/js/modules/purchases.js"></script>
    <script src="assets/js/modules/inventory.js"></script>
    <script src="assets/js/modules/daily-outputs.js"></script>
    <script src="assets/js/modules/dishes.js"></script>
    <script src="assets/js/modules/recipes.js"></script>
    <script src="assets/js/modules/staff.js"></script>
    <script src="assets/js/modules/planning.js"></script>
    <script src="assets/js/modules/timetracking.js"></script>
    <script src="assets/js/modules/consolidation.js"></script>
    
    <!-- Application principale -->
    <script src="assets/js/core/app.js"></script>

    <script>
        const output = document.getElementById('debug-output');
        
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = message;
            output.appendChild(div);
        }

        // Test 1: Vérifier les modules dans window
        log('<h2>1. Test des Modules dans window</h2>');
        const expectedModules = [
            'DashboardModule',
            'SuppliersModule', 
            'PurchasesModule',
            'InventoryModule',
            'DailyOutputsModule',
            'DishesModule',
            'RecipesModule',
            'StaffModule',
            'PlanningModule',
            'TimetrackingModule',
            'ConsolidationModule'
        ];

        expectedModules.forEach(moduleName => {
            if (window[moduleName]) {
                log(`✅ ${moduleName} trouvé`, 'success');
            } else {
                log(`❌ ${moduleName} manquant`, 'error');
            }
        });

        // Test 2: Test de l'application
        log('<h2>2. Test de l\'Application</h2>');
        if (window.RestoManagerApp) {
            log('✅ RestoManagerApp trouvé', 'success');
            
            // Test de la méthode getModuleClassName
            const app = new RestoManagerApp();
            const testCases = [
                { input: 'inventory', expected: 'InventoryModule' },
                { input: 'daily-outputs', expected: 'DailyOutputsModule' },
                { input: 'dashboard', expected: 'DashboardModule' }
            ];
            
            testCases.forEach(test => {
                const result = app.getModuleClassName(test.input);
                if (result === test.expected) {
                    log(`✅ getModuleClassName('${test.input}') = '${result}'`, 'success');
                } else {
                    log(`❌ getModuleClassName('${test.input}') = '${result}', attendu '${test.expected}'`, 'error');
                }
            });
        } else {
            log('❌ RestoManagerApp manquant', 'error');
        }

        // Test 3: Test du stockage
        log('<h2>3. Test du Stockage</h2>');
        try {
            if (typeof storage !== 'undefined') {
                log('✅ Storage disponible', 'success');
                
                // Test basique
                storage.set('debug-test', { test: true });
                const data = storage.get('debug-test');
                if (data && data.test) {
                    log('✅ Storage fonctionne', 'success');
                } else {
                    log('❌ Storage ne fonctionne pas', 'error');
                }
                storage.delete('debug-test');
            } else {
                log('❌ Storage non disponible', 'error');
            }
        } catch (error) {
            log(`❌ Erreur storage: ${error.message}`, 'error');
        }

        // Test 4: Test des utilitaires
        log('<h2>4. Test des Utilitaires</h2>');
        if (typeof Utils !== 'undefined') {
            log('✅ Utils disponible', 'success');
            
            // Test de quelques méthodes
            if (typeof Utils.formatPrice === 'function') {
                const price = Utils.formatPrice(123.45);
                log(`✅ formatPrice(123.45) = '${price}'`, 'success');
            } else {
                log('❌ Utils.formatPrice manquant', 'error');
            }
        } else {
            log('❌ Utils non disponible', 'error');
        }

        // Test 5: Test d'instanciation des modules problématiques
        log('<h2>5. Test d\'Instanciation</h2>');
        
        // Test InventoryModule
        if (window.InventoryModule) {
            try {
                const inventory = new InventoryModule();
                log('✅ InventoryModule instancié', 'success');
                
                if (typeof inventory.init === 'function') {
                    log('✅ InventoryModule.init() disponible', 'success');
                } else {
                    log('❌ InventoryModule.init() manquant', 'error');
                }
                
                if (typeof inventory.render === 'function') {
                    log('✅ InventoryModule.render() disponible', 'success');
                } else {
                    log('❌ InventoryModule.render() manquant', 'error');
                }
            } catch (error) {
                log(`❌ Erreur InventoryModule: ${error.message}`, 'error');
            }
        }

        // Test DailyOutputsModule
        if (window.DailyOutputsModule) {
            try {
                const dailyOutputs = new DailyOutputsModule();
                log('✅ DailyOutputsModule instancié', 'success');
                
                if (typeof dailyOutputs.init === 'function') {
                    log('✅ DailyOutputsModule.init() disponible', 'success');
                } else {
                    log('❌ DailyOutputsModule.init() manquant', 'error');
                }
                
                if (typeof dailyOutputs.render === 'function') {
                    log('✅ DailyOutputsModule.render() disponible', 'success');
                } else {
                    log('❌ DailyOutputsModule.render() manquant', 'error');
                }
            } catch (error) {
                log(`❌ Erreur DailyOutputsModule: ${error.message}`, 'error');
            }
        }

        // Test 6: Simulation de chargement de module
        log('<h2>6. Test de Chargement de Module</h2>');
        if (window.RestoManagerApp && window.InventoryModule) {
            try {
                const app = new RestoManagerApp();
                const className = app.getModuleClassName('inventory');
                const moduleClass = window[className];
                
                if (moduleClass) {
                    log(`✅ Module 'inventory' trouvé comme '${className}'`, 'success');
                    
                    const instance = new moduleClass();
                    log('✅ Instance créée', 'success');
                    
                    if (typeof instance.init === 'function') {
                        log('✅ Méthode init() disponible', 'success');
                    }
                    
                    if (typeof instance.render === 'function') {
                        log('✅ Méthode render() disponible', 'success');
                    }
                } else {
                    log(`❌ Module '${className}' non trouvé`, 'error');
                }
            } catch (error) {
                log(`❌ Erreur simulation: ${error.message}`, 'error');
            }
        }

        log('<h2>Debug terminé</h2>', 'info');
    </script>
</body>
</html>
