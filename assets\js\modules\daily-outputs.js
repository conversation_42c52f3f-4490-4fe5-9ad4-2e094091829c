/**
 * RestoManager - Module Feuilles de Sorties Journalières
 * Enregistrement des sorties matières premières liées aux plats
 */

class DailyOutputsModule {
    constructor() {
        this.dailyOutputs = [];
        this.dishes = [];
        this.rawMaterials = [];
        this.recipes = [];
        this.currentDate = new Date().toISOString().split('T')[0];
        this.selectedDishes = [];
    }

    /**
     * Initialise le module
     */
    async init() {
        this.loadData();
    }

    /**
     * Charge les données depuis le stockage
     */
    loadData() {
        this.dailyOutputs = storage.get('dailyOutputs') || [];
        this.dishes = storage.get('dishes') || [];
        this.rawMaterials = storage.get('rawMaterials') || [];
        this.recipes = storage.get('recipes') || [];
    }

    /**
     * Rend le contenu du module
     */
    async render() {
        return `
            <div class="daily-outputs-container">
                <!-- En-tête -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card-neomorphic">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <h2 class="mb-0">
                                            <i class="fas fa-clipboard-list text-primary me-2"></i>
                                            Sorties Journalières
                                        </h2>
                                        <p class="text-muted mb-0">
                                            Gestion des sorties de matières premières
                                        </p>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <button class="btn btn-neomorphic btn-success me-2" onclick="dailyOutputsModule.showNewOutputModal()">
                                            <i class="fas fa-plus me-2"></i>
                                            Nouvelle Sortie
                                        </button>
                                        <button class="btn btn-neomorphic btn-info" onclick="dailyOutputsModule.exportOutputs()">
                                            <i class="fas fa-download me-2"></i>
                                            Export
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sélection de date et statistiques -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card-neomorphic">
                            <div class="card-body text-center">
                                <label class="form-label">Date sélectionnée</label>
                                <input type="date" class="form-control" id="selectedDate" value="${this.currentDate}">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-utensils"></i>
                            </div>
                            <div class="stat-value">${this.getTodayDishesCount()}</div>
                            <div class="stat-label">Plats Préparés</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-arrow-down"></i>
                            </div>
                            <div class="stat-value">${this.getTodayOutputsCount()}</div>
                            <div class="stat-label">Sorties Enregistrées</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-euro-sign"></i>
                            </div>
                            <div class="stat-value">${this.getTodayOutputsValue()}</div>
                            <div class="stat-label">Valeur Sortie</div>
                        </div>
                    </div>
                </div>

                <!-- Onglets -->
                <div class="row">
                    <div class="col-12">
                        <div class="card-neomorphic">
                            <div class="card-header">
                                <ul class="nav nav-tabs-neomorphic" role="tablist">
                                    <li class="nav-item">
                                        <a class="nav-link active" data-bs-toggle="tab" href="#outputsTab">
                                            <i class="fas fa-list me-1"></i>
                                            Sorties du Jour
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-bs-toggle="tab" href="#dishesTab">
                                            <i class="fas fa-utensils me-1"></i>
                                            Plats Préparés
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-bs-toggle="tab" href="#consolidationTab">
                                            <i class="fas fa-calculator me-1"></i>
                                            Consolidation
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <div class="card-body">
                                <div class="tab-content">
                                    <div class="tab-pane fade show active" id="outputsTab">
                                        ${this.renderOutputsList()}
                                    </div>
                                    <div class="tab-pane fade" id="dishesTab">
                                        ${this.renderDishesSelection()}
                                    </div>
                                    <div class="tab-pane fade" id="consolidationTab">
                                        ${this.renderConsolidation()}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modales -->
                ${this.renderOutputModal()}
            </div>
        `;
    }

    /**
     * Rend la liste des sorties
     */
    renderOutputsList() {
        const todayOutputs = this.getTodayOutputs();

        if (todayOutputs.length === 0) {
            return `
                <div class="text-center py-5">
                    <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Aucune sortie enregistrée</h5>
                    <p class="text-muted">Commencez par enregistrer les sorties du jour</p>
                    <button class="btn btn-neomorphic btn-success" onclick="dailyOutputsModule.showNewOutputModal()">
                        <i class="fas fa-plus me-2"></i>
                        Nouvelle Sortie
                    </button>
                </div>
            `;
        }

        return `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Heure</th>
                            <th>Article</th>
                            <th>Quantité</th>
                            <th>Plat Lié</th>
                            <th>Motif</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${todayOutputs.map(output => this.renderOutputRow(output)).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    /**
     * Rend une ligne de sortie
     */
    renderOutputRow(output) {
        const material = this.rawMaterials.find(m => m.id === output.materialId);
        const dish = this.dishes.find(d => d.id === output.dishId);

        return `
            <tr>
                <td>
                    <div class="fw-bold">${Utils.formatTime(output.date)}</div>
                </td>
                <td>
                    <div class="fw-bold">${material ? material.name : 'Article supprimé'}</div>
                    <small class="text-muted">${material ? material.unit : 'N/A'}</small>
                </td>
                <td>
                    <span class="fw-bold text-danger">-${Utils.formatNumber(output.quantity, 2)}</span>
                </td>
                <td>
                    ${dish ? `<span class="badge bg-info">${dish.name}</span>` : 'N/A'}
                </td>
                <td>
                    <small class="text-muted">${output.reason || 'Production'}</small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-warning" onclick="dailyOutputsModule.editOutput('${output.id}')" 
                                title="Modifier">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="dailyOutputsModule.deleteOutput('${output.id}')" 
                                title="Supprimer">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    /**
     * Rend la sélection de plats
     */
    renderDishesSelection() {
        if (this.dishes.length === 0) {
            return `
                <div class="text-center py-5">
                    <i class="fas fa-utensils fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Aucun plat disponible</h5>
                    <p class="text-muted">Créez d'abord des plats dans le module Menu</p>
                </div>
            `;
        }

        return `
            <div class="row mb-4">
                <div class="col-12">
                    <div class="alert alert-neomorphic alert-info">
                        <h6 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i>
                            Sélection de Plats
                        </h6>
                        <p class="mb-2">Sélectionnez les plats préparés aujourd'hui et leurs quantités.</p>
                        <p class="mb-0">Les sorties de matières premières seront calculées automatiquement selon les fiches techniques.</p>
                    </div>
                </div>
            </div>

            <div class="row">
                ${this.dishes.map(dish => this.renderDishCard(dish)).join('')}
            </div>

            <div class="row mt-4">
                <div class="col-12 text-center">
                    <button class="btn btn-neomorphic btn-primary btn-lg" onclick="dailyOutputsModule.generateOutputsFromDishes()">
                        <i class="fas fa-magic me-2"></i>
                        Générer les Sorties Automatiquement
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Rend une carte de plat
     */
    renderDishCard(dish) {
        const recipe = this.recipes.find(r => r.dishId === dish.id);
        const isSelected = this.selectedDishes.some(s => s.dishId === dish.id);

        return `
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card-neomorphic h-100 ${isSelected ? 'border-primary' : ''}">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="card-title mb-0">${dish.name}</h6>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" 
                                       id="dish_${dish.id}" 
                                       ${isSelected ? 'checked' : ''}
                                       onchange="dailyOutputsModule.toggleDishSelection('${dish.id}')">
                            </div>
                        </div>
                        
                        <p class="card-text text-muted small">${dish.description || 'Aucune description'}</p>
                        
                        <div class="row">
                            <div class="col-6">
                                <label class="form-label small">Quantité</label>
                                <input type="number" class="form-control form-control-sm" 
                                       id="quantity_${dish.id}" 
                                       min="0" step="1" value="1"
                                       ${!isSelected ? 'disabled' : ''}>
                            </div>
                            <div class="col-6">
                                <label class="form-label small">Portions</label>
                                <div class="fw-bold text-primary">${recipe ? recipe.portions || 1 : 1}</div>
                            </div>
                        </div>

                        ${recipe ? `
                            <div class="mt-2">
                                <small class="text-muted">
                                    <i class="fas fa-file-alt me-1"></i>
                                    Fiche technique disponible
                                </small>
                            </div>
                        ` : `
                            <div class="mt-2">
                                <small class="text-warning">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    Aucune fiche technique
                                </small>
                            </div>
                        `}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Rend la consolidation
     */
    renderConsolidation() {
        const consolidatedNeeds = this.calculateConsolidatedNeeds();

        if (Object.keys(consolidatedNeeds).length === 0) {
            return `
                <div class="text-center py-5">
                    <i class="fas fa-calculator fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Aucune consolidation disponible</h5>
                    <p class="text-muted">Sélectionnez des plats dans l'onglet précédent</p>
                </div>
            `;
        }

        return `
            <div class="row mb-4">
                <div class="col-12">
                    <div class="alert alert-neomorphic alert-success">
                        <h6 class="alert-heading">
                            <i class="fas fa-calculator me-2"></i>
                            Consolidation Automatique
                        </h6>
                        <p class="mb-0">Besoins en matières premières calculés automatiquement selon les plats sélectionnés.</p>
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Matière Première</th>
                            <th>Quantité Nécessaire</th>
                            <th>Unité</th>
                            <th>Stock Disponible</th>
                            <th>Statut</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${Object.entries(consolidatedNeeds).map(([materialId, need]) => 
                            this.renderConsolidationRow(materialId, need)
                        ).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    /**
     * Rend une ligne de consolidation
     */
    renderConsolidationRow(materialId, need) {
        const material = this.rawMaterials.find(m => m.id === materialId);
        const inventory = storage.get('inventory') || [];
        const stockItem = inventory.find(i => i.materialId === materialId);
        const availableStock = stockItem ? stockItem.quantity : 0;
        
        const isAvailable = availableStock >= need.quantity;
        const statusBadge = isAvailable ? 
            '<span class="badge bg-success">Disponible</span>' : 
            '<span class="badge bg-danger">Insuffisant</span>';

        return `
            <tr class="${!isAvailable ? 'table-warning' : ''}">
                <td>
                    <div class="fw-bold">${material ? material.name : 'Article supprimé'}</div>
                    <small class="text-muted">Utilisé dans: ${need.dishes.join(', ')}</small>
                </td>
                <td>
                    <span class="fw-bold text-primary">${Utils.formatNumber(need.quantity, 2)}</span>
                </td>
                <td>${material ? material.unit : 'N/A'}</td>
                <td>
                    <span class="fw-bold ${isAvailable ? 'text-success' : 'text-danger'}">
                        ${Utils.formatNumber(availableStock, 2)}
                    </span>
                </td>
                <td>${statusBadge}</td>
            </tr>
        `;
    }

    /**
     * Rend le modal de sortie
     */
    renderOutputModal() {
        return `
            <div class="modal fade" id="outputModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content modal-neomorphic">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-clipboard-list me-2"></i>
                                <span id="outputModalTitle">Nouvelle Sortie</span>
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="outputForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Date et heure *</label>
                                            <input type="datetime-local" class="form-control" id="outputDateTime" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Matière première *</label>
                                            <select class="form-select" id="outputMaterial" required>
                                                <option value="">Sélectionner</option>
                                                ${this.rawMaterials.map(m => `
                                                    <option value="${m.id}">${m.name} (${m.unit})</option>
                                                `).join('')}
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Quantité *</label>
                                            <input type="number" class="form-control" id="outputQuantity" 
                                                   min="0" step="0.01" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Plat lié</label>
                                            <select class="form-select" id="outputDish">
                                                <option value="">Aucun plat spécifique</option>
                                                ${this.dishes.map(d => `
                                                    <option value="${d.id}">${d.name}</option>
                                                `).join('')}
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Motif</label>
                                    <select class="form-select" id="outputReason">
                                        <option value="production">Production</option>
                                        <option value="tasting">Dégustation</option>
                                        <option value="waste">Gaspillage</option>
                                        <option value="loss">Perte</option>
                                        <option value="other">Autre</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Notes</label>
                                    <textarea class="form-control" id="outputNotes" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="button" class="btn btn-success" onclick="dailyOutputsModule.saveOutput()">
                                <i class="fas fa-save me-2"></i>
                                Enregistrer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Actions post-rendu
     */
    postRender() {
        this.attachEventListeners();
        window.dailyOutputsModule = this;

        // Initialiser la date/heure actuelle
        const now = new Date();
        const dateTimeInput = document.getElementById('outputDateTime');
        if (dateTimeInput) {
            dateTimeInput.value = now.toISOString().slice(0, 16);
        }
    }

    /**
     * Attache les gestionnaires d'événements
     */
    attachEventListeners() {
        // Changement de date
        const dateInput = document.getElementById('selectedDate');
        if (dateInput) {
            dateInput.addEventListener('change', (e) => {
                this.currentDate = e.target.value;
                this.refreshOutputsList();
            });
        }
    }

    // Méthodes utilitaires
    getTodayOutputs() {
        return this.dailyOutputs.filter(output =>
            output.date.startsWith(this.currentDate)
        );
    }

    getTodayDishesCount() {
        const todayOutputs = this.getTodayOutputs();
        const uniqueDishes = new Set(todayOutputs.map(o => o.dishId).filter(Boolean));
        return uniqueDishes.size;
    }

    getTodayOutputsCount() {
        return this.getTodayOutputs().length;
    }

    getTodayOutputsValue() {
        const todayOutputs = this.getTodayOutputs();
        const inventory = storage.get('inventory') || [];

        let totalValue = 0;
        todayOutputs.forEach(output => {
            const stockItem = inventory.find(i => i.materialId === output.materialId);
            if (stockItem) {
                totalValue += output.quantity * (stockItem.averagePrice || 0);
            }
        });

        return Utils.formatPrice(totalValue);
    }

    calculateConsolidatedNeeds() {
        const needs = {};

        this.selectedDishes.forEach(selectedDish => {
            const dish = this.dishes.find(d => d.id === selectedDish.dishId);
            const recipe = this.recipes.find(r => r.dishId === selectedDish.dishId);
            const quantity = selectedDish.quantity || 1;

            if (recipe && recipe.ingredients) {
                recipe.ingredients.forEach(ingredient => {
                    const totalNeeded = ingredient.quantity * quantity;

                    if (!needs[ingredient.materialId]) {
                        needs[ingredient.materialId] = {
                            quantity: 0,
                            dishes: []
                        };
                    }

                    needs[ingredient.materialId].quantity += totalNeeded;
                    if (!needs[ingredient.materialId].dishes.includes(dish.name)) {
                        needs[ingredient.materialId].dishes.push(dish.name);
                    }
                });
            }
        });

        return needs;
    }

    // Actions
    showNewOutputModal() {
        this.currentOutput = null;
        document.getElementById('outputModalTitle').textContent = 'Nouvelle Sortie';
        this.resetOutputForm();
        new bootstrap.Modal(document.getElementById('outputModal')).show();
    }

    toggleDishSelection(dishId) {
        const checkbox = document.getElementById(`dish_${dishId}`);
        const quantityInput = document.getElementById(`quantity_${dishId}`);

        if (checkbox.checked) {
            // Ajouter à la sélection
            this.selectedDishes.push({
                dishId: dishId,
                quantity: parseInt(quantityInput.value) || 1
            });
            quantityInput.disabled = false;
        } else {
            // Retirer de la sélection
            this.selectedDishes = this.selectedDishes.filter(s => s.dishId !== dishId);
            quantityInput.disabled = true;
        }

        // Mettre à jour la quantité si elle change
        quantityInput.addEventListener('change', () => {
            const selected = this.selectedDishes.find(s => s.dishId === dishId);
            if (selected) {
                selected.quantity = parseInt(quantityInput.value) || 1;
            }
        });

        // Rafraîchir la consolidation
        this.refreshConsolidation();
    }

    async generateOutputsFromDishes() {
        if (this.selectedDishes.length === 0) {
            Utils.showToast('Veuillez sélectionner au moins un plat', 'warning');
            return;
        }

        const confirmed = await Utils.confirm(
            'Générer automatiquement les sorties pour les plats sélectionnés ?',
            'Confirmer la génération'
        );

        if (!confirmed) return;

        try {
            const consolidatedNeeds = this.calculateConsolidatedNeeds();
            let generatedCount = 0;

            for (const [materialId, need] of Object.entries(consolidatedNeeds)) {
                // Créer une sortie pour chaque matière première
                const outputData = {
                    date: new Date().toISOString(),
                    materialId: materialId,
                    quantity: need.quantity,
                    reason: 'production',
                    notes: `Généré automatiquement pour: ${need.dishes.join(', ')}`,
                    dishId: null // Pas de plat spécifique car consolidé
                };

                storage.add('dailyOutputs', outputData);

                // Mettre à jour le stock
                this.updateInventoryFromOutput(outputData);
                generatedCount++;
            }

            Utils.showToast(`${generatedCount} sortie(s) générée(s) avec succès`, 'success');
            this.loadData();
            this.refreshOutputsList();

            // Réinitialiser la sélection
            this.selectedDishes = [];
            this.refreshDishesSelection();

        } catch (error) {
            console.error('Erreur lors de la génération:', error);
            Utils.showToast('Erreur lors de la génération des sorties', 'error');
        }
    }
