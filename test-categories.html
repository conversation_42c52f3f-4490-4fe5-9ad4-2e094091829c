<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Catégories - RestoManager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Test des Catégories dans tous les Modules</h1>
        
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle me-2"></i>Catégories Requises</h6>
            <p>Vérification que toutes les catégories sont disponibles :</p>
            <div class="row">
                <div class="col-md-6">
                    <ul>
                        <li>Viandes</li>
                        <li>Légumes</li>
                        <li>Volailles</li>
                        <li>Poissons</li>
                        <li>Produits laitiers</li>
                        <li>Épicerie</li>
                        <li>Alimentation générale</li>
                        <li>Achats divers</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <ul>
                        <li>Produits asiatiques</li>
                        <li>Charcuterie</li>
                        <li>Boulangerie</li>
                        <li>Pâtisserie</li>
                        <li>Emballage</li>
                        <li>Produits finis</li>
                        <li>Boissons</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Catégories dans Storage</h5>
                    </div>
                    <div class="card-body">
                        <div id="storageCategories">
                            <p class="text-muted">Chargement...</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test des Modules</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <button class="btn btn-primary me-2" onclick="testPurchasesModule()">
                                <i class="fas fa-shopping-cart me-2"></i>Test Achats
                            </button>
                            <button class="btn btn-success me-2" onclick="testInventoryModule()">
                                <i class="fas fa-boxes me-2"></i>Test Inventaire
                            </button>
                            <button class="btn btn-info" onclick="testSuppliersModule()">
                                <i class="fas fa-truck me-2"></i>Test Fournisseurs
                            </button>
                        </div>
                        <div id="moduleResults">
                            <p class="text-muted">Aucun test effectué</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Résultats des Tests</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults">
                            <p class="text-muted">Aucun résultat</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="assets/js/core/storage.js"></script>
    <script src="assets/js/core/utils.js"></script>
    <script src="assets/js/modules/purchases.js"></script>
    <script src="assets/js/modules/inventory.js"></script>
    <script src="assets/js/modules/suppliers.js"></script>

    <script>
        const requiredCategories = [
            'Viandes', 'Légumes', 'Volailles', 'Poissons', 'Produits laitiers',
            'Épicerie', 'Alimentation générale', 'Achats divers', 'Produits asiatiques',
            'Charcuterie', 'Boulangerie', 'Pâtisserie', 'Emballage', 'Produits finis', 'Boissons'
        ];

        let testResults = [];

        // Initialiser au chargement
        window.addEventListener('load', () => {
            displayStorageCategories();
        });

        function displayStorageCategories() {
            const categories = storage.get('materialCategories') || [];
            
            let html = '<div class="row">';
            categories.forEach(category => {
                const isRequired = requiredCategories.includes(category.name);
                const badgeClass = isRequired ? 'bg-success' : 'bg-warning';
                const icon = isRequired ? 'fa-check' : 'fa-question';
                
                html += `
                    <div class="col-md-6 mb-2">
                        <div class="d-flex align-items-center">
                            <span class="badge ${badgeClass} me-2">
                                <i class="fas ${icon}"></i>
                            </span>
                            <span style="color: ${category.color}; font-weight: bold;">
                                ${category.name}
                            </span>
                            <small class="text-muted ms-2">(ID: ${category.id})</small>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            
            html += `<div class="mt-3">
                <p><strong>Total :</strong> ${categories.length} catégories</p>
                <p><strong>Requises :</strong> ${requiredCategories.length} catégories</p>
                <p><strong>Manquantes :</strong> ${getMissingCategories(categories).length} catégories</p>
            </div>`;
            
            document.getElementById('storageCategories').innerHTML = html;
        }

        function getMissingCategories(categories) {
            const existingNames = categories.map(c => c.name);
            return requiredCategories.filter(name => !existingNames.includes(name));
        }

        async function testPurchasesModule() {
            try {
                const purchasesModule = new PurchasesModule();
                await purchasesModule.init();
                
                const categories = purchasesModule.categories || [];
                const missing = getMissingCategories(categories);
                
                const result = {
                    module: 'Achats',
                    success: missing.length === 0,
                    categoriesCount: categories.length,
                    missing: missing,
                    message: missing.length === 0 ? 
                        `✅ Toutes les catégories sont disponibles (${categories.length})` :
                        `❌ ${missing.length} catégorie(s) manquante(s): ${missing.join(', ')}`
                };
                
                testResults.push(result);
                updateTestResults();
                
            } catch (error) {
                testResults.push({
                    module: 'Achats',
                    success: false,
                    error: error.message,
                    message: `❌ Erreur: ${error.message}`
                });
                updateTestResults();
            }
        }

        async function testInventoryModule() {
            try {
                const inventoryModule = new InventoryModule();
                await inventoryModule.init();
                
                const categories = inventoryModule.categories || [];
                const missing = getMissingCategories(categories);
                
                const result = {
                    module: 'Inventaire',
                    success: missing.length === 0,
                    categoriesCount: categories.length,
                    missing: missing,
                    message: missing.length === 0 ? 
                        `✅ Toutes les catégories sont disponibles (${categories.length})` :
                        `❌ ${missing.length} catégorie(s) manquante(s): ${missing.join(', ')}`
                };
                
                testResults.push(result);
                updateTestResults();
                
            } catch (error) {
                testResults.push({
                    module: 'Inventaire',
                    success: false,
                    error: error.message,
                    message: `❌ Erreur: ${error.message}`
                });
                updateTestResults();
            }
        }

        async function testSuppliersModule() {
            try {
                const suppliersModule = new SuppliersModule();
                await suppliersModule.init();
                
                // Pour les fournisseurs, on teste en créant le HTML et en vérifiant les options
                const modalHtml = suppliersModule.renderModal();
                
                // Extraire les options du select
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = modalHtml;
                const selectElement = tempDiv.querySelector('#supplierType');
                const options = Array.from(selectElement.options).map(option => option.value).filter(v => v);
                
                const missing = requiredCategories.filter(cat => !options.includes(cat));
                
                const result = {
                    module: 'Fournisseurs',
                    success: missing.length === 0,
                    categoriesCount: options.length,
                    missing: missing,
                    options: options,
                    message: missing.length === 0 ? 
                        `✅ Toutes les catégories sont disponibles (${options.length})` :
                        `❌ ${missing.length} catégorie(s) manquante(s): ${missing.join(', ')}`
                };
                
                testResults.push(result);
                updateTestResults();
                
            } catch (error) {
                testResults.push({
                    module: 'Fournisseurs',
                    success: false,
                    error: error.message,
                    message: `❌ Erreur: ${error.message}`
                });
                updateTestResults();
            }
        }

        function updateTestResults() {
            let html = '';
            
            testResults.forEach(result => {
                const alertClass = result.success ? 'alert-success' : 'alert-danger';
                
                html += `
                    <div class="alert ${alertClass}">
                        <h6><strong>Module ${result.module}</strong></h6>
                        <p>${result.message}</p>
                        ${result.missing && result.missing.length > 0 ? `
                            <small>Catégories manquantes: ${result.missing.join(', ')}</small>
                        ` : ''}
                        ${result.options ? `
                            <details class="mt-2">
                                <summary>Voir toutes les options (${result.options.length})</summary>
                                <small>${result.options.join(', ')}</small>
                            </details>
                        ` : ''}
                    </div>
                `;
            });
            
            if (testResults.length === 0) {
                html = '<p class="text-muted">Aucun test effectué</p>';
            }
            
            document.getElementById('testResults').innerHTML = html;
            
            // Mettre à jour le résumé dans moduleResults
            const successCount = testResults.filter(r => r.success).length;
            const totalCount = testResults.length;
            
            if (totalCount > 0) {
                const summaryClass = successCount === totalCount ? 'text-success' : 'text-warning';
                document.getElementById('moduleResults').innerHTML = `
                    <p class="${summaryClass}">
                        <strong>${successCount}/${totalCount}</strong> modules testés avec succès
                    </p>
                `;
            }
        }

        // Test automatique au chargement
        setTimeout(() => {
            console.log('Lancement des tests automatiques...');
            testPurchasesModule();
            setTimeout(() => testInventoryModule(), 500);
            setTimeout(() => testSuppliersModule(), 1000);
        }, 1000);
    </script>
</body>
</html>
