<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Structure Fournisseurs</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Debug Structure Fournisseurs et Matières</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Fournisseurs</h5>
                    </div>
                    <div class="card-body">
                        <div id="suppliers">
                            <p class="text-muted">Chargement...</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Catégories de Matières</h5>
                    </div>
                    <div class="card-body">
                        <div id="categories">
                            <p class="text-muted">Chargement...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Mapping Fournisseur → Catégories</h5>
                    </div>
                    <div class="card-body">
                        <div id="mapping">
                            <p class="text-muted">Chargement...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Filtrage</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <label class="form-label">Fournisseur</label>
                                <select class="form-select" id="testSupplier" onchange="filterMaterials()">
                                    <option value="">Sélectionner</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Type du fournisseur</label>
                                <input type="text" class="form-control" id="supplierType" readonly>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Matières filtrées</label>
                                <input type="text" class="form-control" id="filteredCount" readonly>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <label class="form-label">Matières premières correspondantes</label>
                            <select class="form-select" id="filteredMaterials" size="5">
                                <option value="">Aucun fournisseur sélectionné</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/core/storage.js"></script>
    <script src="assets/js/core/utils.js"></script>

    <script>
        window.addEventListener('load', () => {
            storage.init();
            loadData();
        });

        function loadData() {
            const suppliers = storage.get('suppliers') || [];
            const categories = storage.get('materialCategories') || [];
            const rawMaterials = storage.get('rawMaterials') || [];
            
            // Afficher les fournisseurs
            let suppliersHtml = '<div class="table-responsive"><table class="table table-sm"><thead><tr><th>Nom</th><th>Type</th></tr></thead><tbody>';
            suppliers.forEach(supplier => {
                suppliersHtml += `<tr><td><strong>${supplier.name}</strong></td><td>${supplier.type || 'N/A'}</td></tr>`;
            });
            suppliersHtml += '</tbody></table></div>';
            document.getElementById('suppliers').innerHTML = suppliersHtml;
            
            // Afficher les catégories
            let categoriesHtml = '<div class="table-responsive"><table class="table table-sm"><thead><tr><th>Nom</th><th>Couleur</th><th>Articles</th></tr></thead><tbody>';
            categories.forEach(category => {
                const materialsCount = rawMaterials.filter(m => m.categoryId === category.id).length;
                categoriesHtml += `<tr>
                    <td><strong>${category.name}</strong></td>
                    <td><span class="badge" style="background-color: ${category.color}">${category.color}</span></td>
                    <td>${materialsCount}</td>
                </tr>`;
            });
            categoriesHtml += '</tbody></table></div>';
            document.getElementById('categories').innerHTML = categoriesHtml;
            
            // Créer le mapping
            createMapping();
            
            // Remplir le select de test
            const testSelect = document.getElementById('testSupplier');
            testSelect.innerHTML = '<option value="">Sélectionner</option>';
            suppliers.forEach(supplier => {
                const option = document.createElement('option');
                option.value = supplier.id;
                option.dataset.type = supplier.type || '';
                option.textContent = `${supplier.name} (${supplier.type || 'Sans type'})`;
                testSelect.appendChild(option);
            });
        }

        function createMapping() {
            const suppliers = storage.get('suppliers') || [];
            const categories = storage.get('materialCategories') || [];
            
            // Créer un mapping logique entre types de fournisseurs et catégories
            const typeMapping = {
                'Alimentation générale': ['Épicerie', 'Condiments'],
                'Légumes et fruits': ['Légumes', 'Fruits'],
                'Viandes': ['Viandes'],
                'Poissons': ['Poissons'],
                'Produits laitiers': ['Produits laitiers'],
                'Boulangerie': ['Boulangerie'],
                'Boissons': ['Boissons']
            };
            
            let mappingHtml = '<div class="table-responsive"><table class="table table-sm"><thead><tr><th>Type Fournisseur</th><th>Catégories Correspondantes</th><th>Fournisseurs</th></tr></thead><tbody>';
            
            Object.keys(typeMapping).forEach(type => {
                const relatedSuppliers = suppliers.filter(s => s.type === type);
                const relatedCategories = typeMapping[type];
                
                mappingHtml += `<tr>
                    <td><strong>${type}</strong></td>
                    <td>${relatedCategories.join(', ')}</td>
                    <td>${relatedSuppliers.map(s => s.name).join(', ') || 'Aucun'}</td>
                </tr>`;
            });
            
            mappingHtml += '</tbody></table></div>';
            
            mappingHtml += `
                <div class="alert alert-info mt-3">
                    <h6>Logique de filtrage proposée :</h6>
                    <ul>
                        <li><strong>Alimentation générale</strong> → Épicerie, Condiments</li>
                        <li><strong>Légumes et fruits</strong> → Légumes, Fruits</li>
                        <li><strong>Viandes</strong> → Viandes</li>
                        <li><strong>Poissons</strong> → Poissons</li>
                        <li><strong>Produits laitiers</strong> → Produits laitiers</li>
                    </ul>
                </div>
            `;
            
            document.getElementById('mapping').innerHTML = mappingHtml;
        }

        function filterMaterials() {
            const supplierSelect = document.getElementById('testSupplier');
            const selectedOption = supplierSelect.selectedOptions[0];
            const typeInput = document.getElementById('supplierType');
            const countInput = document.getElementById('filteredCount');
            const materialsSelect = document.getElementById('filteredMaterials');
            
            if (!selectedOption || !selectedOption.value) {
                typeInput.value = '';
                countInput.value = '';
                materialsSelect.innerHTML = '<option value="">Aucun fournisseur sélectionné</option>';
                return;
            }
            
            const supplierType = selectedOption.dataset.type;
            typeInput.value = supplierType;
            
            // Mapping des types vers les catégories
            const typeMapping = {
                'Alimentation générale': ['Épicerie', 'Condiments'],
                'Légumes et fruits': ['Légumes', 'Fruits'],
                'Viandes': ['Viandes'],
                'Poissons': ['Poissons'],
                'Produits laitiers': ['Produits laitiers'],
                'Boulangerie': ['Boulangerie'],
                'Boissons': ['Boissons']
            };
            
            const allowedCategories = typeMapping[supplierType] || [];
            
            // Filtrer les matières premières
            const rawMaterials = storage.get('rawMaterials') || [];
            const categories = storage.get('materialCategories') || [];
            const inventory = storage.get('inventory') || [];
            
            const allowedCategoryIds = categories
                .filter(cat => allowedCategories.includes(cat.name))
                .map(cat => cat.id);
            
            const filteredMaterials = rawMaterials.filter(material => 
                allowedCategoryIds.includes(material.categoryId)
            );
            
            countInput.value = `${filteredMaterials.length} / ${rawMaterials.length}`;
            
            // Remplir le select des matières filtrées
            materialsSelect.innerHTML = '';
            if (filteredMaterials.length === 0) {
                materialsSelect.innerHTML = '<option value="">Aucune matière correspondante</option>';
            } else {
                filteredMaterials.forEach(material => {
                    const category = categories.find(c => c.id === material.categoryId);
                    const invItem = inventory.find(i => i.materialId === material.id);
                    const price = invItem ? (invItem.unitPrice || invItem.averagePrice || 0) : 0;
                    const priceInfo = price > 0 ? ` - ${Utils.formatPrice(price)}` : '';
                    
                    const option = document.createElement('option');
                    option.value = material.id;
                    option.textContent = `${material.name} (${category?.name || 'N/A'})${priceInfo}`;
                    materialsSelect.appendChild(option);
                });
            }
        }
    </script>
</body>
</html>
