<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Application complète de gestion de restaurant">
    <meta name="theme-color" content="#2c3e50">
    
    <!-- PWA Configuration -->
    <link rel="manifest" href="manifest.json">
    <link rel="icon" type="image/png" sizes="192x192" href="assets/icons/icon-192x192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="assets/icons/icon-512x512.png">
    <link rel="apple-touch-icon" href="assets/icons/icon-192x192.png">
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/neumorphic.css">
    
    <title>RestoManager - Gestion de Restaurant</title>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loadingScreen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">
                <i class="fas fa-utensils"></i>
            </div>
            <h3>RestoManager</h3>
            <div class="loading-spinner"></div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-utensils me-2"></i>
                RestoManager
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#" data-module="dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>Tableau de Bord
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-boxes me-1"></i>Stock & Achats
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" data-module="suppliers">
                                <i class="fas fa-truck me-1"></i>Fournisseurs
                            </a></li>
                            <li><a class="dropdown-item" href="#" data-module="purchases">
                                <i class="fas fa-shopping-cart me-1"></i>Achats
                            </a></li>
                            <li><a class="dropdown-item" href="#" data-module="inventory">
                                <i class="fas fa-warehouse me-1"></i>Inventaire
                            </a></li>
                            <li><a class="dropdown-item" href="#" data-module="daily-outputs">
                                <i class="fas fa-clipboard-list me-1"></i>Sorties Journalières
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-utensils me-1"></i>Menu & Plats
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" data-module="dishes">
                                <i class="fas fa-hamburger me-1"></i>Gestion des Plats
                            </a></li>
                            <li><a class="dropdown-item" href="#" data-module="recipes">
                                <i class="fas fa-file-alt me-1"></i>Fiches Techniques
                            </a></li>
                            <li><a class="dropdown-item" href="#" data-module="consolidation">
                                <i class="fas fa-calculator me-1"></i>Consolidation
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-users me-1"></i>Personnel
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" data-module="staff">
                                <i class="fas fa-user-friends me-1"></i>Gestion Personnel
                            </a></li>
                            <li><a class="dropdown-item" href="#" data-module="planning">
                                <i class="fas fa-calendar-alt me-1"></i>Planning
                            </a></li>
                            <li><a class="dropdown-item" href="#" data-module="timetracking">
                                <i class="fas fa-clock me-1"></i>Pointage
                            </a></li>
                        </ul>
                    </li>
                </ul>
                
                <div class="navbar-nav">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog me-1"></i>Paramètres
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#" data-action="export-data">
                                <i class="fas fa-download me-1"></i>Exporter Données
                            </a></li>
                            <li><a class="dropdown-item" href="#" data-action="import-data">
                                <i class="fas fa-upload me-1"></i>Importer Données
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" data-action="backup">
                                <i class="fas fa-save me-1"></i>Sauvegarde
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid">
            <!-- Module Content will be loaded here -->
            <div id="moduleContent">
                <!-- Dashboard will be loaded by default -->
            </div>
        </div>
    </main>

    <!-- Toast Container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="liveToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <i class="fas fa-info-circle text-primary me-2"></i>
                <strong class="me-auto">RestoManager</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                <!-- Toast message will be inserted here -->
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- SheetJS pour lire les fichiers Excel -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    
    <!-- Core JavaScript -->
    <script src="assets/js/core/storage.js"></script>
    <script src="assets/js/core/utils.js"></script>
    <script src="assets/js/core/app.js"></script>
    
    <!-- Module Scripts -->
    <script src="assets/js/modules/dashboard.js"></script>
    <script src="assets/js/modules/suppliers.js"></script>
    <script src="assets/js/modules/purchases.js"></script>
    <script src="assets/js/modules/inventory.js"></script>
    <script src="assets/js/modules/daily-outputs.js"></script>
    <script src="assets/js/modules/dishes.js"></script>
    <script src="assets/js/modules/recipes.js"></script>
    <script src="assets/js/modules/consolidation.js"></script>
    <script src="assets/js/modules/staff.js"></script>
    <script src="assets/js/modules/planning.js"></script>
    <script src="assets/js/modules/timetracking.js"></script>
    
    <!-- PWA Service Worker -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    </script>
</body>
</html>
