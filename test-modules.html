<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Modules - RestoManager</title>
</head>
<body>
    <h1>Test des Modules RestoManager</h1>
    <div id="results"></div>

    <!-- Scripts JavaScript -->
    <script src="assets/js/core/storage.js"></script>
    <script src="assets/js/core/utils.js"></script>
    
    <!-- Modules -->
    <script src="assets/js/modules/dashboard.js"></script>
    <script src="assets/js/modules/suppliers.js"></script>
    <script src="assets/js/modules/purchases.js"></script>
    <script src="assets/js/modules/inventory.js"></script>
    <script src="assets/js/modules/daily-outputs.js"></script>
    <script src="assets/js/modules/dishes.js"></script>
    <script src="assets/js/modules/recipes.js"></script>
    <script src="assets/js/modules/staff.js"></script>
    <script src="assets/js/modules/planning.js"></script>
    <script src="assets/js/modules/timetracking.js"></script>
    <script src="assets/js/modules/consolidation.js"></script>

    <script>
        // Test des modules
        const modules = [
            'DashboardModule',
            'SuppliersModule', 
            'PurchasesModule',
            'InventoryModule',
            'DailyOutputsModule',
            'DishesModule',
            'RecipesModule',
            'StaffModule',
            'PlanningModule',
            'TimetrackingModule',
            'ConsolidationModule'
        ];

        const results = document.getElementById('results');
        let html = '<h2>Résultats des Tests</h2><ul>';

        modules.forEach(moduleName => {
            const moduleClass = window[moduleName];
            if (moduleClass) {
                html += `<li style="color: green;">✅ ${moduleName} - Chargé avec succès</li>`;
                
                // Test d'instanciation
                try {
                    const instance = new moduleClass();
                    html += `<li style="color: blue;">  ↳ Instance créée avec succès</li>`;
                } catch (error) {
                    html += `<li style="color: orange;">  ↳ Erreur d'instanciation: ${error.message}</li>`;
                }
            } else {
                html += `<li style="color: red;">❌ ${moduleName} - Non trouvé dans window</li>`;
            }
        });

        html += '</ul>';
        
        // Test du stockage
        html += '<h2>Test du Stockage</h2><ul>';
        try {
            storage.set('test', { message: 'Test réussi' });
            const testData = storage.get('test');
            if (testData && testData.message === 'Test réussi') {
                html += '<li style="color: green;">✅ LocalStorage fonctionne</li>';
            } else {
                html += '<li style="color: red;">❌ LocalStorage ne fonctionne pas correctement</li>';
            }
            storage.delete('test');
        } catch (error) {
            html += `<li style="color: red;">❌ Erreur LocalStorage: ${error.message}</li>`;
        }
        html += '</ul>';

        results.innerHTML = html;
    </script>
</body>
</html>
