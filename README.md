# RestoManager 🍽️

Application complète de gestion de restaurant développée en HTML5, CSS3 et JavaScript ES6 avec design Neumorphic moderne.

## 🚀 Fonctionnalités

### 📦 Gestion des Stocks & Achats
- **Fournisseurs** : Gestion complète des fournisseurs avec historique
- **Achats & Matières Premières** : Interface d'achat avec catégorisation
- **Inventaire** : Suivi en temps réel avec alertes de rupture de stock
- **Sorties Journalières** : Enregistrement des sorties liées aux plats

### 🍽️ Menu & Plats
- **Gestion des Plats** : Menu complet avec catégories
- **Fiches Techniques** : Ingrédients, coûts et calculs automatiques
- **Consolidation Automatique** : Calcul des besoins en matières premières

### 👥 Personnel
- **Gestion du Personnel** : Fiches complètes des employés
- **Planning** : Création de plannings avec shifts
- **Pointage** : Système de pointage et calcul des heures

## 🎨 Design & Interface

- **Style Neumorphic** moderne avec ombres douces
- **Bootstrap 5** pour les composants responsives
- **Font Awesome** pour les icônes
- **Design responsive** (mobile, tablette, desktop)
- **PWA** (Progressive Web App) compatible

## 💾 Stockage

- **LocalStorage** pour l'enregistrement local
- **Import/Export** JSON et CSV
- **Sauvegarde automatique**
- **Structure modulaire** extensible

## 🛠️ Installation

1. **Cloner ou télécharger** le projet
2. **Ouvrir** `index.html` dans un navigateur moderne
3. **Aucune installation** requise - fonctionne hors ligne

### Installation PWA (optionnel)

1. Ouvrir l'application dans Chrome/Edge/Safari
2. Cliquer sur "Installer l'application" dans la barre d'adresse
3. L'application sera disponible comme une app native

## 📱 Compatibilité

- ✅ **Windows** (Chrome, Edge, Firefox)
- ✅ **Android** (Chrome, Samsung Internet)
- ✅ **iOS** (Safari, Chrome)
- ✅ **macOS** (Safari, Chrome, Firefox)
- ✅ **Linux** (Chrome, Firefox)

## 🏗️ Structure du Projet

```
RestoManager/
├── index.html                 # Page principale
├── manifest.json             # Configuration PWA
├── sw.js                     # Service Worker
├── assets/
│   ├── css/
│   │   ├── style.css         # Styles principaux
│   │   └── neumorphic.css    # Styles Neumorphic
│   ├── js/
│   │   ├── core/
│   │   │   ├── app.js        # Application principale
│   │   │   ├── storage.js    # Gestion LocalStorage
│   │   │   └── utils.js      # Utilitaires
│   │   └── modules/
│   │       ├── dashboard.js  # Tableau de bord
│   │       ├── suppliers.js  # Gestion fournisseurs
│   │       ├── purchases.js  # Gestion achats
│   │       ├── inventory.js  # Gestion inventaire
│   │       ├── dishes.js     # Gestion plats
│   │       ├── recipes.js    # Fiches techniques
│   │       ├── staff.js      # Gestion personnel
│   │       ├── planning.js   # Planning
│   │       └── timetracking.js # Pointage
│   └── icons/
│       └── icon.svg          # Icône de l'application
└── README.md                 # Documentation
```

## 🔧 Utilisation

### Premier Lancement

1. **Tableau de Bord** : Vue d'ensemble avec statistiques
2. **Configuration** : Paramétrer les catégories et unités
3. **Fournisseurs** : Ajouter vos fournisseurs
4. **Stock** : Initialiser votre inventaire

### Workflow Quotidien

1. **Matin** : Vérifier les alertes de stock
2. **Achats** : Enregistrer les livraisons
3. **Production** : Enregistrer les sorties journalières
4. **Planning** : Gérer les équipes et horaires

## 📊 Fonctionnalités Avancées

### Consolidation Automatique
- Sélection de plats à préparer
- Calcul automatique des quantités d'ingrédients
- Génération de listes de courses

### Alertes Intelligentes
- Rupture de stock automatique
- Dates de péremption
- Plannings incomplets

### Export/Import
- Sauvegarde complète en JSON
- Export CSV pour comptabilité
- Import de données externes

## 🔒 Sécurité & Données

- **Stockage local** : Aucune donnée envoyée sur internet
- **Sauvegarde** : Export manuel des données
- **Confidentialité** : Contrôle total de vos données

## 🚀 Évolutions Futures

- [ ] Synchronisation cloud (Firebase)
- [ ] Mode multi-restaurant
- [ ] Intégration comptabilité
- [ ] Application mobile native
- [ ] Système de commandes en ligne

## 🆘 Support

### Problèmes Courants

**L'application ne se charge pas :**
- Vérifier que JavaScript est activé
- Utiliser un navigateur moderne
- Vider le cache du navigateur

**Données perdues :**
- Vérifier les sauvegardes automatiques
- Utiliser la fonction d'import/export

**Performance lente :**
- Nettoyer les anciennes données
- Exporter et réinitialiser si nécessaire

### Contact

Pour toute question ou suggestion :
- Créer une issue sur le projet
- Consulter la documentation en ligne

## 📄 Licence

Ce projet est sous licence MIT. Libre d'utilisation pour usage commercial et personnel.

---

**RestoManager** - Votre solution complète de gestion de restaurant 🍽️
