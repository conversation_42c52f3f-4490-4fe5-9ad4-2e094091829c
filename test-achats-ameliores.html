<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Achats Améliorés - RestoManager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Test des Améliorations Achats</h1>
        
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle me-2"></i>Nouvelles Fonctionnalités</h6>
            <ul class="mb-0">
                <li>✅ <strong>Devise en Dirhams (DH)</strong> - Tous les prix affichés en DH</li>
                <li>✅ <strong>Auto-remplissage des prix</strong> - Prix récupérés depuis l'inventaire</li>
                <li>✅ <strong>Bon de commande</strong> - Téléchargement après enregistrement</li>
            </ul>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test de la Devise</h5>
                    </div>
                    <div class="card-body">
                        <div id="currencyTest">
                            <p class="text-muted">Test en cours...</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test des Prix Inventaire</h5>
                    </div>
                    <div class="card-body">
                        <div id="inventoryTest">
                            <p class="text-muted">Test en cours...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Actions de Test</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <button class="btn btn-primary me-2" onclick="createTestData()">
                                <i class="fas fa-database me-2"></i>Créer Données Test
                            </button>
                            
                            <button class="btn btn-success me-2" onclick="testPurchaseModule()">
                                <i class="fas fa-shopping-cart me-2"></i>Tester Module Achats
                            </button>
                            
                            <button class="btn btn-warning me-2" onclick="reloadPurchaseModule()">
                                <i class="fas fa-sync me-2"></i>Recharger Module
                            </button>

                            <button class="btn btn-info" onclick="openMainApp()">
                                <i class="fas fa-external-link-alt me-2"></i>Ouvrir App Principale
                            </button>
                        </div>
                        
                        <div id="testResults">
                            <p class="text-muted">Aucun test effectué</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Données de Test</h5>
                    </div>
                    <div class="card-body">
                        <div id="testData">
                            <p class="text-muted">Aucune donnée</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/core/storage.js"></script>
    <script src="assets/js/core/utils.js"></script>
    <script src="assets/js/modules/purchases.js"></script>

    <script>
        let purchasesModule;

        // Initialiser au chargement
        window.addEventListener('load', async () => {
            await initializeModule();
            testCurrency();
            testInventoryPrices();
            displayTestData();
        });

        async function initializeModule() {
            try {
                purchasesModule = new PurchasesModule();
                await purchasesModule.init();
                console.log('Module achats initialisé');
            } catch (error) {
                console.error('Erreur initialisation:', error);
            }
        }

        function testCurrency() {
            const testPrices = [10.50, 125.75, 1250.00];
            
            let html = '<h6>Test du formatage des prix :</h6>';
            testPrices.forEach(price => {
                const formatted = Utils.formatPrice(price);
                html += `<p>${price} → <strong>${formatted}</strong></p>`;
            });
            
            // Test de la configuration
            const config = storage.get('config') || {};
            html += `<div class="mt-3 alert alert-${config.currency === 'MAD' ? 'success' : 'warning'}">`;
            html += `<strong>Configuration:</strong> ${config.currency || 'Non définie'} - ${config.currencySymbol || 'N/A'}`;
            html += '</div>';
            
            document.getElementById('currencyTest').innerHTML = html;
        }

        function testInventoryPrices() {
            const inventory = storage.get('inventory') || [];
            const rawMaterials = storage.get('rawMaterials') || [];
            
            let html = '<h6>Articles avec prix dans l\'inventaire :</h6>';
            
            if (inventory.length === 0) {
                html += '<p class="text-warning">Aucun article dans l\'inventaire</p>';
            } else {
                html += '<div class="table-responsive"><table class="table table-sm">';
                html += '<thead><tr><th>Article</th><th>Prix Unitaire</th><th>Stock</th></tr></thead><tbody>';
                
                inventory.slice(0, 10).forEach(item => {
                    const material = rawMaterials.find(m => m.id === item.materialId);
                    html += `<tr>
                        <td>${material ? material.name : 'Inconnu'}</td>
                        <td>${Utils.formatPrice(item.unitPrice || 0)}</td>
                        <td>${item.quantity || 0} ${material ? material.unit : ''}</td>
                    </tr>`;
                });
                
                html += '</tbody></table></div>';
                
                if (inventory.length > 10) {
                    html += `<p class="text-muted">... et ${inventory.length - 10} autres articles</p>`;
                }
            }
            
            document.getElementById('inventoryTest').innerHTML = html;
        }

        function createTestData() {
            // Créer des matières premières de test
            const testMaterials = [
                { name: 'Ail', unit: 'kilogramme', categoryId: 2 },
                { name: 'Aubergine', unit: 'kilogramme', categoryId: 2 },
                { name: 'Betterave', unit: 'kilogramme', categoryId: 2 },
                { name: 'Tomates', unit: 'kilogramme', categoryId: 2 },
                { name: 'Poulet', unit: 'kilogramme', categoryId: 3 }
            ];

            // D'abord créer les matières premières
            const createdMaterials = [];
            testMaterials.forEach(material => {
                const existing = storage.get('rawMaterials').find(m => m.name === material.name);
                if (!existing) {
                    const newMaterial = storage.add('rawMaterials', {
                        ...material,
                        description: 'Article de test',
                        createdAt: new Date().toISOString()
                    });
                    createdMaterials.push(newMaterial);
                } else {
                    createdMaterials.push(existing);
                }
            });

            // Créer des données d'inventaire avec prix en utilisant les vrais IDs
            const testInventory = [
                { materialName: 'Ail', quantity: 50, unitPrice: 25.50 },
                { materialName: 'Aubergine', quantity: 30, unitPrice: 8.75 },
                { materialName: 'Betterave', quantity: 40, unitPrice: 6.25 },
                { materialName: 'Tomates', quantity: 60, unitPrice: 12.50 },
                { materialName: 'Poulet', quantity: 25, unitPrice: 45.00 }
            ];

            // Nettoyer l'inventaire existant pour éviter les doublons
            const currentInventory = storage.get('inventory') || [];
            const rawMaterials = storage.get('rawMaterials') || [];

            testInventory.forEach(invItem => {
                const material = rawMaterials.find(m => m.name === invItem.materialName);
                if (material) {
                    // Supprimer l'entrée existante si elle existe
                    const existingIndex = currentInventory.findIndex(i => i.materialId === material.id);
                    if (existingIndex !== -1) {
                        currentInventory.splice(existingIndex, 1);
                    }

                    // Ajouter la nouvelle entrée
                    const newInventoryItem = {
                        materialId: material.id,
                        quantity: invItem.quantity,
                        averagePrice: invItem.unitPrice,
                        unitPrice: invItem.unitPrice, // Pour compatibilité
                        totalValue: invItem.quantity * invItem.unitPrice,
                        lastUpdated: new Date().toISOString()
                    };

                    currentInventory.push(newInventoryItem);
                }
            });

            // Sauvegarder l'inventaire mis à jour
            storage.set('inventory', currentInventory);

            // Créer un fournisseur de test
            const testSupplier = {
                name: 'SALIPRO',
                type: 'Alimentation générale',
                phone: '0522-123456',
                email: '<EMAIL>',
                address: 'Casablanca, Maroc',
                deliveryTime: 2,
                status: 'active'
            };

            const existing = storage.get('suppliers').find(s => s.name === testSupplier.name);
            if (!existing) {
                storage.add('suppliers', testSupplier);
            }

            updateTestResults('✅ Données de test créées avec succès!');
            testInventoryPrices();
            displayTestData();

            // Debug: Afficher les données créées
            console.log('Matières premières:', storage.get('rawMaterials'));
            console.log('Inventaire:', storage.get('inventory'));
        }

        async function testPurchaseModule() {
            if (!purchasesModule) {
                updateTestResults('❌ Module non initialisé');
                return;
            }

            try {
                // Recharger les données
                purchasesModule.loadData();
                
                // Tester la méthode getInventoryPrice
                const rawMaterials = purchasesModule.rawMaterials || [];
                let results = '<h6>Test de récupération des prix :</h6>';
                
                rawMaterials.slice(0, 5).forEach(material => {
                    const inventoryItem = purchasesModule.getInventoryPrice(material.id);
                    results += `<p><strong>${material.name}:</strong> `;
                    if (inventoryItem) {
                        results += `${Utils.formatPrice(inventoryItem.unitPrice)} ✅`;
                    } else {
                        results += 'Prix non trouvé ❌';
                    }
                    results += '</p>';
                });
                
                updateTestResults(results);
                
            } catch (error) {
                updateTestResults(`❌ Erreur lors du test: ${error.message}`);
            }
        }

        function displayTestData() {
            const rawMaterials = storage.get('rawMaterials') || [];
            const inventory = storage.get('inventory') || [];
            const suppliers = storage.get('suppliers') || [];
            
            let html = `
                <div class="row">
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6>Matières Premières</h6>
                                <h4 class="text-primary">${rawMaterials.length}</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6>Articles en Stock</h6>
                                <h4 class="text-success">${inventory.length}</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6>Fournisseurs</h6>
                                <h4 class="text-info">${suppliers.length}</h4>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.getElementById('testData').innerHTML = html;
        }

        function updateTestResults(message) {
            document.getElementById('testResults').innerHTML = `
                <div class="alert alert-info">
                    ${message}
                </div>
            `;
        }

        async function reloadPurchaseModule() {
            try {
                if (purchasesModule) {
                    purchasesModule.destroy();
                }

                purchasesModule = new PurchasesModule();
                await purchasesModule.init();

                updateTestResults('✅ Module achats rechargé avec succès!');
                testInventoryPrices();

            } catch (error) {
                updateTestResults(`❌ Erreur lors du rechargement: ${error.message}`);
            }
        }

        function openMainApp() {
            window.open('/', '_blank');
        }
    </script>
</body>
</html>
