<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnostic Fichier - RestoManager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Diagnostic de Fichier d'Import</h1>
        
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle me-2"></i>Objectif</h6>
            <p>Cet outil analyse votre fichier pour identifier pourquoi l'import échoue et propose des solutions.</p>
        </div>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>Analyse du Fichier</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">Sélectionner votre fichier problématique</label>
                            <input type="file" class="form-control" id="problemFile" accept=".csv,.xlsx,.xls,.txt">
                        </div>
                        
                        <button class="btn btn-primary" onclick="analyzeProblemFile()">
                            <i class="fas fa-search me-2"></i>Analyser le Fichier
                        </button>
                        
                        <button class="btn btn-success ms-2" onclick="generateFixedFile()">
                            <i class="fas fa-tools me-2"></i>Générer Fichier Corrigé
                        </button>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header">
                        <h5>Analyse Détaillée</h5>
                    </div>
                    <div class="card-body">
                        <div id="analysis">
                            <p class="text-muted">Aucune analyse effectuée</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Console de Debug</h5>
                    </div>
                    <div class="card-body">
                        <div id="debugOutput" style="max-height: 500px; overflow-y: auto; font-family: monospace; font-size: 11px; background: #f8f9fa; padding: 10px; border-radius: 5px;">
                            Aucune analyse
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Aperçu des Données</h5>
                    </div>
                    <div class="card-body">
                        <div id="dataPreview">
                            <p class="text-muted">Aucune donnée à afficher</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="assets/js/core/storage.js"></script>
    <script src="assets/js/core/utils.js"></script>
    <script src="assets/js/modules/inventory.js"></script>

    <script>
        let debugOutput = [];
        let inventoryModule;
        let fileData = null;

        // Rediriger console vers debug
        const originalLog = console.log;
        const originalError = console.error;
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            debugOutput.push(`[LOG] ${new Date().toLocaleTimeString()} - ${args.join(' ')}`);
            updateDebugDisplay();
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            debugOutput.push(`[ERROR] ${new Date().toLocaleTimeString()} - ${args.join(' ')}`);
            updateDebugDisplay();
        };

        function updateDebugDisplay() {
            const debugDiv = document.getElementById('debugOutput');
            debugDiv.innerHTML = debugOutput.slice(-50).map(line => `<div>${line}</div>`).join('');
            debugDiv.scrollTop = debugDiv.scrollHeight;
        }

        // Initialiser
        window.addEventListener('load', () => {
            inventoryModule = new InventoryModule();
            inventoryModule.init().then(() => {
                console.log('Module inventory initialisé');
            });
        });

        async function analyzeProblemFile() {
            const fileInput = document.getElementById('problemFile');
            const file = fileInput.files[0];

            if (!file) {
                alert('Veuillez sélectionner un fichier');
                return;
            }

            debugOutput = [];
            console.log('=== ANALYSE DU FICHIER PROBLÉMATIQUE ===');
            console.log(`Fichier: ${file.name}`);
            console.log(`Taille: ${file.size} bytes`);
            console.log(`Type MIME: ${file.type}`);
            console.log(`Dernière modification: ${new Date(file.lastModified).toLocaleString()}`);

            try {
                // Analyse brute du contenu
                await analyzeRawContent(file);
                
                // Tentative de parsing
                await attemptParsing(file);
                
            } catch (error) {
                console.error('Erreur d\'analyse:', error.message);
                showAnalysisResult('error', error.message);
            }
        }

        async function analyzeRawContent(file) {
            console.log('--- ANALYSE DU CONTENU BRUT ---');
            
            // Lire comme texte
            const text = await readFileAsText(file);
            console.log(`Longueur du texte: ${text.length} caractères`);
            
            // Analyser les 1000 premiers caractères
            const sample = text.substring(0, 1000);
            console.log('Échantillon du début:');
            console.log(sample);
            
            // Détecter l'encodage
            const hasAccents = /[àáâãäåæçèéêëìíîïñòóôõöøùúûüý]/i.test(sample);
            console.log(`Contient des accents: ${hasAccents}`);
            
            // Détecter les séparateurs
            const separators = {
                virgule: (sample.match(/,/g) || []).length,
                pointVirgule: (sample.match(/;/g) || []).length,
                tabulation: (sample.match(/\t/g) || []).length,
                pipe: (sample.match(/\|/g) || []).length
            };
            console.log('Séparateurs détectés:', separators);
            
            // Détecter les fins de ligne
            const lineEndings = {
                windows: (sample.match(/\r\n/g) || []).length,
                unix: (sample.match(/(?<!\r)\n/g) || []).length,
                mac: (sample.match(/\r(?!\n)/g) || []).length
            };
            console.log('Fins de ligne:', lineEndings);
            
            // Compter les lignes
            const lines = text.split(/\r?\n/);
            console.log(`Nombre total de lignes: ${lines.length}`);
            
            // Analyser les premières lignes
            console.log('Premières lignes:');
            lines.slice(0, 10).forEach((line, i) => {
                console.log(`Ligne ${i + 1}: "${line}"`);
            });
            
            // Rechercher des patterns de colonnes
            const firstLine = lines[0] || '';
            const possibleHeaders = ['article', 'nom', 'name', 'produit', 'quantité', 'quantity', 'prix', 'price', 'tva', 'tax'];
            const foundHeaders = possibleHeaders.filter(header => 
                firstLine.toLowerCase().includes(header)
            );
            console.log('En-têtes potentiels trouvés:', foundHeaders);
            
            fileData = { text, lines, sample, separators, lineEndings };
        }

        async function attemptParsing(file) {
            console.log('--- TENTATIVE DE PARSING ---');
            
            try {
                // Utiliser la méthode du module
                const data = await inventoryModule.parseExcelFile(file);
                console.log('Parsing réussi!');
                console.log('Données extraites:', data);
                
                showDataPreview(data);
                showAnalysisResult('success', `Parsing réussi! ${data.length} ligne(s) extraite(s).`);
                
            } catch (error) {
                console.error('Échec du parsing:', error.message);
                
                // Tentatives de parsing alternatif
                await tryAlternativeParsing();
                
                showAnalysisResult('error', `Échec du parsing: ${error.message}`);
            }
        }

        async function tryAlternativeParsing() {
            console.log('--- TENTATIVES DE PARSING ALTERNATIF ---');
            
            if (!fileData) return;
            
            const { lines, separators } = fileData;
            
            // Essayer différents séparateurs
            const separatorTests = [
                { name: 'Virgule', sep: ',' },
                { name: 'Point-virgule', sep: ';' },
                { name: 'Tabulation', sep: '\t' },
                { name: 'Pipe', sep: '|' }
            ];
            
            for (const test of separatorTests) {
                console.log(`Test avec séparateur ${test.name}:`);
                
                if (lines.length > 0) {
                    const headers = lines[0].split(test.sep);
                    console.log(`  En-têtes: ${headers.join(' | ')}`);
                    
                    if (lines.length > 1) {
                        const firstDataLine = lines[1].split(test.sep);
                        console.log(`  Première ligne de données: ${firstDataLine.join(' | ')}`);
                        
                        // Vérifier la cohérence
                        if (headers.length === firstDataLine.length) {
                            console.log(`  ✅ Cohérent: ${headers.length} colonnes`);
                        } else {
                            console.log(`  ❌ Incohérent: ${headers.length} en-têtes vs ${firstDataLine.length} données`);
                        }
                    }
                }
            }
        }

        function showDataPreview(data) {
            if (!data || data.length === 0) {
                document.getElementById('dataPreview').innerHTML = '<p class="text-muted">Aucune donnée</p>';
                return;
            }
            
            const preview = data.slice(0, 10); // Première 10 lignes
            
            const html = `
                <div class="table-responsive">
                    <table class="table table-striped table-sm">
                        <thead>
                            <tr>
                                <th>Article</th>
                                <th>Quantité</th>
                                <th>Prix HT</th>
                                <th>TVA</th>
                                <th>Montant HT</th>
                                <th>Montant TTC</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${preview.map((item, i) => `
                                <tr>
                                    <td>${item.article || 'N/A'}</td>
                                    <td>${item.quantite || 0}</td>
                                    <td>${(item.prixHT || 0).toFixed(2)} €</td>
                                    <td>${(item.tva || 0).toFixed(1)}%</td>
                                    <td>${(item.montantHT || 0).toFixed(2)} €</td>
                                    <td>${(item.montantTTC || 0).toFixed(2)} €</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
                <p class="text-muted">Affichage des ${preview.length} premières lignes sur ${data.length} total.</p>
            `;
            
            document.getElementById('dataPreview').innerHTML = html;
        }

        function showAnalysisResult(type, message) {
            const alertClass = type === 'success' ? 'alert-success' : 
                              type === 'error' ? 'alert-danger' : 'alert-warning';
            
            const icon = type === 'success' ? 'fa-check-circle' : 
                        type === 'error' ? 'fa-exclamation-triangle' : 'fa-info-circle';
            
            const html = `
                <div class="alert ${alertClass}">
                    <h6><i class="fas ${icon} me-2"></i>Résultat de l'analyse</h6>
                    <p>${message}</p>
                </div>
            `;
            
            document.getElementById('analysis').innerHTML = html;
        }

        function generateFixedFile() {
            if (!fileData) {
                alert('Veuillez d\'abord analyser un fichier');
                return;
            }
            
            console.log('--- GÉNÉRATION D\'UN FICHIER CORRIGÉ ---');
            
            // Créer un fichier CSV correct basé sur l'analyse
            const fixedContent = `Article,Quantité,Prix HT,TVA
Exemple Article 1,10,5.50,5.5
Exemple Article 2,25,3.20,5.5
Exemple Article 3,0,12.00,20.0`;

            downloadFile(fixedContent, 'fichier_corrige_exemple.csv');
            
            console.log('Fichier corrigé généré et téléchargé');
        }

        function readFileAsText(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (e) => resolve(e.target.result);
                reader.onerror = () => reject(new Error('Erreur de lecture'));
                reader.readAsText(file, 'utf-8');
            });
        }

        function downloadFile(content, filename) {
            const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    </script>
</body>
</html>
