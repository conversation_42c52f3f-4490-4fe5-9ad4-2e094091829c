/**
 * RestoManager - Module Gestion des Achats & Matières Premières
 * Interface d'achat avec catégorisation et mise à jour automatique du stock
 */

class PurchasesModule {
    constructor() {
        this.purchases = [];
        this.suppliers = [];
        this.rawMaterials = [];
        this.categories = [];
        this.currentPurchase = null;
        this.searchTerm = '';
        this.filterSupplier = '';
        this.filterCategory = '';
    }

    /**
     * Initialise le module
     */
    async init() {
        this.loadData();
    }

    /**
     * Charge les données depuis le stockage
     */
    loadData() {
        this.purchases = storage.get('purchases') || [];
        this.suppliers = storage.get('suppliers') || [];
        this.rawMaterials = storage.get('rawMaterials') || [];
        this.categories = storage.get('materialCategories') || [];
    }

    /**
     * Rend le contenu du module
     */
    async render() {
        return `
            <div class="purchases-container">
                <!-- En-tête -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card-neomorphic">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <h2 class="mb-0">
                                            <i class="fas fa-shopping-cart text-primary me-2"></i>
                                            Gestion des Achats
                                        </h2>
                                        <p class="text-muted mb-0">
                                            ${this.purchases.length} achat(s) enregistré(s)
                                        </p>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <button class="btn btn-neomorphic btn-success me-2" onclick="purchasesModule.showAddMaterialModal()">
                                            <i class="fas fa-plus me-2"></i>
                                            Nouvelle Matière Première
                                        </button>
                                        <button class="btn btn-neomorphic btn-primary" onclick="purchasesModule.showAddPurchaseModal()">
                                            <i class="fas fa-shopping-cart me-2"></i>
                                            Nouvel Achat
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistiques rapides -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-euro-sign"></i>
                            </div>
                            <div class="stat-value">${this.getTotalPurchases()}</div>
                            <div class="stat-label">Total des Achats</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-calendar-day"></i>
                            </div>
                            <div class="stat-value">${this.getTodayPurchases()}</div>
                            <div class="stat-label">Achats Aujourd'hui</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-boxes"></i>
                            </div>
                            <div class="stat-value">${this.rawMaterials.length}</div>
                            <div class="stat-label">Matières Premières</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-truck"></i>
                            </div>
                            <div class="stat-value">${this.suppliers.length}</div>
                            <div class="stat-label">Fournisseurs Actifs</div>
                        </div>
                    </div>
                </div>

                <!-- Filtres et recherche -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" class="form-control" placeholder="Rechercher..."
                                   id="searchPurchases" value="${this.searchTerm}">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="filterSupplier">
                            <option value="">Tous les fournisseurs</option>
                            ${this.suppliers.map(s => `
                                <option value="${s.id}" ${this.filterSupplier === s.id ? 'selected' : ''}>
                                    ${s.name}
                                </option>
                            `).join('')}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="filterCategory">
                            <option value="">Toutes les catégories</option>
                            ${this.categories.map(c => `
                                <option value="${c.id}" ${this.filterCategory === c.id ? 'selected' : ''}>
                                    ${c.name}
                                </option>
                            `).join('')}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-neomorphic btn-info w-100" onclick="purchasesModule.exportPurchases()">
                            <i class="fas fa-download me-1"></i>
                            Export
                        </button>
                    </div>
                </div>

                <!-- Onglets -->
                <div class="row">
                    <div class="col-12">
                        <div class="card-neomorphic">
                            <div class="card-header">
                                <ul class="nav nav-tabs-neomorphic" role="tablist">
                                    <li class="nav-item">
                                        <a class="nav-link active" data-bs-toggle="tab" href="#purchasesTab">
                                            <i class="fas fa-shopping-cart me-1"></i>
                                            Achats
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-bs-toggle="tab" href="#materialsTab">
                                            <i class="fas fa-boxes me-1"></i>
                                            Matières Premières
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <div class="card-body">
                                <div class="tab-content">
                                    <div class="tab-pane fade show active" id="purchasesTab">
                                        ${this.renderPurchasesList()}
                                    </div>
                                    <div class="tab-pane fade" id="materialsTab">
                                        ${this.renderMaterialsList()}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modales -->
                ${this.renderPurchaseModal()}
                ${this.renderMaterialModal()}
            </div>
        `;
    }

    /**
     * Rend la liste des achats
     */
    renderPurchasesList() {
        const filteredPurchases = this.getFilteredPurchases();

        if (filteredPurchases.length === 0) {
            return `
                <div class="text-center py-5">
                    <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Aucun achat trouvé</h5>
                    <p class="text-muted">Commencez par enregistrer votre premier achat</p>
                    <button class="btn btn-neomorphic btn-primary" onclick="purchasesModule.showAddPurchaseModal()">
                        <i class="fas fa-plus me-2"></i>
                        Nouvel Achat
                    </button>
                </div>
            `;
        }

        return `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Fournisseur</th>
                            <th>Articles</th>
                            <th>Montant Total</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${filteredPurchases.map(purchase => this.renderPurchaseRow(purchase)).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    /**
     * Rend une ligne d'achat
     */
    renderPurchaseRow(purchase) {
        const supplier = this.suppliers.find(s => s.id === purchase.supplierId);
        const statusBadge = this.getStatusBadge(purchase.status);

        return `
            <tr>
                <td>
                    <div class="fw-bold">${Utils.formatDate(purchase.date)}</div>
                    <small class="text-muted">${Utils.formatTime(purchase.date)}</small>
                </td>
                <td>
                    <div class="fw-bold">${supplier ? supplier.name : 'Fournisseur supprimé'}</div>
                    <small class="text-muted">${purchase.reference || 'N/A'}</small>
                </td>
                <td>
                    <span class="badge bg-info">${purchase.items ? purchase.items.length : 0} article(s)</span>
                </td>
                <td>
                    <div class="fw-bold text-success">${Utils.formatPrice(purchase.totalAmount)}</div>
                </td>
                <td>${statusBadge}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-info" onclick="purchasesModule.viewPurchase('${purchase.id}')"
                                title="Voir détails">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="purchasesModule.editPurchase('${purchase.id}')"
                                title="Modifier">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="purchasesModule.deletePurchase('${purchase.id}')"
                                title="Supprimer">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    /**
     * Rend la liste des matières premières
     */
    renderMaterialsList() {
        if (this.rawMaterials.length === 0) {
            return `
                <div class="text-center py-5">
                    <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Aucune matière première</h5>
                    <p class="text-muted">Ajoutez vos premières matières premières</p>
                    <button class="btn btn-neomorphic btn-success" onclick="purchasesModule.showAddMaterialModal()">
                        <i class="fas fa-plus me-2"></i>
                        Nouvelle Matière Première
                    </button>
                </div>
            `;
        }

        return `
            <div class="row">
                ${this.rawMaterials.map(material => this.renderMaterialCard(material)).join('')}
            </div>
        `;
    }

    /**
     * Rend une carte de matière première
     */
    renderMaterialCard(material) {
        const category = this.categories.find(c => c.id === material.categoryId);

        return `
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card-neomorphic h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="card-title mb-0">${material.name}</h6>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="purchasesModule.editMaterial('${material.id}')">
                                        <i class="fas fa-edit me-1"></i> Modifier
                                    </a></li>
                                    <li><a class="dropdown-item text-danger" href="#" onclick="purchasesModule.deleteMaterial('${material.id}')">
                                        <i class="fas fa-trash me-1"></i> Supprimer
                                    </a></li>
                                </ul>
                            </div>
                        </div>

                        <div class="mb-2">
                            <span class="badge" style="background-color: ${category ? category.color : '#6c757d'}">
                                ${category ? category.name : 'Sans catégorie'}
                            </span>
                        </div>

                        <div class="row text-center">
                            <div class="col-6">
                                <div class="small text-muted">Unité</div>
                                <div class="fw-bold">${material.unit}</div>
                            </div>
                            <div class="col-6">
                                <div class="small text-muted">Prix moyen</div>
                                <div class="fw-bold text-success">${Utils.formatPrice(material.averagePrice || 0)}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Rend le modal d'achat
     */
    renderPurchaseModal() {
        return `
            <div class="modal fade" id="purchaseModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content modal-neomorphic">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-shopping-cart me-2"></i>
                                <span id="purchaseModalTitle">Nouvel Achat</span>
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="purchaseForm">
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label class="form-label">Date *</label>
                                        <input type="date" class="form-control" id="purchaseDate" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Fournisseur *</label>
                                        <select class="form-select" id="purchaseSupplier" required>
                                            <option value="">Sélectionner un fournisseur</option>
                                            ${this.suppliers.map(s => `
                                                <option value="${s.id}">${s.name}</option>
                                            `).join('')}
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Référence</label>
                                        <input type="text" class="form-control" id="purchaseReference">
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-12">
                                        <h6>Articles achetés</h6>
                                        <div id="purchaseItems">
                                            <!-- Items will be added here -->
                                        </div>
                                        <button type="button" class="btn btn-neomorphic btn-success btn-sm" onclick="purchasesModule.addPurchaseItem()">
                                            <i class="fas fa-plus me-1"></i>
                                            Ajouter un article
                                        </button>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-label">Notes</label>
                                        <textarea class="form-control" id="purchaseNotes" rows="3"></textarea>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <h6>Récapitulatif</h6>
                                                <div class="d-flex justify-content-between">
                                                    <span>Total HT:</span>
                                                    <span id="purchaseTotalHT">0,00 €</span>
                                                </div>
                                                <div class="d-flex justify-content-between">
                                                    <span>TVA:</span>
                                                    <span id="purchaseTVA">0,00 €</span>
                                                </div>
                                                <hr>
                                                <div class="d-flex justify-content-between fw-bold">
                                                    <span>Total TTC:</span>
                                                    <span id="purchaseTotalTTC">0,00 €</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="button" class="btn btn-primary" onclick="purchasesModule.savePurchase()">
                                <i class="fas fa-save me-2"></i>
                                Enregistrer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Rend le modal de matière première
     */
    renderMaterialModal() {
        return `
            <div class="modal fade" id="materialModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content modal-neomorphic">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-boxes me-2"></i>
                                <span id="materialModalTitle">Nouvelle Matière Première</span>
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="materialForm">
                                <div class="mb-3">
                                    <label class="form-label">Nom *</label>
                                    <input type="text" class="form-control" id="materialName" required>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Catégorie *</label>
                                            <select class="form-select" id="materialCategory" required>
                                                <option value="">Sélectionner une catégorie</option>
                                                ${this.categories.map(c => `
                                                    <option value="${c.id}">${c.name}</option>
                                                `).join('')}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Unité *</label>
                                            <select class="form-select" id="materialUnit" required>
                                                <option value="">Sélectionner une unité</option>
                                                <option value="kg">Kilogramme (kg)</option>
                                                <option value="g">Gramme (g)</option>
                                                <option value="l">Litre (l)</option>
                                                <option value="ml">Millilitre (ml)</option>
                                                <option value="pièce">Pièce</option>
                                                <option value="boîte">Boîte</option>
                                                <option value="sachet">Sachet</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Description</label>
                                    <textarea class="form-control" id="materialDescription" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="button" class="btn btn-success" onclick="purchasesModule.saveMaterial()">
                                <i class="fas fa-save me-2"></i>
                                Enregistrer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Actions post-rendu
     */
    postRender() {
        this.attachEventListeners();
        window.purchasesModule = this;

        // Initialiser la date d'aujourd'hui
        const today = new Date().toISOString().split('T')[0];
        const dateInput = document.getElementById('purchaseDate');
        if (dateInput) {
            dateInput.value = today;
        }
    }

    /**
     * Attache les gestionnaires d'événements
     */
    attachEventListeners() {
        // Recherche
        const searchInput = document.getElementById('searchPurchases');
        if (searchInput) {
            searchInput.addEventListener('input', Utils.debounce((e) => {
                this.searchTerm = e.target.value;
                this.refreshPurchasesList();
            }, 300));
        }

        // Filtres
        const supplierFilter = document.getElementById('filterSupplier');
        if (supplierFilter) {
            supplierFilter.addEventListener('change', (e) => {
                this.filterSupplier = e.target.value;
                this.refreshPurchasesList();
            });
        }

        const categoryFilter = document.getElementById('filterCategory');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', (e) => {
                this.filterCategory = e.target.value;
                this.refreshPurchasesList();
            });
        }
    }

    // Méthodes utilitaires et actions
    getTotalPurchases() {
        const total = Utils.sumBy(this.purchases, 'totalAmount');
        return Utils.formatPrice(total);
    }

    getTodayPurchases() {
        const today = new Date().toDateString();
        return this.purchases.filter(p => new Date(p.date).toDateString() === today).length;
    }

    getFilteredPurchases() {
        let filtered = [...this.purchases];

        if (this.searchTerm) {
            const term = this.searchTerm.toLowerCase();
            filtered = filtered.filter(purchase => {
                const supplier = this.suppliers.find(s => s.id === purchase.supplierId);
                return (supplier && supplier.name.toLowerCase().includes(term)) ||
                       (purchase.reference && purchase.reference.toLowerCase().includes(term));
            });
        }

        if (this.filterSupplier) {
            filtered = filtered.filter(p => p.supplierId === this.filterSupplier);
        }

        return filtered.sort((a, b) => new Date(b.date) - new Date(a.date));
    }

    getStatusBadge(status) {
        const badges = {
            pending: '<span class="badge bg-warning">En attente</span>',
            received: '<span class="badge bg-success">Reçu</span>',
            cancelled: '<span class="badge bg-danger">Annulé</span>'
        };
        return badges[status] || badges.pending;
    }

    showAddPurchaseModal() {
        this.currentPurchase = null;
        document.getElementById('purchaseModalTitle').textContent = 'Nouvel Achat';
        this.resetPurchaseForm();
        new bootstrap.Modal(document.getElementById('purchaseModal')).show();
    }

    showAddMaterialModal() {
        this.currentMaterial = null;
        document.getElementById('materialModalTitle').textContent = 'Nouvelle Matière Première';
        this.resetMaterialForm();
        new bootstrap.Modal(document.getElementById('materialModal')).show();
    }

    addPurchaseItem() {
        const container = document.getElementById('purchaseItems');
        const itemIndex = container.children.length;

        const itemHtml = `
            <div class="purchase-item mb-3 p-3 border rounded">
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-label">Matière première</label>
                        <select class="form-select" name="materialId" required>
                            <option value="">Sélectionner</option>
                            ${this.rawMaterials.map(m => `
                                <option value="${m.id}">${m.name} (${m.unit})</option>
                            `).join('')}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Quantité</label>
                        <input type="number" class="form-control" name="quantity" min="0" step="0.01" required>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Prix unitaire</label>
                        <input type="number" class="form-control" name="unitPrice" min="0" step="0.01" required>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Total</label>
                        <input type="number" class="form-control" name="total" readonly>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button type="button" class="btn btn-danger w-100" onclick="this.parentElement.parentElement.parentElement.remove(); purchasesModule.updatePurchaseTotal()">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;

        container.insertAdjacentHTML('beforeend', itemHtml);

        // Ajouter les événements pour le calcul automatique
        const newItem = container.lastElementChild;
        const quantityInput = newItem.querySelector('[name="quantity"]');
        const priceInput = newItem.querySelector('[name="unitPrice"]');
        const totalInput = newItem.querySelector('[name="total"]');

        [quantityInput, priceInput].forEach(input => {
            input.addEventListener('input', () => {
                const quantity = parseFloat(quantityInput.value) || 0;
                const price = parseFloat(priceInput.value) || 0;
                totalInput.value = (quantity * price).toFixed(2);
                this.updatePurchaseTotal();
            });
        });
    }

    updatePurchaseTotal() {
        const items = document.querySelectorAll('#purchaseItems .purchase-item');
        let totalHT = 0;

        items.forEach(item => {
            const total = parseFloat(item.querySelector('[name="total"]').value) || 0;
            totalHT += total;
        });

        const tva = totalHT * 0.2; // 20% TVA
        const totalTTC = totalHT + tva;

        document.getElementById('purchaseTotalHT').textContent = Utils.formatPrice(totalHT);
        document.getElementById('purchaseTVA').textContent = Utils.formatPrice(tva);
        document.getElementById('purchaseTotalTTC').textContent = Utils.formatPrice(totalTTC);
    }

    async savePurchase() {
        const formData = this.getPurchaseFormData();

        if (!this.validatePurchaseForm(formData)) {
            return;
        }

        try {
            if (this.currentPurchase) {
                storage.update('purchases', this.currentPurchase.id, formData);
                Utils.showToast('Achat mis à jour avec succès', 'success');
            } else {
                storage.add('purchases', formData);
                Utils.showToast('Achat enregistré avec succès', 'success');

                // Mettre à jour le stock
                this.updateInventoryFromPurchase(formData);
            }

            this.loadData();
            this.refreshPurchasesList();
            bootstrap.Modal.getInstance(document.getElementById('purchaseModal')).hide();

        } catch (error) {
            console.error('Erreur lors de la sauvegarde:', error);
            Utils.showToast('Erreur lors de la sauvegarde', 'error');
        }
    }

    async saveMaterial() {
        const formData = this.getMaterialFormData();

        if (!this.validateMaterialForm(formData)) {
            return;
        }

        try {
            if (this.currentMaterial) {
                storage.update('rawMaterials', this.currentMaterial.id, formData);
                Utils.showToast('Matière première mise à jour', 'success');
            } else {
                storage.add('rawMaterials', formData);
                Utils.showToast('Matière première créée avec succès', 'success');
            }

            this.loadData();
            this.refreshMaterialsList();
            bootstrap.Modal.getInstance(document.getElementById('materialModal')).hide();

        } catch (error) {
            console.error('Erreur lors de la sauvegarde:', error);
            Utils.showToast('Erreur lors de la sauvegarde', 'error');
        }
    }

    getPurchaseFormData() {
        const items = [];
        document.querySelectorAll('#purchaseItems .purchase-item').forEach(item => {
            const materialId = item.querySelector('[name="materialId"]').value;
            const quantity = parseFloat(item.querySelector('[name="quantity"]').value);
            const unitPrice = parseFloat(item.querySelector('[name="unitPrice"]').value);
            const total = parseFloat(item.querySelector('[name="total"]').value);

            if (materialId && quantity && unitPrice) {
                items.push({ materialId, quantity, unitPrice, total });
            }
        });

        const totalAmount = items.reduce((sum, item) => sum + item.total, 0);

        return {
            date: document.getElementById('purchaseDate').value,
            supplierId: document.getElementById('purchaseSupplier').value,
            reference: document.getElementById('purchaseReference').value,
            items: items,
            totalAmount: totalAmount,
            status: 'received',
            notes: document.getElementById('purchaseNotes').value
        };
    }

    getMaterialFormData() {
        return {
            name: document.getElementById('materialName').value.trim(),
            categoryId: parseInt(document.getElementById('materialCategory').value),
            unit: document.getElementById('materialUnit').value,
            description: document.getElementById('materialDescription').value.trim(),
            averagePrice: 0
        };
    }

    validatePurchaseForm(data) {
        if (!data.date || !data.supplierId || data.items.length === 0) {
            Utils.showToast('Veuillez remplir tous les champs obligatoires', 'warning');
            return false;
        }
        return true;
    }

    validateMaterialForm(data) {
        if (!data.name || !data.categoryId || !data.unit) {
            Utils.showToast('Veuillez remplir tous les champs obligatoires', 'warning');
            return false;
        }
        return true;
    }

    updateInventoryFromPurchase(purchase) {
        const inventory = storage.get('inventory') || [];

        purchase.items.forEach(item => {
            const existingItem = inventory.find(inv => inv.materialId === item.materialId);

            if (existingItem) {
                existingItem.quantity += item.quantity;
                existingItem.totalValue += item.total;
                existingItem.averagePrice = existingItem.totalValue / existingItem.quantity;
                existingItem.lastPurchaseDate = purchase.date;
                storage.update('inventory', existingItem.id, existingItem);
            } else {
                storage.add('inventory', {
                    materialId: item.materialId,
                    quantity: item.quantity,
                    totalValue: item.total,
                    averagePrice: item.unitPrice,
                    minQuantity: 10,
                    lastPurchaseDate: purchase.date
                });
            }
        });
    }

    refreshPurchasesList() {
        const container = document.getElementById('purchasesTab');
        if (container) {
            container.innerHTML = this.renderPurchasesList();
        }
    }

    refreshMaterialsList() {
        const container = document.getElementById('materialsTab');
        if (container) {
            container.innerHTML = this.renderMaterialsList();
        }
    }

    resetPurchaseForm() {
        document.getElementById('purchaseForm').reset();
        document.getElementById('purchaseItems').innerHTML = '';
        this.updatePurchaseTotal();
        this.addPurchaseItem(); // Ajouter un premier item
    }

    resetMaterialForm() {
        document.getElementById('materialForm').reset();
    }

    exportPurchases() {
        const data = this.getFilteredPurchases();
        const filename = `achats_${new Date().toISOString().split('T')[0]}.csv`;
        Utils.exportToCSV(data, filename);
        Utils.showToast('Export réalisé avec succès', 'success');
    }

    destroy() {
        if (window.purchasesModule === this) {
            delete window.purchasesModule;
        }
    }
}

// Rendre le module disponible globalement
window.PurchasesModule = PurchasesModule;