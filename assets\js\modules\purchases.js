/**
 * RestoManager - Module Gestion des Achats & Matières Premières
 * Interface d'achat avec catégorisation et mise à jour automatique du stock
 */

class PurchasesModule {
    constructor() {
        this.purchases = [];
        this.suppliers = [];
        this.rawMaterials = [];
        this.categories = [];
        this.currentPurchase = null;
        this.searchTerm = '';
        this.filterSupplier = '';
        this.filterCategory = '';
    }

    /**
     * Initialise le module
     */
    async init() {
        this.loadData();
    }

    /**
     * Charge les données depuis le stockage
     */
    loadData() {
        this.purchases = storage.get('purchases') || [];
        this.suppliers = storage.get('suppliers') || [];
        this.rawMaterials = storage.get('rawMaterials') || [];
        this.categories = storage.get('materialCategories') || [];

        // Debug: Afficher le nombre de matières premières chargées
        console.log(`Matières premières chargées: ${this.rawMaterials.length}`);
    }

    /**
     * Recharge les données et met à jour les dropdowns
     */
    refreshData() {
        this.loadData();
        this.updateMaterialDropdowns();
    }

    /**
     * Met à jour tous les dropdowns de matières premières
     */
    updateMaterialDropdowns() {
        const selects = document.querySelectorAll('select[name="materialId"]');
        selects.forEach(select => {
            const currentValue = select.value;
            this.populateMaterialSelect(select);
            if (currentValue) {
                select.value = currentValue;
            }
        });
    }

    /**
     * Obtient la liste des fournisseurs uniques (sans doublons)
     */
    getUniqueSuppliers() {
        const uniqueMap = new Map();

        this.suppliers.forEach(supplier => {
            const key = supplier.name.toLowerCase().trim();

            if (uniqueMap.has(key)) {
                // Combiner les types
                const existing = uniqueMap.get(key);
                const existingTypes = existing.type ? existing.type.split(',').map(t => t.trim()) : [];
                const newTypes = supplier.type ? supplier.type.split(',').map(t => t.trim()) : [];

                const allTypes = [...new Set([...existingTypes, ...newTypes])].filter(t => t);
                existing.type = allTypes.join(', ');
            } else {
                uniqueMap.set(key, { ...supplier });
            }
        });

        return Array.from(uniqueMap.values());
    }

    /**
     * Gère le changement de fournisseur
     */
    onSupplierChange() {
        const supplierSelect = document.getElementById('purchaseSupplier');
        const typeSelect = document.getElementById('purchaseSupplierType');
        const selectedOption = supplierSelect.selectedOptions[0];

        // Réinitialiser le select des types
        typeSelect.innerHTML = '<option value="">Sélectionner un type</option>';

        if (selectedOption && selectedOption.value) {
            const supplierTypes = selectedOption.dataset.type || '';

            // Si le fournisseur a plusieurs types, les afficher dans le select
            if (supplierTypes) {
                const types = supplierTypes.split(',').map(t => t.trim()).filter(t => t);

                types.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type;
                    option.textContent = type;
                    typeSelect.appendChild(option);
                });

                // Si un seul type, le sélectionner automatiquement
                if (types.length === 1) {
                    typeSelect.value = types[0];
                    this.onTypeChange();
                }
            }

            // Réinitialiser les dropdowns sans filtrage en attendant la sélection du type
            this.updateMaterialDropdowns();
        } else {
            // Réinitialiser les dropdowns sans filtrage
            this.updateMaterialDropdowns();
        }
    }

    /**
     * Gère le changement de type de fournisseur
     */
    onTypeChange() {
        const typeSelect = document.getElementById('purchaseSupplierType');
        const selectedType = typeSelect.value;

        if (selectedType) {
            // Mettre à jour tous les dropdowns de matières premières avec le filtrage
            this.updateMaterialDropdownsWithFilter(selectedType);
        } else {
            // Réinitialiser les dropdowns sans filtrage
            this.updateMaterialDropdowns();
        }
    }

    /**
     * Met à jour les dropdowns avec filtrage par type de fournisseur
     */
    updateMaterialDropdownsWithFilter(supplierType) {
        const selects = document.querySelectorAll('select[name="materialId"]');
        selects.forEach(select => {
            const currentValue = select.value;
            this.populateMaterialSelectFiltered(select, supplierType);
            if (currentValue) {
                select.value = currentValue;
            }
        });
    }

    /**
     * Remplit un select avec les matières premières filtrées par type de fournisseur
     */
    populateMaterialSelectFiltered(selectElement, supplierType) {
        const inventory = storage.get('inventory') || [];

        // Mapping des types de fournisseurs vers les catégories
        const typeMapping = {
            'alimentation générale': ['Épicerie', 'Condiments', 'Huiles et vinaigres', 'Conserves'],
            'légumes et fruits': ['Légumes', 'Fruits'],
            'viandes': ['Viandes', 'Volailles'],
            'volailles': ['Volailles', 'Viandes'],
            'poissons': ['Poissons', 'Fruits de mer'],
            'produits laitiers': ['Produits laitiers', 'Fromages'],
            'charcuterie': ['Charcuterie', 'Viandes'],
            'boulangerie': ['Boulangerie', 'Pâtisserie'],
            'pâtisserie': ['Pâtisserie', 'Boulangerie'],
            'boissons': ['Boissons'],
            'surgelés': ['Surgelés'],
            'épicerie': ['Épicerie', 'Condiments'],
            'achats divers': ['Épicerie', 'Condiments', 'Autres'],
            'produits asiatiques': ['Épicerie', 'Condiments', 'Sauces'],
            'emballage': ['Emballage', 'Autres'],
            'produits finis': ['Produits finis', 'Autres']
        };

        const allowedCategories = typeMapping[supplierType.toLowerCase()] || [];

        // Si pas de mapping ou type vide, afficher toutes les matières
        if (!supplierType || allowedCategories.length === 0) {
            this.populateMaterialSelect(selectElement);
            return;
        }

        // Filtrer les matières premières par catégorie (insensible à la casse)
        const allowedCategoryIds = this.categories
            .filter(cat => allowedCategories.some(allowedCat =>
                cat.name.toLowerCase().includes(allowedCat.toLowerCase()) ||
                allowedCat.toLowerCase().includes(cat.name.toLowerCase())
            ))
            .map(cat => cat.id);

        const filteredMaterials = this.rawMaterials.filter(material =>
            allowedCategoryIds.includes(material.categoryId)
        );

        selectElement.innerHTML = '<option value="">Sélectionner</option>';

        filteredMaterials.forEach(material => {
            const inventoryItem = inventory.find(item => item.materialId === material.id);
            const unitPrice = inventoryItem ? (inventoryItem.unitPrice || inventoryItem.averagePrice || 0) : 0;
            const priceInfo = unitPrice > 0 ? ` - ${Utils.formatPrice(unitPrice)}` : '';

            const option = document.createElement('option');
            option.value = material.id;
            option.dataset.unitPrice = unitPrice;
            option.dataset.materialName = material.name;
            option.dataset.materialUnit = material.unit;
            option.textContent = `${material.name} (${material.unit})${priceInfo}`;

            selectElement.appendChild(option);
        });
    }

    /**
     * Remplit un select avec les matières premières (version non filtrée)
     */
    populateMaterialSelect(selectElement) {
        const inventory = storage.get('inventory') || [];

        selectElement.innerHTML = '<option value="">Sélectionner</option>';

        this.rawMaterials.forEach(material => {
            const inventoryItem = inventory.find(item => item.materialId === material.id);
            const unitPrice = inventoryItem ? (inventoryItem.unitPrice || inventoryItem.averagePrice || 0) : 0;
            const priceInfo = unitPrice > 0 ? ` - ${Utils.formatPrice(unitPrice)}` : '';

            const option = document.createElement('option');
            option.value = material.id;
            option.dataset.unitPrice = unitPrice;
            option.dataset.materialName = material.name;
            option.dataset.materialUnit = material.unit;
            option.textContent = `${material.name} (${material.unit})${priceInfo}`;

            selectElement.appendChild(option);
        });
    }

    /**
     * Rend le contenu du module
     */
    async render() {
        return `
            <div class="purchases-container">
                <!-- En-tête -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card-neomorphic">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <h2 class="mb-0">
                                            <i class="fas fa-shopping-cart text-primary me-2"></i>
                                            Gestion des Achats
                                        </h2>
                                        <p class="text-muted mb-0">
                                            ${this.purchases.length} achat(s) enregistré(s)
                                        </p>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <button class="btn btn-neomorphic btn-info me-2" onclick="purchasesModule.showImportMaterialsModal()">
                                            <i class="fas fa-upload me-2"></i>
                                            Importer Liste Matières 1ères
                                        </button>
                                        <button class="btn btn-neomorphic btn-success me-2" onclick="purchasesModule.showAddMaterialModal()">
                                            <i class="fas fa-plus me-2"></i>
                                            Nouvelle Matière Première
                                        </button>
                                        <button class="btn btn-neomorphic btn-primary" onclick="purchasesModule.showAddPurchaseModal()">
                                            <i class="fas fa-shopping-cart me-2"></i>
                                            Nouvel Achat
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistiques rapides -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-euro-sign"></i>
                            </div>
                            <div class="stat-value">${this.getTotalPurchases()}</div>
                            <div class="stat-label">Total des Achats</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-calendar-day"></i>
                            </div>
                            <div class="stat-value">${this.getTodayPurchases()}</div>
                            <div class="stat-label">Achats Aujourd'hui</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-boxes"></i>
                            </div>
                            <div class="stat-value">${this.rawMaterials.length}</div>
                            <div class="stat-label">Matières Premières</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-truck"></i>
                            </div>
                            <div class="stat-value">${this.suppliers.length}</div>
                            <div class="stat-label">Fournisseurs Actifs</div>
                        </div>
                    </div>
                </div>

                <!-- Filtres et recherche -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" class="form-control" placeholder="Rechercher..."
                                   id="searchPurchases" value="${this.searchTerm}">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="filterSupplier">
                            <option value="">Tous les fournisseurs</option>
                            ${this.suppliers.map(s => `
                                <option value="${s.id}" ${this.filterSupplier === s.id ? 'selected' : ''}>
                                    ${s.name}
                                </option>
                            `).join('')}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="filterCategory">
                            <option value="">Toutes les catégories</option>
                            ${this.categories.map(c => `
                                <option value="${c.id}" ${this.filterCategory === c.id ? 'selected' : ''}>
                                    ${c.name}
                                </option>
                            `).join('')}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-neomorphic btn-info w-100" onclick="purchasesModule.exportPurchases()">
                            <i class="fas fa-download me-1"></i>
                            Export
                        </button>
                    </div>
                </div>

                <!-- Onglets -->
                <div class="row">
                    <div class="col-12">
                        <div class="card-neomorphic">
                            <div class="card-header">
                                <ul class="nav nav-tabs-neomorphic" role="tablist">
                                    <li class="nav-item">
                                        <a class="nav-link active" data-bs-toggle="tab" href="#purchasesTab">
                                            <i class="fas fa-shopping-cart me-1"></i>
                                            Achats
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-bs-toggle="tab" href="#materialsTab">
                                            <i class="fas fa-boxes me-1"></i>
                                            Matières Premières
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <div class="card-body">
                                <div class="tab-content">
                                    <div class="tab-pane fade show active" id="purchasesTab">
                                        ${this.renderPurchasesList()}
                                    </div>
                                    <div class="tab-pane fade" id="materialsTab">
                                        ${this.renderMaterialsList()}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modales -->
                ${this.renderPurchaseModal()}
                ${this.renderMaterialModal()}
                ${this.renderImportMaterialsModal()}
            </div>
        `;
    }

    /**
     * Rend la liste des achats
     */
    renderPurchasesList() {
        const filteredPurchases = this.getFilteredPurchases();

        if (filteredPurchases.length === 0) {
            return `
                <div class="text-center py-5">
                    <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Aucun achat trouvé</h5>
                    <p class="text-muted">Commencez par enregistrer votre premier achat</p>
                    <button class="btn btn-neomorphic btn-primary" onclick="purchasesModule.showAddPurchaseModal()">
                        <i class="fas fa-plus me-2"></i>
                        Nouvel Achat
                    </button>
                </div>
            `;
        }

        return `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Fournisseur</th>
                            <th>Articles</th>
                            <th>Montant Total</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${filteredPurchases.map(purchase => this.renderPurchaseRow(purchase)).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    /**
     * Rend une ligne d'achat
     */
    renderPurchaseRow(purchase) {
        const supplier = this.suppliers.find(s => s.id === purchase.supplierId);
        const statusBadge = this.getStatusBadge(purchase.status);

        return `
            <tr>
                <td>
                    <div class="fw-bold">${Utils.formatDate(purchase.date)}</div>
                    <small class="text-muted">${Utils.formatTime(purchase.date)}</small>
                </td>
                <td>
                    <div class="fw-bold">${supplier ? supplier.name : 'Fournisseur supprimé'}</div>
                    <small class="text-muted">${purchase.supplierType || supplier?.type || 'Type non spécifié'}</small>
                </td>
                <td>
                    <span class="badge bg-info">${purchase.items ? purchase.items.length : 0} article(s)</span>
                </td>
                <td>
                    <div class="fw-bold text-success">${Utils.formatPrice(purchase.totalAmount)}</div>
                </td>
                <td>${statusBadge}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-info" onclick="purchasesModule.viewPurchase('${purchase.id}')"
                                title="Voir détails">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="purchasesModule.editPurchase('${purchase.id}')"
                                title="Modifier">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="purchasesModule.deletePurchase('${purchase.id}')"
                                title="Supprimer">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    /**
     * Rend la liste des matières premières
     */
    renderMaterialsList() {
        if (this.rawMaterials.length === 0) {
            return `
                <div class="text-center py-5">
                    <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Aucune matière première</h5>
                    <p class="text-muted">Ajoutez vos premières matières premières</p>
                    <button class="btn btn-neomorphic btn-success" onclick="purchasesModule.showAddMaterialModal()">
                        <i class="fas fa-plus me-2"></i>
                        Nouvelle Matière Première
                    </button>
                </div>
            `;
        }

        return `
            <div class="row">
                ${this.rawMaterials.map(material => this.renderMaterialCard(material)).join('')}
            </div>
        `;
    }

    /**
     * Rend une carte de matière première
     */
    renderMaterialCard(material) {
        const category = this.categories.find(c => c.id === material.categoryId);

        return `
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card-neomorphic h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="card-title mb-0">${material.name}</h6>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="purchasesModule.editMaterial('${material.id}')">
                                        <i class="fas fa-edit me-1"></i> Modifier
                                    </a></li>
                                    <li><a class="dropdown-item text-danger" href="#" onclick="purchasesModule.deleteMaterial('${material.id}')">
                                        <i class="fas fa-trash me-1"></i> Supprimer
                                    </a></li>
                                </ul>
                            </div>
                        </div>

                        <div class="mb-2">
                            <span class="badge" style="background-color: ${category ? category.color : '#6c757d'}">
                                ${category ? category.name : 'Sans catégorie'}
                            </span>
                        </div>

                        <div class="row text-center">
                            <div class="col-6">
                                <div class="small text-muted">Unité</div>
                                <div class="fw-bold">${material.unit}</div>
                            </div>
                            <div class="col-6">
                                <div class="small text-muted">Prix moyen</div>
                                <div class="fw-bold text-success">${Utils.formatPrice(material.averagePrice || 0)}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Rend le modal d'achat
     */
    renderPurchaseModal() {
        return `
            <div class="modal fade" id="purchaseModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content modal-neomorphic">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-shopping-cart me-2"></i>
                                <span id="purchaseModalTitle">Nouvel Achat</span>
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="purchaseForm">
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label class="form-label">Date *</label>
                                        <input type="date" class="form-control" id="purchaseDate" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Fournisseur *</label>
                                        <select class="form-select" id="purchaseSupplier" required onchange="purchasesModule.onSupplierChange()">
                                            <option value="">Sélectionner un fournisseur</option>
                                            ${this.getUniqueSuppliers().map(s => `
                                                <option value="${s.id}" data-type="${s.type || ''}">${s.name}</option>
                                            `).join('')}
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Type *</label>
                                        <select class="form-select" id="purchaseSupplierType" required onchange="purchasesModule.onTypeChange()">
                                            <option value="">Sélectionner un type</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-12">
                                        <h6>Articles achetés</h6>
                                        <div id="purchaseItems">
                                            <!-- Items will be added here -->
                                        </div>
                                        <button type="button" class="btn btn-neomorphic btn-success btn-sm" onclick="purchasesModule.addPurchaseItem()">
                                            <i class="fas fa-plus me-1"></i>
                                            Ajouter un article
                                        </button>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-label">Notes</label>
                                        <textarea class="form-control" id="purchaseNotes" rows="3"></textarea>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <h6>Récapitulatif</h6>
                                                <div class="d-flex justify-content-between">
                                                    <span>Total HT:</span>
                                                    <span id="purchaseTotalHT">0,00 €</span>
                                                </div>
                                                <div class="d-flex justify-content-between">
                                                    <span>TVA:</span>
                                                    <span id="purchaseTVA">0,00 €</span>
                                                </div>
                                                <hr>
                                                <div class="d-flex justify-content-between fw-bold">
                                                    <span>Total TTC:</span>
                                                    <span id="purchaseTotalTTC">0,00 €</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="button" class="btn btn-info me-2" onclick="purchasesModule.generatePurchaseOrder()" id="generateOrderBtn" style="display: none;">
                                <i class="fas fa-download me-2"></i>
                                Bon de Commande
                            </button>
                            <button type="button" class="btn btn-primary" onclick="purchasesModule.savePurchase()">
                                <i class="fas fa-save me-2"></i>
                                Enregistrer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Rend le modal de matière première
     */
    renderMaterialModal() {
        return `
            <div class="modal fade" id="materialModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content modal-neomorphic">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-boxes me-2"></i>
                                <span id="materialModalTitle">Nouvelle Matière Première</span>
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="materialForm">
                                <div class="mb-3">
                                    <label class="form-label">Nom *</label>
                                    <input type="text" class="form-control" id="materialName" required>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Catégorie *</label>
                                            <select class="form-select" id="materialCategory" required>
                                                <option value="">Sélectionner une catégorie</option>
                                                ${this.categories.map(c => `
                                                    <option value="${c.id}">${c.name}</option>
                                                `).join('')}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Unité *</label>
                                            <select class="form-select" id="materialUnit" required>
                                                <option value="">Sélectionner une unité</option>
                                                <option value="kg">Kilogramme (kg)</option>
                                                <option value="g">Gramme (g)</option>
                                                <option value="l">Litre (l)</option>
                                                <option value="ml">Millilitre (ml)</option>
                                                <option value="pièce">Pièce</option>
                                                <option value="boîte">Boîte</option>
                                                <option value="sachet">Sachet</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Description</label>
                                    <textarea class="form-control" id="materialDescription" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="button" class="btn btn-success" onclick="purchasesModule.saveMaterial()">
                                <i class="fas fa-save me-2"></i>
                                Enregistrer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Rend le modal d'import de matières premières
     */
    renderImportMaterialsModal() {
        return `
            <div class="modal fade" id="importMaterialsModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content modal-neomorphic">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-upload me-2"></i>
                                Import Liste Matières Premières
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-neomorphic alert-info">
                                <h6 class="alert-heading">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Format du fichier Excel/CSV requis
                                </h6>
                                <p class="mb-2">Le fichier doit contenir les colonnes suivantes :</p>
                                <ul class="mb-2">
                                    <li><strong>Article</strong> : Nom de la matière première</li>
                                    <li><strong>Unité</strong> : Unité de mesure (kg, litre, pièce, etc.)</li>
                                    <li><strong>Prix HT</strong> : Prix unitaire hors taxes</li>
                                    <li><strong>TVA</strong> : Taux de TVA (en %, ex: 5.5)</li>
                                    <li><strong>Fournisseur</strong> : Nom du fournisseur</li>
                                    <li><strong>Catégorie</strong> : Catégorie du produit</li>
                                </ul>
                                <p class="mb-0">
                                    <small class="text-muted">
                                        L'application créera automatiquement les fournisseurs et associera les catégories.
                                    </small>
                                </p>
                            </div>

                            <form id="importMaterialsForm">
                                <div class="mb-3">
                                    <label class="form-label">Fichier Excel (.xlsx, .xls, .csv) *</label>
                                    <input type="file" class="form-control" id="importMaterialsFile"
                                           accept=".xlsx,.xls,.csv" required>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="createMissingSuppliers" checked>
                                                <label class="form-check-label" for="createMissingSuppliers">
                                                    Créer automatiquement les fournisseurs manquants
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="updateExistingMaterials" checked>
                                                <label class="form-check-label" for="updateExistingMaterials">
                                                    Mettre à jour les matières premières existantes
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div id="importMaterialsPreview" class="mt-3" style="display: none;">
                                    <h6>Aperçu des données :</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Article</th>
                                                    <th>Unité</th>
                                                    <th>Prix HT</th>
                                                    <th>TVA</th>
                                                    <th>Prix TTC</th>
                                                    <th>Fournisseur</th>
                                                    <th>Catégorie</th>
                                                    <th>Statut</th>
                                                </tr>
                                            </thead>
                                            <tbody id="importMaterialsPreviewBody">
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-md-4">
                                            <div class="card bg-light">
                                                <div class="card-body text-center">
                                                    <h6>Articles à créer</h6>
                                                    <h4 class="text-success" id="newMaterialsCount">0</h4>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="card bg-light">
                                                <div class="card-body text-center">
                                                    <h6>Articles à mettre à jour</h6>
                                                    <h4 class="text-warning" id="updateMaterialsCount">0</h4>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="card bg-light">
                                                <div class="card-body text-center">
                                                    <h6>Fournisseurs à créer</h6>
                                                    <h4 class="text-info" id="newSuppliersCount">0</h4>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="button" class="btn btn-info" onclick="purchasesModule.previewImportMaterials()" id="previewMaterialsBtn">
                                <i class="fas fa-eye me-2"></i>Aperçu
                            </button>
                            <button type="button" class="btn btn-success" onclick="purchasesModule.executeImportMaterials()"
                                    id="importMaterialsBtn" style="display: none;">
                                <i class="fas fa-upload me-2"></i>Importer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Actions post-rendu
     */
    postRender() {
        this.attachEventListeners();
        window.purchasesModule = this;

        // Initialiser la date d'aujourd'hui
        const today = new Date().toISOString().split('T')[0];
        const dateInput = document.getElementById('purchaseDate');
        if (dateInput) {
            dateInput.value = today;
        }
    }

    /**
     * Attache les gestionnaires d'événements
     */
    attachEventListeners() {
        // Recherche
        const searchInput = document.getElementById('searchPurchases');
        if (searchInput) {
            searchInput.addEventListener('input', Utils.debounce((e) => {
                this.searchTerm = e.target.value;
                this.refreshPurchasesList();
            }, 300));
        }

        // Filtres
        const supplierFilter = document.getElementById('filterSupplier');
        if (supplierFilter) {
            supplierFilter.addEventListener('change', (e) => {
                this.filterSupplier = e.target.value;
                this.refreshPurchasesList();
            });
        }

        const categoryFilter = document.getElementById('filterCategory');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', (e) => {
                this.filterCategory = e.target.value;
                this.refreshPurchasesList();
            });
        }
    }

    // Méthodes utilitaires et actions
    getTotalPurchases() {
        const total = Utils.sumBy(this.purchases, 'totalAmount');
        return Utils.formatPrice(total);
    }

    getTodayPurchases() {
        const today = new Date().toDateString();
        return this.purchases.filter(p => new Date(p.date).toDateString() === today).length;
    }

    getFilteredPurchases() {
        let filtered = [...this.purchases];

        if (this.searchTerm) {
            const term = this.searchTerm.toLowerCase();
            filtered = filtered.filter(purchase => {
                const supplier = this.suppliers.find(s => s.id === purchase.supplierId);
                return (supplier && supplier.name.toLowerCase().includes(term)) ||
                       (purchase.supplierType && purchase.supplierType.toLowerCase().includes(term)) ||
                       (supplier && supplier.type && supplier.type.toLowerCase().includes(term));
            });
        }

        if (this.filterSupplier) {
            filtered = filtered.filter(p => p.supplierId === this.filterSupplier);
        }

        return filtered.sort((a, b) => new Date(b.date) - new Date(a.date));
    }

    getStatusBadge(status) {
        const badges = {
            pending: '<span class="badge bg-warning">En attente</span>',
            received: '<span class="badge bg-success">Reçu</span>',
            cancelled: '<span class="badge bg-danger">Annulé</span>'
        };
        return badges[status] || badges.pending;
    }

    showAddPurchaseModal() {
        this.currentPurchase = null;
        this.lastSavedPurchase = null;

        // Recharger les données pour s'assurer d'avoir les dernières matières premières
        this.refreshData();

        document.getElementById('purchaseModalTitle').textContent = 'Nouvel Achat';
        this.resetPurchaseForm();

        // Cacher le bouton de bon de commande
        const generateBtn = document.getElementById('generateOrderBtn');
        if (generateBtn) {
            generateBtn.style.display = 'none';
        }

        new bootstrap.Modal(document.getElementById('purchaseModal')).show();
    }

    showAddMaterialModal() {
        this.currentMaterial = null;
        document.getElementById('materialModalTitle').textContent = 'Nouvelle Matière Première';
        this.resetMaterialForm();
        new bootstrap.Modal(document.getElementById('materialModal')).show();
    }

    addPurchaseItem() {
        const container = document.getElementById('purchaseItems');
        const itemIndex = container.children.length;

        const itemHtml = `
            <div class="purchase-item mb-3 p-3 border rounded">
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-label">Matière première</label>
                        <select class="form-select" name="materialId" required onchange="purchasesModule.onMaterialSelect(this)">
                            <option value="">Sélectionner</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Quantité</label>
                        <input type="number" class="form-control" name="quantity" min="0" step="0.01" required onchange="purchasesModule.updateItemTotal(this)">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Prix unitaire (DH)</label>
                        <input type="number" class="form-control" name="unitPrice" min="0" step="0.01" required onchange="purchasesModule.updateItemTotal(this)">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Total (DH)</label>
                        <input type="number" class="form-control" name="total" readonly>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button type="button" class="btn btn-danger w-100" onclick="this.parentElement.parentElement.parentElement.remove(); purchasesModule.updatePurchaseTotal()">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;

        container.insertAdjacentHTML('beforeend', itemHtml);

        // Récupérer le nouvel élément ajouté
        const newItem = container.lastElementChild;

        // Remplir le select des matières premières avec filtrage si un type est sélectionné
        const materialSelect = newItem.querySelector('[name="materialId"]');
        const typeSelect = document.getElementById('purchaseSupplierType');
        const selectedType = typeSelect.value;

        if (selectedType) {
            this.populateMaterialSelectFiltered(materialSelect, selectedType);
        } else {
            this.populateMaterialSelect(materialSelect);
        }

        // Ajouter les événements pour le calcul automatique
        const quantityInput = newItem.querySelector('[name="quantity"]');
        const priceInput = newItem.querySelector('[name="unitPrice"]');
        const totalInput = newItem.querySelector('[name="total"]');

        [quantityInput, priceInput].forEach(input => {
            input.addEventListener('input', () => {
                const quantity = parseFloat(quantityInput.value) || 0;
                const price = parseFloat(priceInput.value) || 0;
                totalInput.value = (quantity * price).toFixed(2);
                this.updatePurchaseTotal();
            });
        });
    }

    /**
     * Récupère le prix depuis l'inventaire pour une matière première
     */
    getInventoryPrice(materialId) {
        const inventory = storage.get('inventory') || [];
        const inventoryItem = inventory.find(item => item.materialId == materialId);

        if (inventoryItem) {
            // Essayer unitPrice d'abord, puis averagePrice (pour compatibilité avec le module inventory)
            const price = inventoryItem.unitPrice || inventoryItem.averagePrice || 0;
            return {
                unitPrice: parseFloat(price) || 0,
                quantity: inventoryItem.quantity || 0
            };
        }

        // Si pas trouvé dans l'inventaire, retourner 0
        return {
            unitPrice: 0,
            quantity: 0
        };
    }

    /**
     * Gère la sélection d'une matière première
     */
    onMaterialSelect(selectElement) {
        const selectedOption = selectElement.selectedOptions[0];
        const purchaseItem = selectElement.closest('.purchase-item');

        if (selectedOption && selectedOption.value) {
            const unitPrice = parseFloat(selectedOption.dataset.unitPrice) || 0;
            const priceInput = purchaseItem.querySelector('[name="unitPrice"]');
            const quantityInput = purchaseItem.querySelector('[name="quantity"]');
            const totalInput = purchaseItem.querySelector('[name="total"]');

            // Remplir le prix automatiquement
            if (priceInput) {
                priceInput.value = unitPrice.toFixed(2);
            }

            // Calculer le total si une quantité est déjà saisie
            if (quantityInput && quantityInput.value) {
                const quantity = parseFloat(quantityInput.value) || 0;
                const total = quantity * unitPrice;
                if (totalInput) {
                    totalInput.value = total.toFixed(2);
                }
            }

            // Mettre à jour le total général
            this.updatePurchaseTotal();
        } else {
            // Réinitialiser les champs si aucune sélection
            const priceInput = purchaseItem.querySelector('[name="unitPrice"]');
            const totalInput = purchaseItem.querySelector('[name="total"]');

            if (priceInput) priceInput.value = '';
            if (totalInput) totalInput.value = '';

            this.updatePurchaseTotal();
        }
    }

    /**
     * Met à jour le total d'un article
     */
    updateItemTotal(element) {
        const purchaseItem = element.closest('.purchase-item');
        const quantityInput = purchaseItem.querySelector('[name="quantity"]');
        const priceInput = purchaseItem.querySelector('[name="unitPrice"]');
        const totalInput = purchaseItem.querySelector('[name="total"]');

        const quantity = parseFloat(quantityInput.value) || 0;
        const unitPrice = parseFloat(priceInput.value) || 0;
        const total = quantity * unitPrice;

        totalInput.value = total.toFixed(2);

        // Mettre à jour le total général
        this.updatePurchaseTotal();
    }

    updatePurchaseTotal() {
        const items = document.querySelectorAll('#purchaseItems .purchase-item');
        let totalHT = 0;

        items.forEach(item => {
            const total = parseFloat(item.querySelector('[name="total"]').value) || 0;
            totalHT += total;
        });

        const tva = totalHT * 0.2; // 20% TVA
        const totalTTC = totalHT + tva;

        document.getElementById('purchaseTotalHT').textContent = Utils.formatPrice(totalHT);
        document.getElementById('purchaseTVA').textContent = Utils.formatPrice(tva);
        document.getElementById('purchaseTotalTTC').textContent = Utils.formatPrice(totalTTC);
    }

    async savePurchase() {
        const formData = this.getPurchaseFormData();

        if (!this.validatePurchaseForm(formData)) {
            return;
        }

        try {
            let savedPurchase;
            if (this.currentPurchase) {
                savedPurchase = storage.update('purchases', this.currentPurchase.id, formData);
                Utils.showToast('Achat mis à jour avec succès', 'success');
            } else {
                savedPurchase = storage.add('purchases', formData);
                Utils.showToast('Achat enregistré avec succès', 'success');

                // Mettre à jour le stock
                this.updateInventoryFromPurchase(formData);
            }

            // Stocker l'achat pour le bon de commande
            this.lastSavedPurchase = savedPurchase;

            // Afficher le bouton de bon de commande
            const generateBtn = document.getElementById('generateOrderBtn');
            if (generateBtn) {
                generateBtn.style.display = 'inline-block';
            }

            this.loadData();
            this.refreshPurchasesList();

            // Ne pas fermer le modal pour permettre le téléchargement du bon de commande
            // bootstrap.Modal.getInstance(document.getElementById('purchaseModal')).hide();

        } catch (error) {
            console.error('Erreur lors de la sauvegarde:', error);
            Utils.showToast('Erreur lors de la sauvegarde', 'error');
        }
    }

    async saveMaterial() {
        const formData = this.getMaterialFormData();

        if (!this.validateMaterialForm(formData)) {
            return;
        }

        try {
            if (this.currentMaterial) {
                storage.update('rawMaterials', this.currentMaterial.id, formData);
                Utils.showToast('Matière première mise à jour', 'success');
            } else {
                storage.add('rawMaterials', formData);
                Utils.showToast('Matière première créée avec succès', 'success');
            }

            this.loadData();
            this.refreshMaterialsList();
            bootstrap.Modal.getInstance(document.getElementById('materialModal')).hide();

        } catch (error) {
            console.error('Erreur lors de la sauvegarde:', error);
            Utils.showToast('Erreur lors de la sauvegarde', 'error');
        }
    }

    getPurchaseFormData() {
        const items = [];
        document.querySelectorAll('#purchaseItems .purchase-item').forEach(item => {
            const materialId = item.querySelector('[name="materialId"]').value;
            const quantity = parseFloat(item.querySelector('[name="quantity"]').value);
            const unitPrice = parseFloat(item.querySelector('[name="unitPrice"]').value);
            const total = parseFloat(item.querySelector('[name="total"]').value);

            if (materialId && quantity && unitPrice) {
                items.push({ materialId, quantity, unitPrice, total });
            }
        });

        const totalAmount = items.reduce((sum, item) => sum + item.total, 0);

        return {
            date: document.getElementById('purchaseDate').value,
            supplierId: document.getElementById('purchaseSupplier').value,
            supplierType: document.getElementById('purchaseSupplierType').value,
            items: items,
            totalAmount: totalAmount,
            status: 'received',
            notes: document.getElementById('purchaseNotes').value
        };
    }

    getMaterialFormData() {
        return {
            name: document.getElementById('materialName').value.trim(),
            categoryId: parseInt(document.getElementById('materialCategory').value),
            unit: document.getElementById('materialUnit').value,
            description: document.getElementById('materialDescription').value.trim(),
            averagePrice: 0
        };
    }

    validatePurchaseForm(data) {
        if (!data.date || !data.supplierId || data.items.length === 0) {
            Utils.showToast('Veuillez remplir tous les champs obligatoires', 'warning');
            return false;
        }
        return true;
    }

    validateMaterialForm(data) {
        if (!data.name || !data.categoryId || !data.unit) {
            Utils.showToast('Veuillez remplir tous les champs obligatoires', 'warning');
            return false;
        }
        return true;
    }

    updateInventoryFromPurchase(purchase) {
        const inventory = storage.get('inventory') || [];

        purchase.items.forEach(item => {
            const existingItem = inventory.find(inv => inv.materialId === item.materialId);

            if (existingItem) {
                existingItem.quantity += item.quantity;
                existingItem.totalValue += item.total;
                existingItem.averagePrice = existingItem.totalValue / existingItem.quantity;
                existingItem.lastPurchaseDate = purchase.date;
                storage.update('inventory', existingItem.id, existingItem);
            } else {
                storage.add('inventory', {
                    materialId: item.materialId,
                    quantity: item.quantity,
                    totalValue: item.total,
                    averagePrice: item.unitPrice,
                    minQuantity: 10,
                    lastPurchaseDate: purchase.date
                });
            }
        });
    }

    refreshPurchasesList() {
        const container = document.getElementById('purchasesTab');
        if (container) {
            container.innerHTML = this.renderPurchasesList();
        }
    }

    refreshMaterialsList() {
        const container = document.getElementById('materialsTab');
        if (container) {
            container.innerHTML = this.renderMaterialsList();
        }
    }

    resetPurchaseForm() {
        document.getElementById('purchaseForm').reset();
        document.getElementById('purchaseItems').innerHTML = '';
        this.updatePurchaseTotal();
        this.addPurchaseItem(); // Ajouter un premier item
    }

    resetMaterialForm() {
        document.getElementById('materialForm').reset();
    }

    exportPurchases() {
        const data = this.getFilteredPurchases();
        const filename = `achats_${new Date().toISOString().split('T')[0]}.csv`;
        Utils.exportToCSV(data, filename);
        Utils.showToast('Export réalisé avec succès', 'success');
    }

    // Méthodes d'import de matières premières
    showImportMaterialsModal() {
        new bootstrap.Modal(document.getElementById('importMaterialsModal')).show();
    }

    async previewImportMaterials() {
        const fileInput = document.getElementById('importMaterialsFile');
        const file = fileInput.files[0];

        if (!file) {
            Utils.showToast('Veuillez sélectionner un fichier', 'warning');
            return;
        }

        try {
            const data = await this.parseMaterialsFile(file);
            this.displayMaterialsPreview(data);

            document.getElementById('previewMaterialsBtn').style.display = 'none';
            document.getElementById('importMaterialsBtn').style.display = 'inline-block';

        } catch (error) {
            console.error('Erreur lors de la lecture du fichier:', error);
            Utils.showToast('Erreur lors de la lecture du fichier: ' + error.message, 'error');
        }
    }

    async parseMaterialsFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();

            reader.onload = (e) => {
                try {
                    console.log(`Parsing du fichier: ${file.name}, Type: ${file.type}, Taille: ${file.size}`);

                    const isExcel = file.name.toLowerCase().endsWith('.xlsx') ||
                                   file.name.toLowerCase().endsWith('.xls') ||
                                   file.type.includes('spreadsheet');

                    if (isExcel && window.XLSX) {
                        console.log('Parsing Excel avec SheetJS');
                        const data = new Uint8Array(e.target.result);
                        const workbook = XLSX.read(data, { type: 'array' });
                        const firstSheet = workbook.Sheets[workbook.SheetNames[0]];

                        const jsonData = XLSX.utils.sheet_to_json(firstSheet, {
                            header: 1,
                            defval: ''
                        });

                        const headers = jsonData[0] || [];
                        const dataRows = jsonData.slice(1);

                        const objectData = dataRows.map(row => {
                            const obj = {};
                            headers.forEach((header, index) => {
                                obj[header] = row[index] || '';
                            });
                            return obj;
                        });

                        resolve(this.normalizeMaterialsData(objectData));

                    } else {
                        console.log('Parsing CSV');
                        const text = new TextDecoder('utf-8').decode(e.target.result);
                        const csvData = this.parseCSV(text);
                        resolve(this.normalizeMaterialsData(csvData));
                    }
                } catch (error) {
                    console.error('Erreur lors du parsing:', error);
                    reject(error);
                }
            };

            reader.onerror = () => reject(new Error('Erreur de lecture du fichier'));

            if (file.name.toLowerCase().endsWith('.csv') || file.type === 'text/csv') {
                reader.readAsText(file, 'utf-8');
            } else {
                reader.readAsArrayBuffer(file);
            }
        });
    }

    parseCSV(text) {
        const normalizedText = text.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
        const lines = normalizedText.split('\n').filter(line => line.trim());

        if (lines.length < 2) {
            throw new Error('Le fichier CSV doit contenir au moins une ligne d\'en-tête et une ligne de données');
        }

        const firstLine = lines[0];
        let separator = ',';

        if (firstLine.includes('\t')) {
            separator = '\t';
        } else if (firstLine.includes(';') && firstLine.split(';').length > firstLine.split(',').length) {
            separator = ';';
        }

        const headers = firstLine.split(separator).map(h => h.trim().replace(/^["']|["']$/g, ''));
        const data = [];

        for (let i = 1; i < lines.length; i++) {
            const line = lines[i].trim();
            if (!line) continue;

            const values = line.split(separator).map(v => v.trim().replace(/^["']|["']$/g, ''));
            const row = {};
            headers.forEach((header, index) => {
                row[header] = values[index] || '';
            });

            const hasData = Object.values(row).some(value => value && value.trim() !== '');
            if (hasData) {
                data.push(row);
            }
        }

        return data;
    }

    normalizeMaterialsData(rawData) {
        console.log('Données brutes reçues:', rawData);

        if (!rawData || rawData.length === 0) {
            throw new Error('Aucune donnée trouvée dans le fichier');
        }

        const cleanNumericValue = (value) => {
            if (value === null || value === undefined || value === '') return 0;
            let cleanValue = value.toString().trim();
            cleanValue = cleanValue.replace(/\s+/g, '');
            cleanValue = cleanValue.replace(',', '.');
            cleanValue = cleanValue.replace('%', '');
            return parseFloat(cleanValue) || 0;
        };

        const cleanTextValue = (value) => {
            if (value === null || value === undefined) return '';
            return value.toString().trim().replace(/\s+/g, ' ');
        };

        return rawData.map((row, index) => {
            const article = cleanTextValue(
                row['Article'] || row['article'] ||
                row['Nom'] || row['nom'] ||
                row['Name'] || row['name'] ||
                row['Produit'] || row['produit']
            );

            const unite = cleanTextValue(
                row['Unité'] || row['unité'] || row['Unite'] || row['unite'] ||
                row['Unit'] || row['unit'] || row['Unité de mesure'] || 'pièce'
            );

            const prixHT = cleanNumericValue(
                row['Prix HT'] || row['prix ht'] || row['PrixHT'] || row['prixht'] ||
                row['Price'] || row['price'] || row['Prix'] || row['prix']
            );

            const tva = cleanNumericValue(
                row['TVA'] || row['tva'] || row['Tax'] || row['tax'] ||
                row['VAT'] || row['vat'] || row['Taxe'] || row['taxe']
            );

            const fournisseur = cleanTextValue(
                row['Fournisseur'] || row['fournisseur'] ||
                row['Supplier'] || row['supplier'] ||
                row['Vendeur'] || row['vendeur']
            );

            const categorie = cleanTextValue(
                row['Catégorie'] || row['catégorie'] || row['Categorie'] || row['categorie'] ||
                row['Category'] || row['category'] || row['Type'] || row['type']
            );

            // Validation
            if (!article || article === '') {
                throw new Error(`Ligne ${index + 1}: Nom d'article manquant. Colonnes disponibles: ${Object.keys(row).join(', ')}`);
            }

            if (!unite || unite === '') {
                throw new Error(`Ligne ${index + 1}: Unité manquante pour l'article "${article}"`);
            }

            if (isNaN(prixHT) || prixHT < 0) {
                throw new Error(`Ligne ${index + 1}: Prix HT invalide pour l'article "${article}"`);
            }

            if (isNaN(tva) || tva < 0 || tva > 100) {
                throw new Error(`Ligne ${index + 1}: TVA invalide pour l'article "${article}"`);
            }

            const prixTTC = prixHT * (1 + tva / 100);

            return {
                article: article,
                unite: unite,
                prixHT: prixHT,
                tva: tva,
                prixTTC: prixTTC,
                fournisseur: fournisseur,
                categorie: categorie
            };
        });
    }

    displayMaterialsPreview(data) {
        const previewDiv = document.getElementById('importMaterialsPreview');
        const tbody = document.getElementById('importMaterialsPreviewBody');

        let newMaterials = 0;
        let updateMaterials = 0;
        let newSuppliers = 0;
        const uniqueSuppliers = new Set();

        tbody.innerHTML = data.map(item => {
            // Vérifier si la matière première existe déjà
            const existingMaterial = this.rawMaterials.find(m =>
                m.name.toLowerCase() === item.article.toLowerCase()
            );

            // Vérifier si le fournisseur existe
            const existingSupplier = this.suppliers.find(s =>
                s.name.toLowerCase() === item.fournisseur.toLowerCase()
            );

            if (existingMaterial) {
                updateMaterials++;
            } else {
                newMaterials++;
            }

            if (!existingSupplier && item.fournisseur) {
                uniqueSuppliers.add(item.fournisseur.toLowerCase());
            }

            const statusBadge = existingMaterial ?
                '<span class="badge bg-warning">Mise à jour</span>' :
                '<span class="badge bg-success">Nouveau</span>';

            const supplierBadge = existingSupplier ?
                '<span class="badge bg-info">Existant</span>' :
                '<span class="badge bg-success">Nouveau</span>';

            return `
                <tr>
                    <td>${item.article}</td>
                    <td>${item.unite}</td>
                    <td>${Utils.formatPrice(item.prixHT)}</td>
                    <td>${item.tva.toFixed(1)}%</td>
                    <td>${Utils.formatPrice(item.prixTTC)}</td>
                    <td>${item.fournisseur} ${supplierBadge}</td>
                    <td>${item.categorie}</td>
                    <td>${statusBadge}</td>
                </tr>
            `;
        }).join('');

        newSuppliers = uniqueSuppliers.size;

        document.getElementById('newMaterialsCount').textContent = newMaterials;
        document.getElementById('updateMaterialsCount').textContent = updateMaterials;
        document.getElementById('newSuppliersCount').textContent = newSuppliers;

        previewDiv.style.display = 'block';
        this.importMaterialsData = data;
    }

    async executeImportMaterials() {
        if (!this.importMaterialsData || this.importMaterialsData.length === 0) {
            Utils.showToast('Aucune donnée à importer', 'warning');
            return;
        }

        const createMissingSuppliers = document.getElementById('createMissingSuppliers').checked;
        const updateExisting = document.getElementById('updateExistingMaterials').checked;

        try {
            let importedCount = 0;
            let updatedCount = 0;
            let skippedCount = 0;
            let suppliersCreated = 0;

            for (const item of this.importMaterialsData) {
                // Gérer le fournisseur
                let supplierId = null;
                if (item.fournisseur) {
                    let supplier = this.suppliers.find(s =>
                        s.name.toLowerCase() === item.fournisseur.toLowerCase()
                    );

                    if (!supplier && createMissingSuppliers) {
                        // Créer le fournisseur
                        const newSupplier = {
                            name: item.fournisseur,
                            type: this.getCategoryTypeForSupplier(item.categorie),
                            contact: '',
                            phone: '',
                            email: '',
                            address: '',
                            deliveryTime: 1,
                            status: 'active',
                            notes: 'Créé automatiquement lors de l\'import',
                            createdAt: new Date().toISOString()
                        };

                        supplier = storage.add('suppliers', newSupplier);
                        suppliersCreated++;
                    }

                    if (supplier) {
                        supplierId = supplier.id;
                    }
                }

                // Gérer la catégorie
                let categoryId = null;
                if (item.categorie) {
                    const category = this.categories.find(c =>
                        c.name.toLowerCase() === item.categorie.toLowerCase()
                    );
                    if (category) {
                        categoryId = category.id;
                    } else {
                        // Utiliser la première catégorie par défaut
                        categoryId = this.categories[0]?.id || 1;
                    }
                }

                // Gérer la matière première
                let existingMaterial = this.rawMaterials.find(m =>
                    m.name.toLowerCase() === item.article.toLowerCase()
                );

                if (existingMaterial) {
                    if (updateExisting) {
                        // Mettre à jour la matière première existante
                        storage.update('rawMaterials', existingMaterial.id, {
                            ...existingMaterial,
                            unit: item.unite,
                            categoryId: categoryId || existingMaterial.categoryId,
                            updatedAt: new Date().toISOString()
                        });
                        updatedCount++;
                    } else {
                        skippedCount++;
                        continue;
                    }
                } else {
                    // Créer une nouvelle matière première
                    const newMaterial = {
                        name: item.article,
                        unit: item.unite,
                        categoryId: categoryId || 1,
                        description: `Importé le ${new Date().toLocaleDateString()}`,
                        createdAt: new Date().toISOString()
                    };

                    storage.add('rawMaterials', newMaterial);
                    importedCount++;
                }
            }

            // Recharger les données
            this.loadData();

            // Afficher le résumé
            let message = `Import terminé avec succès !\n`;
            if (importedCount > 0) message += `• ${importedCount} nouvelle(s) matière(s) première(s) créée(s)\n`;
            if (updatedCount > 0) message += `• ${updatedCount} matière(s) première(s) mise(s) à jour\n`;
            if (suppliersCreated > 0) message += `• ${suppliersCreated} nouveau(x) fournisseur(s) créé(s)\n`;
            if (skippedCount > 0) message += `• ${skippedCount} matière(s) première(s) ignorée(s)`;

            Utils.showToast(message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('importMaterialsModal')).hide();

        } catch (error) {
            console.error('Erreur lors de l\'import:', error);
            Utils.showToast('Erreur lors de l\'import: ' + error.message, 'error');
        }
    }

    getCategoryTypeForSupplier(categoryName) {
        const categoryMapping = {
            'viandes': 'viandes',
            'volailles': 'volailles',
            'poissons': 'poissons',
            'légumes': 'légumes',
            'produits laitiers': 'produits-laitiers',
            'épicerie': 'epicerie',
            'alimentation générale': 'alimentation-generale',
            'achats divers': 'achats-divers',
            'produits asiatiques': 'produits-asiatiques',
            'charcuterie': 'charcuterie',
            'boulangerie': 'boulangerie',
            'pâtisserie': 'patisserie',
            'emballage': 'emballage',
            'produits finis': 'produits-finis',
            'boissons': 'boissons'
        };

        const normalizedCategory = categoryName.toLowerCase();
        return categoryMapping[normalizedCategory] || 'alimentation-generale';
    }

    /**
     * Génère et télécharge un bon de commande
     */
    generatePurchaseOrder() {
        if (!this.lastSavedPurchase) {
            Utils.showToast('Aucun achat à exporter', 'warning');
            return;
        }

        const purchase = this.lastSavedPurchase;
        const supplier = this.suppliers.find(s => s.id == purchase.supplierId);

        // Générer le contenu HTML du bon de commande
        const orderHtml = this.generateOrderHTML(purchase, supplier);

        // Créer un blob et télécharger
        const blob = new Blob([orderHtml], { type: 'text/html;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `bon_commande_${purchase.id}_${new Date().toISOString().split('T')[0]}.html`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        Utils.showToast('Bon de commande téléchargé avec succès', 'success');

        // Fermer le modal après téléchargement
        bootstrap.Modal.getInstance(document.getElementById('purchaseModal')).hide();
    }

    /**
     * Génère le HTML du bon de commande
     */
    generateOrderHTML(purchase, supplier) {
        const today = new Date().toLocaleDateString('fr-FR');
        const deliveryDate = new Date(purchase.date);
        deliveryDate.setDate(deliveryDate.getDate() + (supplier?.deliveryTime || 7));

        let totalHT = 0;
        let totalTVA = 0;

        const itemsHtml = purchase.items.map(item => {
            const material = this.rawMaterials.find(m => m.id == item.materialId);
            const itemTotal = item.quantity * item.unitPrice;
            const itemTVA = itemTotal * 0.20; // TVA 20% par défaut

            totalHT += itemTotal;
            totalTVA += itemTVA;

            return `
                <tr>
                    <td>${material ? material.name : 'Article inconnu'}</td>
                    <td>${material ? material.unit : ''}</td>
                    <td style="text-align: right;">${item.quantity.toFixed(2)}</td>
                    <td style="text-align: right;">${Utils.formatPrice(item.unitPrice)}</td>
                    <td style="text-align: right;">${Utils.formatPrice(itemTotal)}</td>
                </tr>
            `;
        }).join('');

        const totalTTC = totalHT + totalTVA;

        return `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bon de Commande - ${purchase.id}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; color: #333; }
        .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #2c3e50; padding-bottom: 20px; }
        .company-info { margin-bottom: 30px; }
        .order-info { display: flex; justify-content: space-between; margin-bottom: 30px; }
        .supplier-info, .order-details { width: 45%; }
        .supplier-info { background: #f8f9fa; padding: 15px; border-radius: 5px; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        th, td { border: 1px solid #ddd; padding: 10px; text-align: left; }
        th { background-color: #2c3e50; color: white; }
        .totals { margin-top: 20px; }
        .totals table { width: 300px; margin-left: auto; }
        .total-row { font-weight: bold; background-color: #f8f9fa; }
        .footer { margin-top: 40px; text-align: center; font-size: 12px; color: #666; }
        @media print { body { margin: 0; } }
    </style>
</head>
<body>
    <div class="header">
        <h1>BON DE COMMANDE</h1>
        <h2>RestoManager</h2>
    </div>

    <div class="company-info">
        <strong>Restaurant [Nom du Restaurant]</strong><br>
        [Adresse du Restaurant]<br>
        [Téléphone] - [Email]
    </div>

    <div class="order-info">
        <div class="supplier-info">
            <h3>Fournisseur</h3>
            <strong>${supplier ? supplier.name : 'Fournisseur inconnu'}</strong><br>
            ${supplier ? supplier.address || '' : ''}<br>
            ${supplier ? supplier.phone || '' : ''}<br>
            ${supplier ? supplier.email || '' : ''}
        </div>

        <div class="order-details">
            <h3>Détails de la commande</h3>
            <strong>N° Commande:</strong> ${purchase.id}<br>
            <strong>Type:</strong> ${purchase.supplierType || supplier?.type || 'Non spécifié'}<br>
            <strong>Date:</strong> ${today}<br>
            <strong>Date de livraison souhaitée:</strong> ${deliveryDate.toLocaleDateString('fr-FR')}<br>
            <strong>Statut:</strong> ${purchase.status || 'En attente'}
        </div>
    </div>

    <table>
        <thead>
            <tr>
                <th>Article</th>
                <th>Unité</th>
                <th>Quantité</th>
                <th>Prix unitaire</th>
                <th>Total HT</th>
            </tr>
        </thead>
        <tbody>
            ${itemsHtml}
        </tbody>
    </table>

    <div class="totals">
        <table>
            <tr>
                <td><strong>Total HT:</strong></td>
                <td style="text-align: right;"><strong>${Utils.formatPrice(totalHT)}</strong></td>
            </tr>
            <tr>
                <td><strong>TVA (20%):</strong></td>
                <td style="text-align: right;"><strong>${Utils.formatPrice(totalTVA)}</strong></td>
            </tr>
            <tr class="total-row">
                <td><strong>Total TTC:</strong></td>
                <td style="text-align: right;"><strong>${Utils.formatPrice(totalTTC)}</strong></td>
            </tr>
        </table>
    </div>

    <div class="footer">
        <p>Bon de commande généré le ${today} par RestoManager</p>
        <p>Merci de confirmer la réception de cette commande</p>
    </div>
</body>
</html>
        `;
    }

    /**
     * Méthode de debug pour vérifier les données
     */
    debugData() {
        console.log('=== DEBUG PURCHASES MODULE ===');
        console.log('Raw Materials:', this.rawMaterials);
        console.log('Inventory:', storage.get('inventory'));
        console.log('Suppliers:', this.suppliers);

        // Test de getInventoryPrice pour chaque matière première
        this.rawMaterials.forEach(material => {
            const price = this.getInventoryPrice(material.id);
            console.log(`${material.name}: Prix = ${price.unitPrice} DH`);
        });
    }

    destroy() {
        if (window.purchasesModule === this) {
            delete window.purchasesModule;
        }
    }
}

// Rendre le module disponible globalement
window.PurchasesModule = PurchasesModule;