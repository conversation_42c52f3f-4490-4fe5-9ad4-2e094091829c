/**
 * RestoManager - Module Pointage & Horaires
 * Système de pointage et calcul des heures travaillées
 */

class TimetrackingModule {
    constructor() {
        this.timeTracking = [];
        this.staff = [];
        this.currentDate = new Date().toISOString().split('T')[0];
    }

    async init() {
        this.loadData();
    }

    loadData() {
        this.timeTracking = storage.get('timeTracking') || [];
        this.staff = storage.get('staff') || [];
    }

    async render() {
        return `
            <div class="timetracking-container">
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card-neomorphic">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <h2 class="mb-0">
                                            <i class="fas fa-clock text-primary me-2"></i>
                                            Pointage & Horaires
                                        </h2>
                                        <p class="text-muted mb-0">Gestion des heures de travail</p>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <button class="btn btn-neomorphic btn-success me-2" onclick="timetrackingModule.showQuickPunch()">
                                            <i class="fas fa-clock me-2"></i>Pointage Rapide
                                        </button>
                                        <button class="btn btn-neomorphic btn-primary" onclick="timetrackingModule.showAddModal()">
                                            <i class="fas fa-plus me-2"></i>Nouveau Pointage
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sélection de date et statistiques -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card-neomorphic">
                            <div class="card-body text-center">
                                <label class="form-label">Date sélectionnée</label>
                                <input type="date" class="form-control" id="selectedDate" value="${this.currentDate}">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-value">${this.getTodayPresentCount()}</div>
                            <div class="stat-label">Présents Aujourd'hui</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-value">${this.getTodayTotalHours()}</div>
                            <div class="stat-label">Heures Travaillées</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-euro-sign"></i>
                            </div>
                            <div class="stat-value">${this.getTodayTotalCost()}</div>
                            <div class="stat-label">Coût Journalier</div>
                        </div>
                    </div>
                </div>

                <!-- Onglets -->
                <div class="row">
                    <div class="col-12">
                        <div class="card-neomorphic">
                            <div class="card-header">
                                <ul class="nav nav-tabs-neomorphic" role="tablist">
                                    <li class="nav-item">
                                        <a class="nav-link active" data-bs-toggle="tab" href="#todayTab">
                                            <i class="fas fa-calendar-day me-1"></i>
                                            Aujourd'hui
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-bs-toggle="tab" href="#historyTab">
                                            <i class="fas fa-history me-1"></i>
                                            Historique
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-bs-toggle="tab" href="#reportsTab">
                                            <i class="fas fa-chart-bar me-1"></i>
                                            Rapports
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <div class="card-body">
                                <div class="tab-content">
                                    <div class="tab-pane fade show active" id="todayTab">
                                        ${this.renderTodayView()}
                                    </div>
                                    <div class="tab-pane fade" id="historyTab">
                                        ${this.renderHistoryView()}
                                    </div>
                                    <div class="tab-pane fade" id="reportsTab">
                                        ${this.renderReportsView()}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                ${this.renderTimetrackingModal()}
                ${this.renderQuickPunchModal()}
            </div>
        `;
    }

    renderTodayView() {
        const todayEntries = this.getTodayEntries();
        const activeStaff = this.staff.filter(s => s.status === 'active');

        return `
            <div class="row">
                ${activeStaff.map(member => {
                    const entries = todayEntries.filter(e => e.staffId === member.id);
                    const isPresent = this.isStaffPresent(member.id);
                    const todayHours = this.calculateDayHours(member.id, this.currentDate);
                    
                    return `
                        <div class="col-lg-6 col-md-12 mb-3">
                            <div class="card-neomorphic">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <div>
                                            <h6 class="mb-0">${member.firstName} ${member.lastName}</h6>
                                            <small class="text-muted">${this.getPositionName(member.position)}</small>
                                        </div>
                                        <span class="badge bg-${isPresent ? 'success' : 'secondary'}">
                                            ${isPresent ? 'Présent' : 'Absent'}
                                        </span>
                                    </div>
                                    
                                    <div class="row mb-2">
                                        <div class="col-6">
                                            <small class="text-muted">Heures aujourd'hui</small>
                                            <div class="fw-bold">${todayHours.toFixed(2)}h</div>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Dernière action</small>
                                            <div class="fw-bold">
                                                ${entries.length > 0 ? 
                                                    Utils.formatTime(entries[entries.length - 1].timestamp) : 
                                                    'Aucune'
                                                }
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-neomorphic btn-success btn-sm flex-fill" 
                                                onclick="timetrackingModule.quickPunch('${member.id}', 'in')"
                                                ${isPresent ? 'disabled' : ''}>
                                            <i class="fas fa-sign-in-alt me-1"></i>Entrée
                                        </button>
                                        <button class="btn btn-neomorphic btn-warning btn-sm flex-fill" 
                                                onclick="timetrackingModule.quickPunch('${member.id}', 'out')"
                                                ${!isPresent ? 'disabled' : ''}>
                                            <i class="fas fa-sign-out-alt me-1"></i>Sortie
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('')}
            </div>
        `;
    }

    renderHistoryView() {
        const recentEntries = this.timeTracking
            .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
            .slice(0, 50);

        if (recentEntries.length === 0) {
            return `
                <div class="text-center py-5">
                    <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Aucun pointage enregistré</h5>
                    <p class="text-muted">L'historique des pointages apparaîtra ici</p>
                </div>
            `;
        }

        return `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Date/Heure</th>
                            <th>Personnel</th>
                            <th>Type</th>
                            <th>Notes</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${recentEntries.map(entry => {
                            const member = this.staff.find(s => s.id === entry.staffId);
                            const typeIcon = entry.type === 'in' ? 'fas fa-sign-in-alt text-success' : 'fas fa-sign-out-alt text-warning';
                            const typeText = entry.type === 'in' ? 'Entrée' : 'Sortie';
                            
                            return `
                                <tr>
                                    <td>
                                        <div class="fw-bold">${Utils.formatDate(entry.timestamp)}</div>
                                        <small class="text-muted">${Utils.formatTime(entry.timestamp)}</small>
                                    </td>
                                    <td>
                                        <div class="fw-bold">${member ? `${member.firstName} ${member.lastName}` : 'Personnel supprimé'}</div>
                                        <small class="text-muted">${member ? this.getPositionName(member.position) : 'N/A'}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-${entry.type === 'in' ? 'success' : 'warning'}">
                                            <i class="${typeIcon} me-1"></i>
                                            ${typeText}
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted">${entry.notes || 'N/A'}</small>
                                    </td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    renderReportsView() {
        return `
            <div class="row">
                <div class="col-md-6">
                    <div class="card-neomorphic mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-chart-bar me-2"></i>
                                Heures par Personnel (Cette Semaine)
                            </h6>
                        </div>
                        <div class="card-body">
                            ${this.renderWeeklyHours()}
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card-neomorphic mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-euro-sign me-2"></i>
                                Coûts de Personnel
                            </h6>
                        </div>
                        <div class="card-body">
                            ${this.renderCostAnalysis()}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    renderTimetrackingModal() {
        return `
            <div class="modal fade" id="timetrackingModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content modal-neomorphic">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-clock me-2"></i>
                                Nouveau Pointage
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="timetrackingForm">
                                <div class="mb-3">
                                    <label class="form-label">Personnel *</label>
                                    <select class="form-select" id="timetrackingStaff" required>
                                        <option value="">Sélectionner un membre</option>
                                        ${this.staff.filter(s => s.status === 'active').map(s => `
                                            <option value="${s.id}">${s.firstName} ${s.lastName}</option>
                                        `).join('')}
                                    </select>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Type *</label>
                                            <select class="form-select" id="timetrackingType" required>
                                                <option value="in">Entrée</option>
                                                <option value="out">Sortie</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Date/Heure *</label>
                                            <input type="datetime-local" class="form-control" id="timetrackingTimestamp" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Notes</label>
                                    <textarea class="form-control" id="timetrackingNotes" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="button" class="btn btn-primary" onclick="timetrackingModule.saveTimetracking()">
                                <i class="fas fa-save me-2"></i>Enregistrer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    renderQuickPunchModal() {
        return `
            <div class="modal fade" id="quickPunchModal" tabindex="-1">
                <div class="modal-dialog modal-sm">
                    <div class="modal-content modal-neomorphic">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-clock me-2"></i>
                                Pointage Rapide
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body text-center">
                            <div class="mb-3">
                                <select class="form-select" id="quickPunchStaff">
                                    <option value="">Sélectionner un membre</option>
                                    ${this.staff.filter(s => s.status === 'active').map(s => `
                                        <option value="${s.id}">${s.firstName} ${s.lastName}</option>
                                    `).join('')}
                                </select>
                            </div>
                            <div class="d-grid gap-2">
                                <button class="btn btn-neomorphic btn-success btn-lg" onclick="timetrackingModule.executeQuickPunch('in')">
                                    <i class="fas fa-sign-in-alt me-2"></i>Entrée
                                </button>
                                <button class="btn btn-neomorphic btn-warning btn-lg" onclick="timetrackingModule.executeQuickPunch('out')">
                                    <i class="fas fa-sign-out-alt me-2"></i>Sortie
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    postRender() {
        this.attachEventListeners();
        window.timetrackingModule = this;
        
        // Initialiser la date/heure actuelle
        const now = new Date();
        const timestampInput = document.getElementById('timetrackingTimestamp');
        if (timestampInput) {
            timestampInput.value = now.toISOString().slice(0, 16);
        }
    }

    attachEventListeners() {
        const dateInput = document.getElementById('selectedDate');
        if (dateInput) {
            dateInput.addEventListener('change', (e) => {
                this.currentDate = e.target.value;
                this.refreshTodayView();
            });
        }
    }

    // Méthodes utilitaires
    getTodayEntries() {
        return this.timeTracking.filter(entry => 
            entry.timestamp.startsWith(this.currentDate)
        );
    }

    getTodayPresentCount() {
        const activeStaff = this.staff.filter(s => s.status === 'active');
        return activeStaff.filter(member => this.isStaffPresent(member.id)).length;
    }

    getTodayTotalHours() {
        const activeStaff = this.staff.filter(s => s.status === 'active');
        const totalHours = activeStaff.reduce((sum, member) => {
            return sum + this.calculateDayHours(member.id, this.currentDate);
        }, 0);
        return totalHours.toFixed(1) + 'h';
    }

    getTodayTotalCost() {
        const activeStaff = this.staff.filter(s => s.status === 'active');
        const totalCost = activeStaff.reduce((sum, member) => {
            const hours = this.calculateDayHours(member.id, this.currentDate);
            const rate = member.hourlyRate || 0;
            return sum + (hours * rate);
        }, 0);
        return Utils.formatPrice(totalCost);
    }

    isStaffPresent(staffId) {
        const todayEntries = this.getTodayEntries().filter(e => e.staffId === staffId);
        if (todayEntries.length === 0) return false;
        
        const lastEntry = todayEntries[todayEntries.length - 1];
        return lastEntry.type === 'in';
    }

    calculateDayHours(staffId, date) {
        const dayEntries = this.timeTracking.filter(entry => 
            entry.staffId === staffId && 
            entry.timestamp.startsWith(date)
        ).sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

        let totalHours = 0;
        let lastIn = null;

        dayEntries.forEach(entry => {
            if (entry.type === 'in') {
                lastIn = new Date(entry.timestamp);
            } else if (entry.type === 'out' && lastIn) {
                const out = new Date(entry.timestamp);
                const hours = (out - lastIn) / (1000 * 60 * 60);
                totalHours += hours;
                lastIn = null;
            }
        });

        return totalHours;
    }

    getPositionName(position) {
        const names = {
            manager: 'Manager',
            chef: 'Chef',
            cook: 'Cuisinier',
            server: 'Serveur',
            cleaner: 'Agent d\'entretien',
            other: 'Autre'
        };
        return names[position] || 'Autre';
    }

    renderWeeklyHours() {
        const activeStaff = this.staff.filter(s => s.status === 'active');
        
        return activeStaff.map(member => {
            const weeklyHours = this.calculateWeeklyHours(member.id);
            return `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div>
                        <div class="fw-bold">${member.firstName} ${member.lastName}</div>
                        <small class="text-muted">${this.getPositionName(member.position)}</small>
                    </div>
                    <span class="badge bg-primary">${weeklyHours.toFixed(1)}h</span>
                </div>
            `;
        }).join('');
    }

    renderCostAnalysis() {
        const activeStaff = this.staff.filter(s => s.status === 'active');
        const totalWeeklyCost = activeStaff.reduce((sum, member) => {
            const hours = this.calculateWeeklyHours(member.id);
            const rate = member.hourlyRate || 0;
            return sum + (hours * rate);
        }, 0);

        return `
            <div class="text-center">
                <div class="mb-3">
                    <h4 class="text-primary">${Utils.formatPrice(totalWeeklyCost)}</h4>
                    <small class="text-muted">Coût total cette semaine</small>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div class="fw-bold">${Utils.formatPrice(totalWeeklyCost / 7)}</div>
                        <small class="text-muted">Coût moyen/jour</small>
                    </div>
                    <div class="col-6">
                        <div class="fw-bold">${Utils.formatPrice(totalWeeklyCost * 52 / 12)}</div>
                        <small class="text-muted">Estimation mensuelle</small>
                    </div>
                </div>
            </div>
        `;
    }

    calculateWeeklyHours(staffId) {
        const now = new Date();
        const monday = new Date(now);
        monday.setDate(now.getDate() - now.getDay() + 1);
        
        let totalHours = 0;
        for (let i = 0; i < 7; i++) {
            const date = new Date(monday);
            date.setDate(monday.getDate() + i);
            const dateStr = date.toISOString().split('T')[0];
            totalHours += this.calculateDayHours(staffId, dateStr);
        }
        
        return totalHours;
    }

    // Actions
    showAddModal() {
        this.resetForm();
        new bootstrap.Modal(document.getElementById('timetrackingModal')).show();
    }

    showQuickPunch() {
        new bootstrap.Modal(document.getElementById('quickPunchModal')).show();
    }

    async quickPunch(staffId, type) {
        try {
            const entry = {
                staffId: staffId,
                type: type,
                timestamp: new Date().toISOString(),
                notes: 'Pointage rapide'
            };

            storage.add('timeTracking', entry);
            
            const member = this.staff.find(s => s.id === staffId);
            const typeText = type === 'in' ? 'entrée' : 'sortie';
            Utils.showToast(`${typeText.charAt(0).toUpperCase() + typeText.slice(1)} enregistrée pour ${member.firstName} ${member.lastName}`, 'success');
            
            this.loadData();
            this.refreshTodayView();

        } catch (error) {
            console.error('Erreur lors du pointage:', error);
            Utils.showToast('Erreur lors du pointage', 'error');
        }
    }

    async executeQuickPunch(type) {
        const staffId = document.getElementById('quickPunchStaff').value;
        if (!staffId) {
            Utils.showToast('Veuillez sélectionner un membre du personnel', 'warning');
            return;
        }

        await this.quickPunch(staffId, type);
        bootstrap.Modal.getInstance(document.getElementById('quickPunchModal')).hide();
    }

    async saveTimetracking() {
        const formData = this.getTimetrackingFormData();
        
        if (!this.validateTimetrackingForm(formData)) {
            return;
        }

        try {
            storage.add('timeTracking', formData);
            Utils.showToast('Pointage enregistré avec succès', 'success');

            this.loadData();
            this.refreshTodayView();
            bootstrap.Modal.getInstance(document.getElementById('timetrackingModal')).hide();

        } catch (error) {
            console.error('Erreur lors de la sauvegarde:', error);
            Utils.showToast('Erreur lors de la sauvegarde', 'error');
        }
    }

    getTimetrackingFormData() {
        return {
            staffId: document.getElementById('timetrackingStaff').value,
            type: document.getElementById('timetrackingType').value,
            timestamp: document.getElementById('timetrackingTimestamp').value,
            notes: document.getElementById('timetrackingNotes').value.trim()
        };
    }

    validateTimetrackingForm(data) {
        if (!data.staffId || !data.type || !data.timestamp) {
            Utils.showToast('Veuillez remplir tous les champs obligatoires', 'warning');
            return false;
        }
        return true;
    }

    resetForm() {
        document.getElementById('timetrackingForm').reset();
        const now = new Date();
        document.getElementById('timetrackingTimestamp').value = now.toISOString().slice(0, 16);
    }

    refreshTodayView() {
        const container = document.getElementById('todayTab');
        if (container) {
            container.innerHTML = this.renderTodayView();
        }
    }

    destroy() {
        if (window.timetrackingModule === this) {
            delete window.timetrackingModule;
        }
    }
}

window.TimetrackingModule = TimetrackingModule;
