/* RestoManager - Styles Neumorphic spécialisés */

/* Composants Neumorphic personnalisés */

/* Conteneurs Neumorphic */
.neomorphic-container {
  background: var(--bg-color);
  border-radius: var(--border-radius-lg);
  box-shadow: 
    12px 12px 24px var(--shadow-dark),
    -12px -12px 24px var(--shadow-light);
  padding: var(--spacing-xl);
  margin: var(--spacing-lg) 0;
  transition: all var(--transition-normal);
}

.neomorphic-container:hover {
  box-shadow: 
    16px 16px 32px var(--shadow-dark),
    -16px -16px 32px var(--shadow-light);
}

/* Boutons Neumorphic */
.btn-neomorphic {
  background: var(--bg-color);
  border: none;
  border-radius: var(--border-radius);
  box-shadow: 
    6px 6px 12px var(--shadow-dark),
    -6px -6px 12px var(--shadow-light);
  color: var(--text-primary);
  font-weight: 600;
  padding: var(--spacing-md) var(--spacing-xl);
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.btn-neomorphic:hover {
  transform: translateY(-1px);
  box-shadow: 
    8px 8px 16px var(--shadow-dark),
    -8px -8px 16px var(--shadow-light);
  color: var(--text-primary);
}

.btn-neomorphic:active {
  transform: translateY(1px);
  box-shadow: 
    inset 4px 4px 8px var(--shadow-dark),
    inset -4px -4px 8px var(--shadow-light);
}

.btn-neomorphic.btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
  color: white;
}

.btn-neomorphic.btn-success {
  background: linear-gradient(135deg, var(--success-color) 0%, #2ecc71 100%);
  color: white;
}

.btn-neomorphic.btn-warning {
  background: linear-gradient(135deg, var(--warning-color) 0%, #e67e22 100%);
  color: white;
}

.btn-neomorphic.btn-danger {
  background: linear-gradient(135deg, var(--danger-color) 0%, #c0392b 100%);
  color: white;
}

/* Inputs Neumorphic */
.input-neomorphic {
  background: var(--bg-color);
  border: none;
  border-radius: var(--border-radius);
  box-shadow: 
    inset 6px 6px 12px var(--shadow-dark),
    inset -6px -6px 12px var(--shadow-light);
  padding: var(--spacing-md);
  font-size: 1rem;
  color: var(--text-primary);
  transition: all var(--transition-fast);
  width: 100%;
}

.input-neomorphic:focus {
  outline: none;
  box-shadow: 
    inset 8px 8px 16px var(--shadow-dark),
    inset -8px -8px 16px var(--shadow-light),
    0 0 0 3px rgba(52, 144, 220, 0.1);
}

.input-neomorphic::placeholder {
  color: var(--text-secondary);
}

/* Cards Neumorphic */
.card-neomorphic {
  background: var(--bg-color);
  border: none;
  border-radius: var(--border-radius-lg);
  box-shadow: 
    10px 10px 20px var(--shadow-dark),
    -10px -10px 20px var(--shadow-light);
  transition: all var(--transition-normal);
  overflow: hidden;
}

.card-neomorphic:hover {
  transform: translateY(-4px);
  box-shadow: 
    15px 15px 30px var(--shadow-dark),
    -15px -15px 30px var(--shadow-light);
}

.card-neomorphic .card-header {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-bottom: 1px solid rgba(163, 177, 198, 0.1);
  padding: var(--spacing-lg);
  font-weight: 700;
  color: var(--text-primary);
}

.card-neomorphic .card-body {
  padding: var(--spacing-xl);
}

/* Statistiques Cards */
.stat-card {
  background: var(--bg-color);
  border-radius: var(--border-radius-lg);
  box-shadow: 
    8px 8px 16px var(--shadow-dark),
    -8px -8px 16px var(--shadow-light);
  padding: var(--spacing-xl);
  text-align: center;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 
    12px 12px 24px var(--shadow-dark),
    -12px -12px 24px var(--shadow-light);
}

.stat-card .stat-icon {
  width: 60px;
  height: 60px;
  margin: 0 auto var(--spacing-md);
  background: var(--bg-color);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 
    6px 6px 12px var(--shadow-dark),
    -6px -6px 12px var(--shadow-light);
}

.stat-card .stat-icon i {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.stat-card .stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.stat-card .stat-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Toggles et Switches */
.switch-neomorphic {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 30px;
}

.switch-neomorphic input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider-neomorphic {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-color);
  border-radius: 30px;
  box-shadow: 
    inset 4px 4px 8px var(--shadow-dark),
    inset -4px -4px 8px var(--shadow-light);
  transition: all var(--transition-fast);
}

.slider-neomorphic:before {
  position: absolute;
  content: "";
  height: 22px;
  width: 22px;
  left: 4px;
  bottom: 4px;
  background: var(--bg-color);
  border-radius: 50%;
  box-shadow: 
    3px 3px 6px var(--shadow-dark),
    -3px -3px 6px var(--shadow-light);
  transition: all var(--transition-fast);
}

input:checked + .slider-neomorphic {
  background: var(--success-color);
}

input:checked + .slider-neomorphic:before {
  transform: translateX(30px);
  background: white;
}

/* Progress Bars */
.progress-neomorphic {
  background: var(--bg-color);
  border-radius: var(--border-radius-lg);
  box-shadow: 
    inset 4px 4px 8px var(--shadow-dark),
    inset -4px -4px 8px var(--shadow-light);
  height: 20px;
  overflow: hidden;
}

.progress-neomorphic .progress-bar {
  background: linear-gradient(90deg, var(--success-color), #2ecc71);
  border-radius: var(--border-radius-lg);
  box-shadow: 
    2px 2px 4px rgba(0, 0, 0, 0.1);
  transition: width var(--transition-normal);
}

/* Modales Neumorphic */
.modal-neomorphic .modal-content {
  background: var(--bg-color);
  border: none;
  border-radius: var(--border-radius-lg);
  box-shadow: 
    20px 20px 40px var(--shadow-dark),
    -20px -20px 40px var(--shadow-light);
}

.modal-neomorphic .modal-header {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-bottom: 1px solid rgba(163, 177, 198, 0.1);
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.modal-neomorphic .modal-footer {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);
  border-top: 1px solid rgba(163, 177, 198, 0.1);
  border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
}

/* Tabs Neumorphic */
.nav-tabs-neomorphic {
  border: none;
  background: var(--bg-color);
  border-radius: var(--border-radius-lg);
  box-shadow: 
    8px 8px 16px var(--shadow-dark),
    -8px -8px 16px var(--shadow-light);
  padding: var(--spacing-sm);
}

.nav-tabs-neomorphic .nav-link {
  border: none;
  background: transparent;
  color: var(--text-secondary);
  border-radius: var(--border-radius);
  margin: 0 var(--spacing-xs);
  transition: all var(--transition-fast);
}

.nav-tabs-neomorphic .nav-link.active {
  background: var(--bg-color);
  color: var(--text-primary);
  box-shadow: 
    4px 4px 8px var(--shadow-dark),
    -4px -4px 8px var(--shadow-light);
}

/* Alerts Neumorphic */
.alert-neomorphic {
  background: var(--bg-color);
  border: none;
  border-radius: var(--border-radius-lg);
  box-shadow: 
    8px 8px 16px var(--shadow-dark),
    -8px -8px 16px var(--shadow-light);
  padding: var(--spacing-lg);
  margin: var(--spacing-lg) 0;
  position: relative;
  overflow: hidden;
}

.alert-neomorphic.alert-success::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--success-color);
}

.alert-neomorphic.alert-warning::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--warning-color);
}

.alert-neomorphic.alert-danger::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--danger-color);
}

.alert-neomorphic.alert-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--info-color);
}

/* Responsive Neumorphic */
@media (max-width: 768px) {
  .neomorphic-container {
    padding: var(--spacing-lg);
    margin: var(--spacing-md) 0;
  }
  
  .stat-card {
    padding: var(--spacing-lg);
  }
  
  .stat-card .stat-icon {
    width: 50px;
    height: 50px;
  }
  
  .stat-card .stat-value {
    font-size: 1.5rem;
  }
}
