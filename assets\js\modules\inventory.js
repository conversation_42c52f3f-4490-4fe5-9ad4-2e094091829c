/**
 * RestoManager - Module Stock & Inventaire
 * Suivi des stocks en temps réel avec alertes et historique
 */

class InventoryModule {
    constructor() {
        this.inventory = [];
        this.rawMaterials = [];
        this.categories = [];
        this.stockMovements = [];
        this.searchTerm = '';
        this.filterCategory = '';
        this.filterStatus = '';
    }

    /**
     * Initialise le module
     */
    async init() {
        this.loadData();
    }

    /**
     * Charge les données depuis le stockage
     */
    loadData() {
        this.inventory = storage.get('inventory') || [];
        this.rawMaterials = storage.get('rawMaterials') || [];
        this.categories = storage.get('materialCategories') || [];
        this.stockMovements = storage.get('stockMovements') || [];
    }

    /**
     * Rend le contenu du module
     */
    async render() {
        return `
            <div class="inventory-container">
                <!-- En-tête -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card-neomorphic">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <h2 class="mb-0">
                                            <i class="fas fa-warehouse text-primary me-2"></i>
                                            Stock & Inventaire
                                        </h2>
                                        <p class="text-muted mb-0">
                                            ${this.inventory.length} article(s) en stock
                                        </p>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <button class="btn btn-neomorphic btn-warning me-2" onclick="inventoryModule.showAdjustmentModal()">
                                            <i class="fas fa-edit me-2"></i>
                                            Ajustement Stock
                                        </button>
                                        <button class="btn btn-neomorphic btn-info" onclick="inventoryModule.exportInventory()">
                                            <i class="fas fa-download me-2"></i>
                                            Export Inventaire
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistiques -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-euro-sign"></i>
                            </div>
                            <div class="stat-value">${this.getTotalValue()}</div>
                            <div class="stat-label">Valeur Totale</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-exclamation-triangle text-warning"></i>
                            </div>
                            <div class="stat-value">${this.getLowStockCount()}</div>
                            <div class="stat-label">Alertes Stock</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-boxes"></i>
                            </div>
                            <div class="stat-value">${this.inventory.length}</div>
                            <div class="stat-label">Articles Différents</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="stat-value">${this.getTodayMovements()}</div>
                            <div class="stat-label">Mouvements Aujourd'hui</div>
                        </div>
                    </div>
                </div>

                <!-- Alertes -->
                ${this.renderAlerts()}

                <!-- Filtres -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" class="form-control" placeholder="Rechercher un article..." 
                                   id="searchInventory" value="${this.searchTerm}">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="filterCategory">
                            <option value="">Toutes les catégories</option>
                            ${this.categories.map(c => `
                                <option value="${c.id}" ${this.filterCategory === c.id ? 'selected' : ''}>
                                    ${c.name}
                                </option>
                            `).join('')}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="filterStatus">
                            <option value="">Tous les statuts</option>
                            <option value="ok" ${this.filterStatus === 'ok' ? 'selected' : ''}>Stock OK</option>
                            <option value="low" ${this.filterStatus === 'low' ? 'selected' : ''}>Stock Faible</option>
                            <option value="out" ${this.filterStatus === 'out' ? 'selected' : ''}>Rupture</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-neomorphic btn-primary w-100" onclick="inventoryModule.refreshData()">
                            <i class="fas fa-sync-alt me-1"></i>
                            Actualiser
                        </button>
                    </div>
                </div>

                <!-- Onglets -->
                <div class="row">
                    <div class="col-12">
                        <div class="card-neomorphic">
                            <div class="card-header">
                                <ul class="nav nav-tabs-neomorphic" role="tablist">
                                    <li class="nav-item">
                                        <a class="nav-link active" data-bs-toggle="tab" href="#stockTab">
                                            <i class="fas fa-warehouse me-1"></i>
                                            Stock Actuel
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-bs-toggle="tab" href="#movementsTab">
                                            <i class="fas fa-exchange-alt me-1"></i>
                                            Mouvements
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-bs-toggle="tab" href="#reportsTab">
                                            <i class="fas fa-chart-bar me-1"></i>
                                            Rapports
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <div class="card-body">
                                <div class="tab-content">
                                    <div class="tab-pane fade show active" id="stockTab">
                                        ${this.renderStockList()}
                                    </div>
                                    <div class="tab-pane fade" id="movementsTab">
                                        ${this.renderMovementsList()}
                                    </div>
                                    <div class="tab-pane fade" id="reportsTab">
                                        ${this.renderReports()}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modales -->
                ${this.renderAdjustmentModal()}
            </div>
        `;
    }

    /**
     * Rend les alertes de stock
     */
    renderAlerts() {
        const lowStockItems = this.getLowStockItems();
        const outOfStockItems = this.getOutOfStockItems();

        if (lowStockItems.length === 0 && outOfStockItems.length === 0) {
            return `
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="alert alert-neomorphic alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            Aucune alerte de stock. Tous les articles sont suffisamment approvisionnés.
                        </div>
                    </div>
                </div>
            `;
        }

        let alertsHtml = '';

        if (outOfStockItems.length > 0) {
            alertsHtml += `
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="alert alert-neomorphic alert-danger">
                            <h6 class="alert-heading">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                Ruptures de Stock (${outOfStockItems.length})
                            </h6>
                            <div class="row">
                                ${outOfStockItems.slice(0, 6).map(item => {
                                    const material = this.rawMaterials.find(m => m.id === item.materialId);
                                    return `
                                        <div class="col-md-4 mb-1">
                                            <small><strong>${material ? material.name : 'Article supprimé'}</strong></small>
                                        </div>
                                    `;
                                }).join('')}
                            </div>
                            ${outOfStockItems.length > 6 ? `<small>... et ${outOfStockItems.length - 6} autre(s)</small>` : ''}
                        </div>
                    </div>
                </div>
            `;
        }

        if (lowStockItems.length > 0) {
            alertsHtml += `
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="alert alert-neomorphic alert-warning">
                            <h6 class="alert-heading">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Stock Faible (${lowStockItems.length})
                            </h6>
                            <div class="row">
                                ${lowStockItems.slice(0, 6).map(item => {
                                    const material = this.rawMaterials.find(m => m.id === item.materialId);
                                    return `
                                        <div class="col-md-4 mb-1">
                                            <small><strong>${material ? material.name : 'Article supprimé'}</strong> (${item.quantity})</small>
                                        </div>
                                    `;
                                }).join('')}
                            </div>
                            ${lowStockItems.length > 6 ? `<small>... et ${lowStockItems.length - 6} autre(s)</small>` : ''}
                        </div>
                    </div>
                </div>
            `;
        }

        return alertsHtml;
    }

    /**
     * Rend la liste du stock
     */
    renderStockList() {
        const filteredInventory = this.getFilteredInventory();

        if (filteredInventory.length === 0) {
            return `
                <div class="text-center py-5">
                    <i class="fas fa-warehouse fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Aucun article en stock</h5>
                    <p class="text-muted">Les articles apparaîtront ici après vos premiers achats</p>
                </div>
            `;
        }

        return `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Article</th>
                            <th>Catégorie</th>
                            <th>Quantité</th>
                            <th>Unité</th>
                            <th>Valeur</th>
                            <th>Prix Moyen</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${filteredInventory.map(item => this.renderStockRow(item)).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    /**
     * Rend une ligne de stock
     */
    renderStockRow(item) {
        const material = this.rawMaterials.find(m => m.id === item.materialId);
        const category = this.categories.find(c => c.id === material?.categoryId);
        const status = this.getStockStatus(item);

        return `
            <tr class="${status.class}">
                <td>
                    <div class="d-flex align-items-center">
                        <div class="material-icon me-3">
                            <i class="fas fa-box"></i>
                        </div>
                        <div>
                            <div class="fw-bold">${material ? material.name : 'Article supprimé'}</div>
                            <small class="text-muted">Dernière entrée: ${item.lastPurchaseDate ? Utils.formatDate(item.lastPurchaseDate) : 'N/A'}</small>
                        </div>
                    </div>
                </td>
                <td>
                    ${category ? `<span class="badge" style="background-color: ${category.color}">${category.name}</span>` : 'N/A'}
                </td>
                <td>
                    <span class="fw-bold ${status.quantityClass}">${Utils.formatNumber(item.quantity, 2)}</span>
                    ${item.minQuantity ? `<br><small class="text-muted">Min: ${item.minQuantity}</small>` : ''}
                </td>
                <td>${material ? material.unit : 'N/A'}</td>
                <td>
                    <span class="fw-bold text-success">${Utils.formatPrice(item.totalValue || 0)}</span>
                </td>
                <td>
                    <span class="text-info">${Utils.formatPrice(item.averagePrice || 0)}</span>
                </td>
                <td>${status.badge}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-warning" onclick="inventoryModule.adjustStock('${item.id}')" 
                                title="Ajuster stock">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="inventoryModule.viewHistory('${item.id}')" 
                                title="Historique">
                            <i class="fas fa-history"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    /**
     * Rend la liste des mouvements
     */
    renderMovementsList() {
        const recentMovements = this.stockMovements
            .sort((a, b) => new Date(b.date) - new Date(a.date))
            .slice(0, 50);

        if (recentMovements.length === 0) {
            return `
                <div class="text-center py-5">
                    <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Aucun mouvement de stock</h5>
                    <p class="text-muted">L'historique des mouvements apparaîtra ici</p>
                </div>
            `;
        }

        return `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Article</th>
                            <th>Type</th>
                            <th>Quantité</th>
                            <th>Stock Après</th>
                            <th>Motif</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${recentMovements.map(movement => this.renderMovementRow(movement)).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    /**
     * Rend une ligne de mouvement
     */
    renderMovementRow(movement) {
        const material = this.rawMaterials.find(m => m.id === movement.materialId);
        const typeIcon = movement.type === 'in' ? 'fas fa-arrow-up text-success' : 'fas fa-arrow-down text-danger';
        const typeText = movement.type === 'in' ? 'Entrée' : 'Sortie';

        return `
            <tr>
                <td>
                    <div class="fw-bold">${Utils.formatDate(movement.date)}</div>
                    <small class="text-muted">${Utils.formatTime(movement.date)}</small>
                </td>
                <td>
                    <div class="fw-bold">${material ? material.name : 'Article supprimé'}</div>
                </td>
                <td>
                    <span class="badge bg-${movement.type === 'in' ? 'success' : 'danger'}">
                        <i class="${typeIcon} me-1"></i>
                        ${typeText}
                    </span>
                </td>
                <td>
                    <span class="fw-bold">${movement.type === 'in' ? '+' : '-'}${Utils.formatNumber(movement.quantity, 2)}</span>
                </td>
                <td>
                    <span class="text-info">${Utils.formatNumber(movement.stockAfter, 2)}</span>
                </td>
                <td>
                    <small class="text-muted">${movement.reason || 'N/A'}</small>
                </td>
            </tr>
        `;
    }

    /**
     * Rend les rapports
     */
    renderReports() {
        return `
            <div class="row">
                <div class="col-md-6">
                    <div class="card-neomorphic mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-chart-pie me-2"></i>
                                Répartition par Catégorie
                            </h6>
                        </div>
                        <div class="card-body">
                            ${this.renderCategoryDistribution()}
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card-neomorphic mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Articles à Réapprovisionner
                            </h6>
                        </div>
                        <div class="card-body">
                            ${this.renderReorderList()}
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <div class="card-neomorphic">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-euro-sign me-2"></i>
                                Valeur du Stock par Catégorie
                            </h6>
                        </div>
                        <div class="card-body">
                            ${this.renderValueByCategory()}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Rend le modal d'ajustement
     */
    renderAdjustmentModal() {
        return `
            <div class="modal fade" id="adjustmentModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content modal-neomorphic">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-edit me-2"></i>
                                Ajustement de Stock
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="adjustmentForm">
                                <div class="mb-3">
                                    <label class="form-label">Article</label>
                                    <select class="form-select" id="adjustmentMaterial" required>
                                        <option value="">Sélectionner un article</option>
                                        ${this.inventory.map(item => {
                                            const material = this.rawMaterials.find(m => m.id === item.materialId);
                                            return `<option value="${item.id}">${material ? material.name : 'Article supprimé'} (Stock: ${item.quantity})</option>`;
                                        }).join('')}
                                    </select>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Type d'ajustement</label>
                                            <select class="form-select" id="adjustmentType" required>
                                                <option value="set">Définir quantité</option>
                                                <option value="add">Ajouter</option>
                                                <option value="remove">Retirer</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Quantité</label>
                                            <input type="number" class="form-control" id="adjustmentQuantity"
                                                   min="0" step="0.01" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Motif</label>
                                    <select class="form-select" id="adjustmentReason">
                                        <option value="correction">Correction d'inventaire</option>
                                        <option value="loss">Perte/Casse</option>
                                        <option value="expiry">Péremption</option>
                                        <option value="theft">Vol</option>
                                        <option value="other">Autre</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Notes</label>
                                    <textarea class="form-control" id="adjustmentNotes" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="button" class="btn btn-warning" onclick="inventoryModule.saveAdjustment()">
                                <i class="fas fa-save me-2"></i>
                                Ajuster Stock
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
