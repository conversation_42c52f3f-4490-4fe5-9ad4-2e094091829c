/**
 * RestoManager - Module Stock & Inventaire
 * Suivi des stocks en temps réel avec alertes et historique
 */

class InventoryModule {
    constructor() {
        this.inventory = [];
        this.rawMaterials = [];
        this.categories = [];
        this.stockMovements = [];
        this.searchTerm = '';
        this.filterCategory = '';
        this.filterStatus = '';
    }

    /**
     * Initialise le module
     */
    async init() {
        this.loadData();
    }

    /**
     * Charge les données depuis le stockage
     */
    loadData() {
        this.inventory = storage.get('inventory') || [];
        this.rawMaterials = storage.get('rawMaterials') || [];
        this.categories = storage.get('materialCategories') || [];
        this.stockMovements = storage.get('stockMovements') || [];
    }

    /**
     * Rend le contenu du module
     */
    async render() {
        return `
            <div class="inventory-container">
                <!-- En-tête -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card-neomorphic">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <h2 class="mb-0">
                                            <i class="fas fa-warehouse text-primary me-2"></i>
                                            Stock & Inventaire
                                        </h2>
                                        <p class="text-muted mb-0">
                                            ${this.inventory.length} article(s) en stock
                                        </p>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <button class="btn btn-neomorphic btn-warning me-2" onclick="inventoryModule.showAdjustmentModal()">
                                            <i class="fas fa-edit me-2"></i>
                                            Ajustement Stock
                                        </button>
                                        <button class="btn btn-neomorphic btn-success me-2" onclick="inventoryModule.showImportModal()">
                                            <i class="fas fa-upload me-2"></i>
                                            Import Excel
                                        </button>
                                        <button class="btn btn-neomorphic btn-info" onclick="inventoryModule.exportInventory()">
                                            <i class="fas fa-download me-2"></i>
                                            Export Inventaire
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistiques -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-euro-sign"></i>
                            </div>
                            <div class="stat-value">${this.getTotalValue()}</div>
                            <div class="stat-label">Valeur Totale</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-exclamation-triangle text-warning"></i>
                            </div>
                            <div class="stat-value">${this.getLowStockCount()}</div>
                            <div class="stat-label">Alertes Stock</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-boxes"></i>
                            </div>
                            <div class="stat-value">${this.inventory.length}</div>
                            <div class="stat-label">Articles Différents</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="stat-value">${this.getTodayMovements()}</div>
                            <div class="stat-label">Mouvements Aujourd'hui</div>
                        </div>
                    </div>
                </div>

                <!-- Alertes -->
                ${this.renderAlerts()}

                <!-- Filtres -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" class="form-control" placeholder="Rechercher un article..." 
                                   id="searchInventory" value="${this.searchTerm}">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="filterCategory">
                            <option value="">Toutes les catégories</option>
                            ${this.categories.map(c => `
                                <option value="${c.id}" ${this.filterCategory === c.id ? 'selected' : ''}>
                                    ${c.name}
                                </option>
                            `).join('')}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="filterStatus">
                            <option value="">Tous les statuts</option>
                            <option value="ok" ${this.filterStatus === 'ok' ? 'selected' : ''}>Stock OK</option>
                            <option value="low" ${this.filterStatus === 'low' ? 'selected' : ''}>Stock Faible</option>
                            <option value="out" ${this.filterStatus === 'out' ? 'selected' : ''}>Rupture</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-neomorphic btn-primary w-100" onclick="inventoryModule.refreshData()">
                            <i class="fas fa-sync-alt me-1"></i>
                            Actualiser
                        </button>
                    </div>
                </div>

                <!-- Onglets -->
                <div class="row">
                    <div class="col-12">
                        <div class="card-neomorphic">
                            <div class="card-header">
                                <ul class="nav nav-tabs-neomorphic" role="tablist">
                                    <li class="nav-item">
                                        <a class="nav-link active" data-bs-toggle="tab" href="#stockTab">
                                            <i class="fas fa-warehouse me-1"></i>
                                            Stock Actuel
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-bs-toggle="tab" href="#movementsTab">
                                            <i class="fas fa-exchange-alt me-1"></i>
                                            Mouvements
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-bs-toggle="tab" href="#reportsTab">
                                            <i class="fas fa-chart-bar me-1"></i>
                                            Rapports
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <div class="card-body">
                                <div class="tab-content">
                                    <div class="tab-pane fade show active" id="stockTab">
                                        ${this.renderStockList()}
                                    </div>
                                    <div class="tab-pane fade" id="movementsTab">
                                        ${this.renderMovementsList()}
                                    </div>
                                    <div class="tab-pane fade" id="reportsTab">
                                        ${this.renderReports()}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modales -->
                ${this.renderAdjustmentModal()}
                ${this.renderImportModal()}
            </div>
        `;
    }

    /**
     * Rend les alertes de stock
     */
    renderAlerts() {
        const lowStockItems = this.getLowStockItems();
        const outOfStockItems = this.getOutOfStockItems();

        if (lowStockItems.length === 0 && outOfStockItems.length === 0) {
            return `
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="alert alert-neomorphic alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            Aucune alerte de stock. Tous les articles sont suffisamment approvisionnés.
                        </div>
                    </div>
                </div>
            `;
        }

        let alertsHtml = '';

        if (outOfStockItems.length > 0) {
            alertsHtml += `
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="alert alert-neomorphic alert-danger">
                            <h6 class="alert-heading">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                Ruptures de Stock (${outOfStockItems.length})
                            </h6>
                            <div class="row">
                                ${outOfStockItems.slice(0, 6).map(item => {
                                    const material = this.rawMaterials.find(m => m.id === item.materialId);
                                    return `
                                        <div class="col-md-4 mb-1">
                                            <small><strong>${material ? material.name : 'Article supprimé'}</strong></small>
                                        </div>
                                    `;
                                }).join('')}
                            </div>
                            ${outOfStockItems.length > 6 ? `<small>... et ${outOfStockItems.length - 6} autre(s)</small>` : ''}
                        </div>
                    </div>
                </div>
            `;
        }

        if (lowStockItems.length > 0) {
            alertsHtml += `
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="alert alert-neomorphic alert-warning">
                            <h6 class="alert-heading">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Stock Faible (${lowStockItems.length})
                            </h6>
                            <div class="row">
                                ${lowStockItems.slice(0, 6).map(item => {
                                    const material = this.rawMaterials.find(m => m.id === item.materialId);
                                    return `
                                        <div class="col-md-4 mb-1">
                                            <small><strong>${material ? material.name : 'Article supprimé'}</strong> (${item.quantity})</small>
                                        </div>
                                    `;
                                }).join('')}
                            </div>
                            ${lowStockItems.length > 6 ? `<small>... et ${lowStockItems.length - 6} autre(s)</small>` : ''}
                        </div>
                    </div>
                </div>
            `;
        }

        return alertsHtml;
    }

    /**
     * Rend la liste du stock
     */
    renderStockList() {
        const filteredInventory = this.getFilteredInventory();

        if (filteredInventory.length === 0) {
            return `
                <div class="text-center py-5">
                    <i class="fas fa-warehouse fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Aucun article en stock</h5>
                    <p class="text-muted">Les articles apparaîtront ici après vos premiers achats</p>
                </div>
            `;
        }

        return `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Article</th>
                            <th>Catégorie</th>
                            <th>Quantité</th>
                            <th>Unité</th>
                            <th>Valeur</th>
                            <th>Prix Moyen</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${filteredInventory.map(item => this.renderStockRow(item)).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    /**
     * Rend une ligne de stock
     */
    renderStockRow(item) {
        const material = this.rawMaterials.find(m => m.id === item.materialId);
        const category = this.categories.find(c => c.id === material?.categoryId);
        const status = this.getStockStatus(item);

        return `
            <tr class="${status.class}">
                <td>
                    <div class="d-flex align-items-center">
                        <div class="material-icon me-3">
                            <i class="fas fa-box"></i>
                        </div>
                        <div>
                            <div class="fw-bold">${material ? material.name : 'Article supprimé'}</div>
                            <small class="text-muted">Dernière entrée: ${item.lastPurchaseDate ? Utils.formatDate(item.lastPurchaseDate) : 'N/A'}</small>
                        </div>
                    </div>
                </td>
                <td>
                    ${category ? `<span class="badge" style="background-color: ${category.color}">${category.name}</span>` : 'N/A'}
                </td>
                <td>
                    <span class="fw-bold ${status.quantityClass}">${Utils.formatNumber(item.quantity, 2)}</span>
                    ${item.minQuantity ? `<br><small class="text-muted">Min: ${item.minQuantity}</small>` : ''}
                </td>
                <td>${material ? material.unit : 'N/A'}</td>
                <td>
                    <span class="fw-bold text-success">${Utils.formatPrice(item.totalValue || 0)}</span>
                </td>
                <td>
                    <span class="text-info">${Utils.formatPrice(item.averagePrice || 0)}</span>
                </td>
                <td>${status.badge}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-warning" onclick="inventoryModule.adjustStock('${item.id}')" 
                                title="Ajuster stock">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="inventoryModule.viewHistory('${item.id}')" 
                                title="Historique">
                            <i class="fas fa-history"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    /**
     * Rend la liste des mouvements
     */
    renderMovementsList() {
        const recentMovements = this.stockMovements
            .sort((a, b) => new Date(b.date) - new Date(a.date))
            .slice(0, 50);

        if (recentMovements.length === 0) {
            return `
                <div class="text-center py-5">
                    <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Aucun mouvement de stock</h5>
                    <p class="text-muted">L'historique des mouvements apparaîtra ici</p>
                </div>
            `;
        }

        return `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Article</th>
                            <th>Type</th>
                            <th>Quantité</th>
                            <th>Stock Après</th>
                            <th>Motif</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${recentMovements.map(movement => this.renderMovementRow(movement)).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    /**
     * Rend une ligne de mouvement
     */
    renderMovementRow(movement) {
        const material = this.rawMaterials.find(m => m.id === movement.materialId);
        const typeIcon = movement.type === 'in' ? 'fas fa-arrow-up text-success' : 'fas fa-arrow-down text-danger';
        const typeText = movement.type === 'in' ? 'Entrée' : 'Sortie';

        return `
            <tr>
                <td>
                    <div class="fw-bold">${Utils.formatDate(movement.date)}</div>
                    <small class="text-muted">${Utils.formatTime(movement.date)}</small>
                </td>
                <td>
                    <div class="fw-bold">${material ? material.name : 'Article supprimé'}</div>
                </td>
                <td>
                    <span class="badge bg-${movement.type === 'in' ? 'success' : 'danger'}">
                        <i class="${typeIcon} me-1"></i>
                        ${typeText}
                    </span>
                </td>
                <td>
                    <span class="fw-bold">${movement.type === 'in' ? '+' : '-'}${Utils.formatNumber(movement.quantity, 2)}</span>
                </td>
                <td>
                    <span class="text-info">${Utils.formatNumber(movement.stockAfter, 2)}</span>
                </td>
                <td>
                    <small class="text-muted">${movement.reason || 'N/A'}</small>
                </td>
            </tr>
        `;
    }

    /**
     * Rend les rapports
     */
    renderReports() {
        return `
            <div class="row">
                <div class="col-md-6">
                    <div class="card-neomorphic mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-chart-pie me-2"></i>
                                Répartition par Catégorie
                            </h6>
                        </div>
                        <div class="card-body">
                            ${this.renderCategoryDistribution()}
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card-neomorphic mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Articles à Réapprovisionner
                            </h6>
                        </div>
                        <div class="card-body">
                            ${this.renderReorderList()}
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <div class="card-neomorphic">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-euro-sign me-2"></i>
                                Valeur du Stock par Catégorie
                            </h6>
                        </div>
                        <div class="card-body">
                            ${this.renderValueByCategory()}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Rend le modal d'ajustement
     */
    renderAdjustmentModal() {
        return `
            <div class="modal fade" id="adjustmentModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content modal-neomorphic">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-edit me-2"></i>
                                Ajustement de Stock
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="adjustmentForm">
                                <div class="mb-3">
                                    <label class="form-label">Article</label>
                                    <select class="form-select" id="adjustmentMaterial" required>
                                        <option value="">Sélectionner un article</option>
                                        ${this.inventory.map(item => {
                                            const material = this.rawMaterials.find(m => m.id === item.materialId);
                                            return `<option value="${item.id}">${material ? material.name : 'Article supprimé'} (Stock: ${item.quantity})</option>`;
                                        }).join('')}
                                    </select>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Type d'ajustement</label>
                                            <select class="form-select" id="adjustmentType" required>
                                                <option value="set">Définir quantité</option>
                                                <option value="add">Ajouter</option>
                                                <option value="remove">Retirer</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Quantité</label>
                                            <input type="number" class="form-control" id="adjustmentQuantity"
                                                   min="0" step="0.01" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Motif</label>
                                    <select class="form-select" id="adjustmentReason">
                                        <option value="correction">Correction d'inventaire</option>
                                        <option value="loss">Perte/Casse</option>
                                        <option value="expiry">Péremption</option>
                                        <option value="theft">Vol</option>
                                        <option value="other">Autre</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Notes</label>
                                    <textarea class="form-control" id="adjustmentNotes" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="button" class="btn btn-warning" onclick="inventoryModule.saveAdjustment()">
                                <i class="fas fa-save me-2"></i>
                                Ajuster Stock
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Rend le modal d'import Excel
     */
    renderImportModal() {
        return `
            <div class="modal fade" id="importModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content modal-neomorphic">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-upload me-2"></i>
                                Import Inventaire Excel
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-neomorphic alert-info">
                                <h6 class="alert-heading">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Format du fichier Excel requis
                                </h6>
                                <p class="mb-2">Le fichier Excel doit contenir les colonnes suivantes :</p>
                                <ul class="mb-2">
                                    <li><strong>Article</strong> : Nom de l'article</li>
                                    <li><strong>Quantité</strong> : Quantité en stock</li>
                                    <li><strong>Prix HT</strong> : Prix unitaire hors taxes</li>
                                    <li><strong>TVA</strong> : Taux de TVA (en %, ex: 20)</li>
                                </ul>
                                <p class="mb-0">
                                    <small class="text-muted">
                                        L'application calculera automatiquement le montant HT, TTC par article et le total du stock.
                                    </small>
                                </p>
                            </div>

                            <form id="importForm">
                                <div class="mb-3">
                                    <label class="form-label">Fichier Excel (.xlsx, .xls, .csv) *</label>
                                    <input type="file" class="form-control" id="importFile"
                                           accept=".xlsx,.xls,.csv" required>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Catégorie par défaut</label>
                                            <select class="form-select" id="defaultCategory">
                                                <option value="">Sélectionner une catégorie</option>
                                                ${this.categories.map(c => `
                                                    <option value="${c.id}">${c.name}</option>
                                                `).join('')}
                                            </select>
                                            <small class="text-muted">Utilisée si l'article n'existe pas encore</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Unité par défaut</label>
                                            <select class="form-select" id="defaultUnit">
                                                <option value="pièce">Pièce</option>
                                                <option value="kg">Kilogramme</option>
                                                <option value="litre">Litre</option>
                                                <option value="gramme">Gramme</option>
                                                <option value="ml">Millilitre</option>
                                                <option value="boîte">Boîte</option>
                                                <option value="paquet">Paquet</option>
                                            </select>
                                            <small class="text-muted">Utilisée si l'article n'existe pas encore</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="updateExisting" checked>
                                        <label class="form-check-label" for="updateExisting">
                                            Mettre à jour les articles existants
                                        </label>
                                        <small class="form-text text-muted d-block">
                                            Si décoché, seuls les nouveaux articles seront ajoutés
                                        </small>
                                    </div>
                                </div>

                                <div id="importPreview" class="mt-3" style="display: none;">
                                    <h6>Aperçu des données :</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Article</th>
                                                    <th>Quantité</th>
                                                    <th>Prix HT</th>
                                                    <th>TVA</th>
                                                    <th>Prix TTC</th>
                                                    <th>Montant HT</th>
                                                    <th>Montant TTC</th>
                                                    <th>Statut</th>
                                                </tr>
                                            </thead>
                                            <tbody id="importPreviewBody">
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-md-6">
                                            <div class="card bg-light">
                                                <div class="card-body text-center">
                                                    <h6>Total Stock HT</h6>
                                                    <h4 class="text-primary" id="totalHT">0,00 €</h4>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="card bg-light">
                                                <div class="card-body text-center">
                                                    <h6>Total Stock TTC</h6>
                                                    <h4 class="text-success" id="totalTTC">0,00 €</h4>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="button" class="btn btn-info" onclick="inventoryModule.previewImport()" id="previewBtn">
                                <i class="fas fa-eye me-2"></i>Aperçu
                            </button>
                            <button type="button" class="btn btn-success" onclick="inventoryModule.executeImport()"
                                    id="importBtn" style="display: none;">
                                <i class="fas fa-upload me-2"></i>Importer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Actions post-rendu
     */
    postRender() {
        this.attachEventListeners();
        window.inventoryModule = this;
    }

    /**
     * Attache les gestionnaires d'événements
     */
    attachEventListeners() {
        // Recherche
        const searchInput = document.getElementById('searchInventory');
        if (searchInput) {
            searchInput.addEventListener('input', Utils.debounce((e) => {
                this.searchTerm = e.target.value;
                this.refreshStockList();
            }, 300));
        }

        // Filtres
        const categoryFilter = document.getElementById('filterCategory');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', (e) => {
                this.filterCategory = e.target.value;
                this.refreshStockList();
            });
        }

        const statusFilter = document.getElementById('filterStatus');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filterStatus = e.target.value;
                this.refreshStockList();
            });
        }
    }

    // Méthodes utilitaires
    getTotalValue() {
        const total = Utils.sumBy(this.inventory, 'totalValue');
        return Utils.formatPrice(total);
    }

    getLowStockCount() {
        return this.getLowStockItems().length;
    }

    getTodayMovements() {
        const today = new Date().toDateString();
        return this.stockMovements.filter(m => new Date(m.date).toDateString() === today).length;
    }

    getLowStockItems() {
        return this.inventory.filter(item =>
            item.quantity > 0 && item.quantity <= (item.minQuantity || 10)
        );
    }

    getOutOfStockItems() {
        return this.inventory.filter(item => item.quantity <= 0);
    }

    getFilteredInventory() {
        let filtered = [...this.inventory];

        // Filtrage par recherche
        if (this.searchTerm) {
            const term = this.searchTerm.toLowerCase();
            filtered = filtered.filter(item => {
                const material = this.rawMaterials.find(m => m.id === item.materialId);
                return material && material.name.toLowerCase().includes(term);
            });
        }

        // Filtrage par catégorie
        if (this.filterCategory) {
            filtered = filtered.filter(item => {
                const material = this.rawMaterials.find(m => m.id === item.materialId);
                return material && material.categoryId == this.filterCategory;
            });
        }

        // Filtrage par statut
        if (this.filterStatus) {
            filtered = filtered.filter(item => {
                const status = this.getStockStatus(item);
                return status.type === this.filterStatus;
            });
        }

        return filtered.sort((a, b) => {
            const materialA = this.rawMaterials.find(m => m.id === a.materialId);
            const materialB = this.rawMaterials.find(m => m.id === b.materialId);
            return (materialA?.name || '').localeCompare(materialB?.name || '');
        });
    }

    getStockStatus(item) {
        if (item.quantity <= 0) {
            return {
                type: 'out',
                badge: '<span class="badge bg-danger">Rupture</span>',
                class: 'table-danger',
                quantityClass: 'text-danger'
            };
        } else if (item.quantity <= (item.minQuantity || 10)) {
            return {
                type: 'low',
                badge: '<span class="badge bg-warning">Stock Faible</span>',
                class: 'table-warning',
                quantityClass: 'text-warning'
            };
        } else {
            return {
                type: 'ok',
                badge: '<span class="badge bg-success">OK</span>',
                class: '',
                quantityClass: 'text-success'
            };
        }
    }

    renderCategoryDistribution() {
        const distribution = {};
        this.inventory.forEach(item => {
            const material = this.rawMaterials.find(m => m.id === item.materialId);
            const category = this.categories.find(c => c.id === material?.categoryId);
            const categoryName = category ? category.name : 'Sans catégorie';

            if (!distribution[categoryName]) {
                distribution[categoryName] = { count: 0, color: category?.color || '#6c757d' };
            }
            distribution[categoryName].count++;
        });

        return Object.entries(distribution).map(([name, data]) => `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <div class="d-flex align-items-center">
                    <div class="category-color me-2" style="width: 12px; height: 12px; background-color: ${data.color}; border-radius: 50%;"></div>
                    <span>${name}</span>
                </div>
                <span class="badge bg-secondary">${data.count}</span>
            </div>
        `).join('');
    }

    renderReorderList() {
        const reorderItems = [...this.getLowStockItems(), ...this.getOutOfStockItems()];

        if (reorderItems.length === 0) {
            return '<p class="text-muted">Aucun article à réapprovisionner</p>';
        }

        return reorderItems.slice(0, 10).map(item => {
            const material = this.rawMaterials.find(m => m.id === item.materialId);
            const status = this.getStockStatus(item);

            return `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div>
                        <div class="fw-bold">${material ? material.name : 'Article supprimé'}</div>
                        <small class="text-muted">Stock: ${item.quantity} ${material?.unit || ''}</small>
                    </div>
                    ${status.badge}
                </div>
            `;
        }).join('');
    }

    renderValueByCategory() {
        const valueByCategory = {};
        this.inventory.forEach(item => {
            const material = this.rawMaterials.find(m => m.id === item.materialId);
            const category = this.categories.find(c => c.id === material?.categoryId);
            const categoryName = category ? category.name : 'Sans catégorie';

            if (!valueByCategory[categoryName]) {
                valueByCategory[categoryName] = { value: 0, color: category?.color || '#6c757d' };
            }
            valueByCategory[categoryName].value += item.totalValue || 0;
        });

        return `
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Catégorie</th>
                            <th>Valeur</th>
                            <th>Pourcentage</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${Object.entries(valueByCategory).map(([name, data]) => {
                            const total = Object.values(valueByCategory).reduce((sum, cat) => sum + cat.value, 0);
                            const percentage = total > 0 ? (data.value / total * 100).toFixed(1) : 0;

                            return `
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="category-color me-2" style="width: 12px; height: 12px; background-color: ${data.color}; border-radius: 50%;"></div>
                                            ${name}
                                        </div>
                                    </td>
                                    <td class="fw-bold text-success">${Utils.formatPrice(data.value)}</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar" style="width: ${percentage}%; background-color: ${data.color};">
                                                ${percentage}%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    // Actions
    showAdjustmentModal() {
        new bootstrap.Modal(document.getElementById('adjustmentModal')).show();
    }

    async saveAdjustment() {
        const itemId = document.getElementById('adjustmentMaterial').value;
        const type = document.getElementById('adjustmentType').value;
        const quantity = parseFloat(document.getElementById('adjustmentQuantity').value);
        const reason = document.getElementById('adjustmentReason').value;
        const notes = document.getElementById('adjustmentNotes').value;

        if (!itemId || !type || isNaN(quantity)) {
            Utils.showToast('Veuillez remplir tous les champs obligatoires', 'warning');
            return;
        }

        try {
            const item = this.inventory.find(i => i.id === itemId);
            if (!item) return;

            let newQuantity = item.quantity;
            let movementType = 'out';

            switch (type) {
                case 'set':
                    newQuantity = quantity;
                    movementType = quantity > item.quantity ? 'in' : 'out';
                    break;
                case 'add':
                    newQuantity = item.quantity + quantity;
                    movementType = 'in';
                    break;
                case 'remove':
                    newQuantity = Math.max(0, item.quantity - quantity);
                    movementType = 'out';
                    break;
            }

            // Mettre à jour l'inventaire
            storage.update('inventory', itemId, {
                ...item,
                quantity: newQuantity,
                updatedAt: new Date().toISOString()
            });

            // Enregistrer le mouvement
            storage.add('stockMovements', {
                materialId: item.materialId,
                type: movementType,
                quantity: Math.abs(newQuantity - item.quantity),
                stockBefore: item.quantity,
                stockAfter: newQuantity,
                reason: reason,
                notes: notes,
                date: new Date().toISOString()
            });

            Utils.showToast('Ajustement de stock effectué avec succès', 'success');
            this.loadData();
            this.refreshStockList();
            bootstrap.Modal.getInstance(document.getElementById('adjustmentModal')).hide();

        } catch (error) {
            console.error('Erreur lors de l\'ajustement:', error);
            Utils.showToast('Erreur lors de l\'ajustement', 'error');
        }
    }

    refreshData() {
        this.loadData();
        this.refreshStockList();
        Utils.showToast('Données actualisées', 'success');
    }

    refreshStockList() {
        const container = document.getElementById('stockTab');
        if (container) {
            container.innerHTML = this.renderStockList();
        }
    }

    exportInventory() {
        const data = this.getFilteredInventory().map(item => {
            const material = this.rawMaterials.find(m => m.id === item.materialId);
            const category = this.categories.find(c => c.id === material?.categoryId);

            return {
                article: material ? material.name : 'Article supprimé',
                categorie: category ? category.name : 'Sans catégorie',
                quantite: item.quantity,
                unite: material ? material.unit : 'N/A',
                valeur_totale: item.totalValue || 0,
                prix_moyen: item.averagePrice || 0,
                stock_minimum: item.minQuantity || 0,
                derniere_entree: item.lastPurchaseDate || 'N/A'
            };
        });

        const filename = `inventaire_${new Date().toISOString().split('T')[0]}.csv`;
        Utils.exportToCSV(data, filename);
        Utils.showToast('Export réalisé avec succès', 'success');
    }

    // Méthodes d'import
    showImportModal() {
        new bootstrap.Modal(document.getElementById('importModal')).show();
    }

    async previewImport() {
        const fileInput = document.getElementById('importFile');
        const file = fileInput.files[0];

        if (!file) {
            Utils.showToast('Veuillez sélectionner un fichier', 'warning');
            return;
        }

        try {
            const data = await this.parseExcelFile(file);
            this.displayImportPreview(data);

            document.getElementById('previewBtn').style.display = 'none';
            document.getElementById('importBtn').style.display = 'inline-block';

        } catch (error) {
            console.error('Erreur lors de la lecture du fichier:', error);
            Utils.showToast('Erreur lors de la lecture du fichier: ' + error.message, 'error');
        }
    }

    async parseExcelFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();

            reader.onload = (e) => {
                try {
                    const data = new Uint8Array(e.target.result);
                    let workbook;

                    // Utiliser SheetJS si disponible, sinon parser CSV
                    if (window.XLSX) {
                        workbook = XLSX.read(data, { type: 'array' });
                        const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
                        const jsonData = XLSX.utils.sheet_to_json(firstSheet);
                        resolve(this.normalizeImportData(jsonData));
                    } else {
                        // Fallback pour CSV
                        const text = new TextDecoder().decode(data);
                        const csvData = this.parseCSV(text);
                        resolve(this.normalizeImportData(csvData));
                    }
                } catch (error) {
                    reject(error);
                }
            };

            reader.onerror = () => reject(new Error('Erreur de lecture du fichier'));
            reader.readAsArrayBuffer(file);
        });
    }

    parseCSV(text) {
        const lines = text.split('\n').filter(line => line.trim());
        if (lines.length < 2) throw new Error('Le fichier CSV doit contenir au moins une ligne d\'en-tête et une ligne de données');

        const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
        const data = [];

        for (let i = 1; i < lines.length; i++) {
            const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
            const row = {};
            headers.forEach((header, index) => {
                row[header] = values[index] || '';
            });
            data.push(row);
        }

        return data;
    }

    normalizeImportData(rawData) {
        console.log('Données brutes reçues:', rawData);

        return rawData.map((row, index) => {
            console.log(`Ligne ${index + 1}:`, row);

            // Fonction pour nettoyer les valeurs numériques
            const cleanNumericValue = (value) => {
                if (value === null || value === undefined || value === '') return 0;
                let cleanValue = value.toString().trim();

                // Supprimer les espaces supplémentaires
                cleanValue = cleanValue.replace(/\s+/g, '');

                // Remplacer virgule par point pour les décimaux
                cleanValue = cleanValue.replace(',', '.');

                // Supprimer le symbole %
                cleanValue = cleanValue.replace('%', '');

                return parseFloat(cleanValue) || 0;
            };

            // Fonction pour nettoyer les valeurs texte
            const cleanTextValue = (value) => {
                if (value === null || value === undefined) return '';
                return value.toString().trim().replace(/\s+/g, ' ');
            };

            // Extraire les valeurs directement avec nettoyage
            const article = cleanTextValue(
                row['Article'] || row['article'] ||
                row['Nom'] || row['nom'] ||
                row['Name'] || row['name'] ||
                row['Produit'] || row['produit']
            );

            const quantiteRaw = row['Quantité'] || row['quantité'] || row['Quantite'] || row['quantite'] ||
                               row['Quantity'] || row['quantity'] || row['Qty'] || row['qty'] || '0';
            const quantite = cleanNumericValue(quantiteRaw);

            const prixHTRaw = row['Prix HT'] || row['prix ht'] || row['PrixHT'] || row['prixht'] ||
                             row['Price'] || row['price'] || row['Prix'] || row['prix'] || '0';
            const prixHT = cleanNumericValue(prixHTRaw);

            const tvaRaw = row['TVA'] || row['tva'] || row['Tax'] || row['tax'] ||
                          row['VAT'] || row['vat'] || row['Taxe'] || row['taxe'] || '0';
            const tva = cleanNumericValue(tvaRaw);

            console.log(`Valeurs extraites - Article: "${article}", Quantité: ${quantite} (de "${quantiteRaw}"), Prix HT: ${prixHT} (de "${prixHTRaw}"), TVA: ${tva} (de "${tvaRaw}")`);

            // Validation avec messages d'erreur détaillés
            if (!article || article === '') {
                throw new Error(`Ligne ${index + 1}: Nom d'article manquant ou vide. Colonnes disponibles: ${Object.keys(row).join(', ')}`);
            }

            if (isNaN(quantite) || quantite < 0) {
                throw new Error(`Ligne ${index + 1}: Quantité invalide "${quantiteRaw}" (résultat: ${quantite}) pour l'article "${article}". La quantité doit être un nombre positif ou zéro.`);
            }

            if (isNaN(prixHT) || prixHT < 0) {
                throw new Error(`Ligne ${index + 1}: Prix HT invalide "${prixHTRaw}" (résultat: ${prixHT}) pour l'article "${article}". Le prix doit être un nombre positif ou zéro.`);
            }

            if (isNaN(tva) || tva < 0 || tva > 100) {
                throw new Error(`Ligne ${index + 1}: TVA invalide "${tvaRaw}" (résultat: ${tva}%) pour l'article "${article}". La TVA doit être entre 0 et 100%.`);
            }

            const prixTTC = prixHT * (1 + tva / 100);
            const montantHT = quantite * prixHT;
            const montantTTC = quantite * prixTTC;

            return {
                article: article.toString().trim(),
                quantite: quantite,
                prixHT: prixHT,
                tva: tva,
                prixTTC: prixTTC,
                montantHT: montantHT,
                montantTTC: montantTTC
            };
        }).filter(item => item !== null);
    }

    displayImportPreview(data) {
        const previewDiv = document.getElementById('importPreview');
        const tbody = document.getElementById('importPreviewBody');

        let totalHT = 0;
        let totalTTC = 0;

        tbody.innerHTML = data.map(item => {
            totalHT += item.montantHT;
            totalTTC += item.montantTTC;

            // Vérifier si l'article existe déjà
            const existingMaterial = this.rawMaterials.find(m =>
                m.name.toLowerCase() === item.article.toLowerCase()
            );

            const statusBadge = existingMaterial ?
                '<span class="badge bg-warning">Existe</span>' :
                '<span class="badge bg-success">Nouveau</span>';

            return `
                <tr>
                    <td>${item.article}</td>
                    <td>${Utils.formatNumber(item.quantite, 2)}</td>
                    <td>${Utils.formatPrice(item.prixHT)}</td>
                    <td>${item.tva}%</td>
                    <td>${Utils.formatPrice(item.prixTTC)}</td>
                    <td>${Utils.formatPrice(item.montantHT)}</td>
                    <td>${Utils.formatPrice(item.montantTTC)}</td>
                    <td>${statusBadge}</td>
                </tr>
            `;
        }).join('');

        document.getElementById('totalHT').textContent = Utils.formatPrice(totalHT);
        document.getElementById('totalTTC').textContent = Utils.formatPrice(totalTTC);

        previewDiv.style.display = 'block';
        this.importData = data;
    }

    async executeImport() {
        if (!this.importData || this.importData.length === 0) {
            Utils.showToast('Aucune donnée à importer', 'warning');
            return;
        }

        const defaultCategory = document.getElementById('defaultCategory').value;
        const defaultUnit = document.getElementById('defaultUnit').value;
        const updateExisting = document.getElementById('updateExisting').checked;

        try {
            let importedCount = 0;
            let updatedCount = 0;
            let skippedCount = 0;

            for (const item of this.importData) {
                // Chercher si l'article existe déjà
                let existingMaterial = this.rawMaterials.find(m =>
                    m.name.toLowerCase() === item.article.toLowerCase()
                );

                let materialId;

                if (existingMaterial) {
                    if (updateExisting) {
                        // Mettre à jour l'article existant
                        materialId = existingMaterial.id;
                        updatedCount++;
                    } else {
                        skippedCount++;
                        continue;
                    }
                } else {
                    // Créer un nouvel article
                    if (!defaultCategory) {
                        Utils.showToast('Veuillez sélectionner une catégorie par défaut pour les nouveaux articles', 'warning');
                        return;
                    }

                    const newMaterial = {
                        name: item.article,
                        categoryId: parseInt(defaultCategory),
                        unit: defaultUnit,
                        description: `Importé le ${new Date().toLocaleDateString()}`,
                        createdAt: new Date().toISOString()
                    };

                    const savedMaterial = storage.add('rawMaterials', newMaterial);
                    materialId = savedMaterial.id;
                    importedCount++;
                }

                // Mettre à jour ou créer l'entrée d'inventaire
                let inventoryItem = this.inventory.find(i => i.materialId === materialId);

                if (inventoryItem) {
                    // Mettre à jour l'inventaire existant
                    storage.update('inventory', inventoryItem.id, {
                        ...inventoryItem,
                        quantity: item.quantite,
                        averagePrice: item.prixHT,
                        totalValue: item.montantHT,
                        lastPurchaseDate: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    });
                } else {
                    // Créer une nouvelle entrée d'inventaire
                    storage.add('inventory', {
                        materialId: materialId,
                        quantity: item.quantite,
                        averagePrice: item.prixHT,
                        totalValue: item.montantHT,
                        minQuantity: 10,
                        lastPurchaseDate: new Date().toISOString(),
                        createdAt: new Date().toISOString()
                    });
                }

                // Enregistrer le mouvement de stock
                storage.add('stockMovements', {
                    materialId: materialId,
                    type: 'in',
                    quantity: item.quantite,
                    stockBefore: inventoryItem ? inventoryItem.quantity : 0,
                    stockAfter: item.quantite,
                    reason: 'import',
                    notes: `Import Excel - Prix HT: ${Utils.formatPrice(item.prixHT)}, TVA: ${item.tva}%`,
                    date: new Date().toISOString()
                });
            }

            // Recharger les données
            this.loadData();
            this.refreshStockList();

            // Afficher le résumé
            let message = `Import terminé avec succès !\n`;
            if (importedCount > 0) message += `• ${importedCount} nouvel(s) article(s) créé(s)\n`;
            if (updatedCount > 0) message += `• ${updatedCount} article(s) mis à jour\n`;
            if (skippedCount > 0) message += `• ${skippedCount} article(s) ignoré(s)`;

            Utils.showToast(message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('importModal')).hide();

        } catch (error) {
            console.error('Erreur lors de l\'import:', error);
            Utils.showToast('Erreur lors de l\'import: ' + error.message, 'error');
        }
    }

    destroy() {
        if (window.inventoryModule === this) {
            delete window.inventoryModule;
        }
    }
}

// Rendre le module disponible globalement
window.InventoryModule = InventoryModule;
