/**
 * RestoManager - Module Dashboard
 * Tableau de bord principal avec statistiques et aperçu
 */

class DashboardModule {
    constructor() {
        this.stats = {};
        this.charts = {};
    }

    /**
     * Initialise le module
     */
    async init() {
        this.loadStats();
    }

    /**
     * Charge les statistiques
     */
    loadStats() {
        const suppliers = storage.get('suppliers') || [];
        const inventory = storage.get('inventory') || [];
        const dishes = storage.get('dishes') || [];
        const staff = storage.get('staff') || [];
        const purchases = storage.get('purchases') || [];
        const dailyOutputs = storage.get('dailyOutputs') || [];

        // Statistiques de base
        this.stats = {
            suppliers: this.getUniqueSuppliers(suppliers).length,
            inventory: inventory.length,
            dishes: dishes.length,
            staff: staff.length,
            totalPurchases: Utils.sumBy(purchases, 'totalAmount'),
            lowStockItems: inventory.filter(item => item.quantity <= (item.minQuantity || 10)).length,
            todayOutputs: dailyOutputs.filter(output => Utils.isToday(output.date)).length,
            activeStaff: staff.filter(member => member.status === 'active').length
        };

        // Statistiques avancées
        this.stats.inventoryValue = Utils.sumBy(inventory, 'totalValue');
        this.stats.averagePurchase = purchases.length > 0 ? this.stats.totalPurchases / purchases.length : 0;
        
        // Tendances (derniers 7 jours)
        const lastWeek = new Date();
        lastWeek.setDate(lastWeek.getDate() - 7);
        
        this.stats.weeklyPurchases = purchases.filter(p => new Date(p.date) >= lastWeek).length;
        this.stats.weeklyOutputs = dailyOutputs.filter(o => new Date(o.date) >= lastWeek).length;
    }

    /**
     * Rend le contenu du module
     */
    async render() {
        return `
            <div class="dashboard-container">
                <!-- En-tête du tableau de bord -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card-neomorphic">
                            <div class="card-body text-center">
                                <h1 class="display-4 text-primary mb-3">
                                    <i class="fas fa-utensils me-3"></i>
                                    RestoManager
                                </h1>
                                <p class="lead text-muted">
                                    Tableau de bord - ${Utils.formatDate(new Date())}
                                </p>
                                <div class="row mt-4">
                                    <div class="col-md-3">
                                        <div class="text-primary">
                                            <i class="fas fa-clock fs-4"></i>
                                            <div class="mt-2">
                                                <span id="currentTime" class="fw-bold">${Utils.formatTime(new Date())}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-success">
                                            <i class="fas fa-chart-line fs-4"></i>
                                            <div class="mt-2">
                                                <span class="fw-bold">Activité</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-warning">
                                            <i class="fas fa-bell fs-4"></i>
                                            <div class="mt-2">
                                                <span class="fw-bold">${this.stats.lowStockItems} Alertes</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-info">
                                            <i class="fas fa-users fs-4"></i>
                                            <div class="mt-2">
                                                <span class="fw-bold">${this.stats.activeStaff} Actifs</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistiques principales -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-truck"></i>
                            </div>
                            <div class="stat-value">${this.stats.suppliers}</div>
                            <div class="stat-label">Fournisseurs</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-warehouse"></i>
                            </div>
                            <div class="stat-value">${this.stats.inventory}</div>
                            <div class="stat-label">Articles en Stock</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-utensils"></i>
                            </div>
                            <div class="stat-value">${this.stats.dishes}</div>
                            <div class="stat-label">Plats au Menu</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-value">${this.stats.staff}</div>
                            <div class="stat-label">Membres du Personnel</div>
                        </div>
                    </div>
                </div>

                <!-- Statistiques financières -->
                <div class="row mb-4">
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-euro-sign"></i>
                            </div>
                            <div class="stat-value">${Utils.formatPrice(this.stats.inventoryValue)}</div>
                            <div class="stat-label">Valeur du Stock</div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="stat-value">${Utils.formatPrice(this.stats.totalPurchases)}</div>
                            <div class="stat-label">Total des Achats</div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div class="stat-value">${Utils.formatPrice(this.stats.averagePurchase)}</div>
                            <div class="stat-label">Achat Moyen</div>
                        </div>
                    </div>
                </div>

                <!-- Alertes et notifications -->
                ${this.renderAlerts()}

                <!-- Activité récente et raccourcis -->
                <div class="row">
                    <div class="col-lg-6 mb-4">
                        <div class="card-neomorphic">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-clock me-2"></i>
                                    Activité Récente
                                </h5>
                            </div>
                            <div class="card-body">
                                ${this.renderRecentActivity()}
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 mb-4">
                        <div class="card-neomorphic">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-rocket me-2"></i>
                                    Raccourcis Rapides
                                </h5>
                            </div>
                            <div class="card-body">
                                ${this.renderQuickActions()}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Graphiques et tendances -->
                <div class="row">
                    <div class="col-12">
                        <div class="card-neomorphic">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-line me-2"></i>
                                    Tendances de la Semaine
                                </h5>
                            </div>
                            <div class="card-body">
                                ${this.renderWeeklyTrends()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Rend les alertes
     */
    renderAlerts() {
        if (this.stats.lowStockItems === 0) {
            return `
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="alert alert-neomorphic alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            Aucune alerte de stock. Tous les articles sont suffisamment approvisionnés.
                        </div>
                    </div>
                </div>
            `;
        }

        return `
            <div class="row mb-4">
                <div class="col-12">
                    <div class="alert alert-neomorphic alert-warning">
                        <h6 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Alertes de Stock
                        </h6>
                        <p class="mb-2">
                            <strong>${this.stats.lowStockItems}</strong> article(s) en rupture ou stock faible.
                        </p>
                        <button class="btn btn-neomorphic btn-warning btn-sm" data-module="inventory">
                            <i class="fas fa-eye me-1"></i>
                            Voir l'inventaire
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Rend l'activité récente
     */
    renderRecentActivity() {
        const activities = [
            { icon: 'fas fa-plus text-success', text: 'Nouvel article ajouté au stock', time: '2h' },
            { icon: 'fas fa-shopping-cart text-primary', text: 'Commande fournisseur validée', time: '4h' },
            { icon: 'fas fa-user text-info', text: 'Planning mis à jour', time: '6h' },
            { icon: 'fas fa-utensils text-warning', text: 'Nouvelle fiche technique créée', time: '1j' }
        ];

        return activities.map(activity => `
            <div class="d-flex align-items-center mb-3">
                <div class="flex-shrink-0">
                    <div class="activity-icon">
                        <i class="${activity.icon}"></i>
                    </div>
                </div>
                <div class="flex-grow-1 ms-3">
                    <div class="activity-text">${activity.text}</div>
                    <small class="text-muted">Il y a ${activity.time}</small>
                </div>
            </div>
        `).join('');
    }

    /**
     * Rend les actions rapides
     */
    renderQuickActions() {
        const actions = [
            { icon: 'fas fa-plus', text: 'Ajouter un achat', module: 'purchases', color: 'primary' },
            { icon: 'fas fa-clipboard-list', text: 'Sortie journalière', module: 'daily-outputs', color: 'success' },
            { icon: 'fas fa-user-plus', text: 'Nouveau personnel', module: 'staff', color: 'info' },
            { icon: 'fas fa-utensils', text: 'Créer un plat', module: 'dishes', color: 'warning' }
        ];

        return `
            <div class="row">
                ${actions.map(action => `
                    <div class="col-6 mb-3">
                        <button class="btn btn-neomorphic btn-${action.color} w-100" data-module="${action.module}">
                            <i class="${action.icon} me-2"></i>
                            ${action.text}
                        </button>
                    </div>
                `).join('')}
            </div>
        `;
    }

    /**
     * Rend les tendances hebdomadaires
     */
    renderWeeklyTrends() {
        return `
            <div class="row text-center">
                <div class="col-md-4">
                    <div class="trend-item">
                        <div class="trend-value text-primary">${this.stats.weeklyPurchases}</div>
                        <div class="trend-label">Achats cette semaine</div>
                        <div class="trend-change text-success">
                            <i class="fas fa-arrow-up"></i> +12%
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="trend-item">
                        <div class="trend-value text-success">${this.stats.weeklyOutputs}</div>
                        <div class="trend-label">Sorties cette semaine</div>
                        <div class="trend-change text-success">
                            <i class="fas fa-arrow-up"></i> +8%
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="trend-item">
                        <div class="trend-value text-warning">${this.stats.todayOutputs}</div>
                        <div class="trend-label">Sorties aujourd'hui</div>
                        <div class="trend-change text-info">
                            <i class="fas fa-minus"></i> Stable
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Actions post-rendu
     */
    postRender() {
        // Mettre à jour l'heure toutes les secondes
        this.updateClock();
        this.clockInterval = setInterval(() => this.updateClock(), 1000);

        // Ajouter les styles CSS spécifiques au dashboard
        this.addDashboardStyles();
    }

    /**
     * Met à jour l'horloge
     */
    updateClock() {
        const timeElement = document.getElementById('currentTime');
        if (timeElement) {
            timeElement.textContent = Utils.formatTime(new Date());
        }
    }

    /**
     * Ajoute les styles CSS spécifiques
     */
    addDashboardStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .activity-icon {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background: var(--bg-color);
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 4px 4px 8px var(--shadow-dark), -4px -4px 8px var(--shadow-light);
            }
            
            .trend-item {
                padding: 1rem;
            }
            
            .trend-value {
                font-size: 2rem;
                font-weight: 700;
                margin-bottom: 0.5rem;
            }
            
            .trend-label {
                color: var(--text-secondary);
                margin-bottom: 0.5rem;
            }
            
            .trend-change {
                font-weight: 600;
                font-size: 0.9rem;
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Nettoie le module
     */
    destroy() {
        if (this.clockInterval) {
            clearInterval(this.clockInterval);
        }
    }
}

// Rendre le module disponible globalement
window.DashboardModule = DashboardModule;
