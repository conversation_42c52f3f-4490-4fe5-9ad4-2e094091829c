/**
 * RestoManager - Système de stockage LocalStorage
 * Gestion centralisée des données avec sauvegarde locale
 */

class StorageManager {
    constructor() {
        this.prefix = 'restomanager_';
        this.version = '1.0.0';
        this.initializeStorage();
    }

    /**
     * Initialise le stockage avec les structures de données par défaut
     */
    initializeStorage() {
        const defaultData = {
            // Configuration
            config: {
                version: this.version,
                initialized: true,
                lastBackup: null,
                currency: 'MAD',
                currencySymbol: 'DH',
                language: 'fr'
            },
            
            // Fournisseurs
            suppliers: [],
            
            // Matières premières et catégories
            rawMaterials: [],
            materialCategories: [
                { id: 1, name: '<PERSON><PERSON>', color: '#e74c3c' },
                { id: 2, name: '<PERSON><PERSON>gum<PERSON>', color: '#27ae60' },
                { id: 3, name: '<PERSON>ail<PERSON>', color: '#fd7e14' },
                { id: 4, name: '<PERSON><PERSON><PERSON>', color: '#17a2b8' },
                { id: 5, name: 'Produits laitiers', color: '#3498db' },
                { id: 6, name: '<PERSON><PERSON><PERSON>', color: '#9b59b6' },
                { id: 7, name: 'Alimentation générale', color: '#6c757d' },
                { id: 8, name: 'Achats divers', color: '#495057' },
                { id: 9, name: 'Produits asiatiques', color: '#e83e8c' },
                { id: 10, name: 'Charcuterie', color: '#dc3545' },
                { id: 11, name: 'Boulangerie', color: '#f4a261' },
                { id: 12, name: 'Pâtisserie', color: '#e76f51' },
                { id: 13, name: 'Emballage', color: '#2a9d8f' },
                { id: 14, name: 'Produits finis', color: '#264653' },
                { id: 15, name: 'Boissons', color: '#1abc9c' }
            ],
            
            // Achats
            purchases: [],
            
            // Stock et inventaire
            inventory: [],
            stockMovements: [],
            
            // Sorties journalières
            dailyOutputs: [],
            
            // Plats et menu
            dishes: [],
            dishCategories: [
                { id: 1, name: 'Entrées', color: '#3498db' },
                { id: 2, name: 'Plats principaux', color: '#e74c3c' },
                { id: 3, name: 'Desserts', color: '#f39c12' },
                { id: 4, name: 'Boissons', color: '#1abc9c' },
                { id: 5, name: 'Accompagnements', color: '#9b59b6' }
            ],
            
            // Fiches techniques
            recipes: [],
            
            // Personnel
            staff: [],
            
            // Planning
            schedules: [],
            shifts: [
                { id: 1, name: 'Matin', startTime: '06:00', endTime: '14:00', color: '#f39c12' },
                { id: 2, name: 'Après-midi', startTime: '14:00', endTime: '22:00', color: '#3498db' },
                { id: 3, name: 'Nuit', startTime: '22:00', endTime: '06:00', color: '#9b59b6' },
                { id: 4, name: 'Jour complet', startTime: '08:00', endTime: '20:00', color: '#27ae60' }
            ],
            
            // Pointage
            timeTracking: [],
            
            // Consolidation
            consolidations: []
        };

        // Initialiser les données si elles n'existent pas
        Object.keys(defaultData).forEach(key => {
            if (!this.get(key)) {
                this.set(key, defaultData[key]);
            }
        });

        // Forcer la mise à jour des catégories si elles sont obsolètes
        this.updateCategoriesIfNeeded(defaultData.materialCategories);
    }

    /**
     * Met à jour les catégories si nécessaire
     */
    updateCategoriesIfNeeded(newCategories) {
        const existingCategories = this.get('materialCategories') || [];

        // Vérifier si on a toutes les nouvelles catégories
        const existingNames = existingCategories.map(c => c.name);
        const newNames = newCategories.map(c => c.name);
        const missingCategories = newNames.filter(name => !existingNames.includes(name));

        // Forcer la mise à jour si on a moins de 15 catégories ou des catégories manquantes
        if (existingCategories.length < 15 || missingCategories.length > 0) {
            console.log('Mise à jour des catégories détectée. Catégories manquantes:', missingCategories);
            console.log('Remplacement de', existingCategories.length, 'catégories par', newCategories.length, 'nouvelles catégories');
            this.set('materialCategories', newCategories);
            return true;
        }
        return false;
    }

    /**
     * Génère une clé avec le préfixe
     */
    getKey(key) {
        return this.prefix + key;
    }

    /**
     * Sauvegarde une donnée dans le localStorage
     */
    set(key, value) {
        try {
            const data = {
                value: value,
                timestamp: new Date().toISOString(),
                version: this.version
            };
            localStorage.setItem(this.getKey(key), JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('Erreur lors de la sauvegarde:', error);
            return false;
        }
    }

    /**
     * Récupère une donnée du localStorage
     */
    get(key) {
        try {
            const item = localStorage.getItem(this.getKey(key));
            if (!item) return null;
            
            const data = JSON.parse(item);
            return data.value;
        } catch (error) {
            console.error('Erreur lors de la récupération:', error);
            return null;
        }
    }

    /**
     * Supprime une donnée du localStorage
     */
    remove(key) {
        try {
            localStorage.removeItem(this.getKey(key));
            return true;
        } catch (error) {
            console.error('Erreur lors de la suppression:', error);
            return false;
        }
    }

    /**
     * Vide tout le stockage de l'application
     */
    clear() {
        try {
            Object.keys(localStorage).forEach(key => {
                if (key.startsWith(this.prefix)) {
                    localStorage.removeItem(key);
                }
            });
            this.initializeStorage();
            return true;
        } catch (error) {
            console.error('Erreur lors du nettoyage:', error);
            return false;
        }
    }

    /**
     * Génère un ID unique
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * Ajoute un élément à une collection
     */
    add(collection, item) {
        const items = this.get(collection) || [];
        item.id = item.id || this.generateId();
        item.createdAt = new Date().toISOString();
        item.updatedAt = new Date().toISOString();
        
        items.push(item);
        return this.set(collection, items) ? item : null;
    }

    /**
     * Met à jour un élément dans une collection
     */
    update(collection, id, updates) {
        const items = this.get(collection) || [];
        const index = items.findIndex(item => item.id === id);
        
        if (index === -1) return null;
        
        items[index] = {
            ...items[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        
        return this.set(collection, items) ? items[index] : null;
    }

    /**
     * Supprime un élément d'une collection
     */
    delete(collection, id) {
        const items = this.get(collection) || [];
        const filteredItems = items.filter(item => item.id !== id);
        
        return this.set(collection, filteredItems);
    }

    /**
     * Trouve un élément par ID
     */
    findById(collection, id) {
        const items = this.get(collection) || [];
        return items.find(item => item.id === id) || null;
    }

    /**
     * Filtre les éléments d'une collection
     */
    filter(collection, predicate) {
        const items = this.get(collection) || [];
        return items.filter(predicate);
    }

    /**
     * Exporte toutes les données en JSON
     */
    exportData() {
        const data = {};
        Object.keys(localStorage).forEach(key => {
            if (key.startsWith(this.prefix)) {
                const cleanKey = key.replace(this.prefix, '');
                data[cleanKey] = this.get(cleanKey);
            }
        });
        
        return {
            version: this.version,
            exportDate: new Date().toISOString(),
            data: data
        };
    }

    /**
     * Importe des données depuis un objet JSON
     */
    importData(importData) {
        try {
            if (!importData.data) {
                throw new Error('Format de données invalide');
            }
            
            // Sauvegarde actuelle avant import
            const backup = this.exportData();
            
            // Import des nouvelles données
            Object.keys(importData.data).forEach(key => {
                this.set(key, importData.data[key]);
            });
            
            // Mise à jour de la configuration
            this.set('config', {
                ...this.get('config'),
                lastImport: new Date().toISOString(),
                importVersion: importData.version
            });
            
            return { success: true, backup: backup };
        } catch (error) {
            console.error('Erreur lors de l\'import:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Crée une sauvegarde automatique
     */
    createBackup() {
        const backup = this.exportData();
        const backupKey = 'backup_' + new Date().toISOString().split('T')[0];
        
        this.set(backupKey, backup);
        this.set('config', {
            ...this.get('config'),
            lastBackup: new Date().toISOString()
        });
        
        return backup;
    }

    /**
     * Récupère la taille du stockage utilisé
     */
    getStorageSize() {
        let total = 0;
        Object.keys(localStorage).forEach(key => {
            if (key.startsWith(this.prefix)) {
                total += localStorage.getItem(key).length;
            }
        });
        
        return {
            bytes: total,
            kb: Math.round(total / 1024 * 100) / 100,
            mb: Math.round(total / (1024 * 1024) * 100) / 100
        };
    }

    /**
     * Vérifie la santé du stockage
     */
    checkStorageHealth() {
        const size = this.getStorageSize();
        const maxSize = 5 * 1024 * 1024; // 5MB limite approximative
        
        return {
            healthy: size.bytes < maxSize * 0.8,
            usage: Math.round((size.bytes / maxSize) * 100),
            size: size,
            recommendations: size.bytes > maxSize * 0.8 ? 
                ['Considérez exporter et nettoyer les anciennes données'] : []
        };
    }
}

// Instance globale du gestionnaire de stockage
window.storage = new StorageManager();
