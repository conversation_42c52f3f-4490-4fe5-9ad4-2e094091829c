<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Filtres Catégories - RestoManager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Test des Filtres de Catégories</h1>
        
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle me-2"></i>Test des Filtres</h6>
            <p>Cette page teste spécifiquement les filtres de catégories dans les modules Achats et Inventaire.</p>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Module Achats - Filtre Catégories</h5>
                    </div>
                    <div class="card-body">
                        <div id="purchasesFilter">
                            <p class="text-muted">Chargement...</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Module Inventaire - Filtre Catégories</h5>
                    </div>
                    <div class="card-body">
                        <div id="inventoryFilter">
                            <p class="text-muted">Chargement...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Catégories dans le Storage</h5>
                    </div>
                    <div class="card-body">
                        <div id="storageCategories">
                            <p class="text-muted">Chargement...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Actions</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary me-2" onclick="refreshTest()">
                            <i class="fas fa-sync me-2"></i>Actualiser Test
                        </button>
                        
                        <button class="btn btn-warning me-2" onclick="forceUpdate()">
                            <i class="fas fa-tools me-2"></i>Forcer Mise à Jour
                        </button>
                        
                        <button class="btn btn-success" onclick="openMainApp()">
                            <i class="fas fa-external-link-alt me-2"></i>Ouvrir App Principale
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/core/storage.js"></script>
    <script src="assets/js/core/utils.js"></script>
    <script src="assets/js/modules/purchases.js"></script>
    <script src="assets/js/modules/inventory.js"></script>

    <script>
        let purchasesModule;
        let inventoryModule;

        // Initialiser au chargement
        window.addEventListener('load', async () => {
            await initializeModules();
            displayStorageCategories();
            testFilters();
        });

        async function initializeModules() {
            try {
                purchasesModule = new PurchasesModule();
                await purchasesModule.init();
                
                inventoryModule = new InventoryModule();
                await inventoryModule.init();
                
                console.log('Modules initialisés');
                console.log('Catégories Achats:', purchasesModule.categories?.length || 0);
                console.log('Catégories Inventaire:', inventoryModule.categories?.length || 0);
                
            } catch (error) {
                console.error('Erreur lors de l\'initialisation:', error);
            }
        }

        function displayStorageCategories() {
            const categories = storage.get('materialCategories') || [];
            
            let html = `
                <div class="alert alert-${categories.length >= 15 ? 'success' : 'warning'}">
                    <strong>Total:</strong> ${categories.length} catégories dans le storage
                </div>
                <div class="row">
            `;
            
            categories.forEach(category => {
                html += `
                    <div class="col-md-3 mb-2">
                        <div class="d-flex align-items-center">
                            <span class="badge me-2" style="background-color: ${category.color};">
                                ${category.id}
                            </span>
                            <small>${category.name}</small>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            document.getElementById('storageCategories').innerHTML = html;
        }

        function testFilters() {
            // Test filtre Achats
            if (purchasesModule) {
                const purchasesHtml = purchasesModule.render();
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = purchasesHtml;
                
                const filterSelect = tempDiv.querySelector('#filterCategory');
                if (filterSelect) {
                    const options = Array.from(filterSelect.options);
                    const categoryOptions = options.filter(opt => opt.value && opt.value !== '');
                    
                    let html = `
                        <div class="alert alert-${categoryOptions.length >= 15 ? 'success' : 'warning'}">
                            <strong>Options trouvées:</strong> ${categoryOptions.length}
                        </div>
                        <select class="form-select" disabled>
                    `;
                    
                    options.forEach(option => {
                        html += `<option value="${option.value}">${option.textContent}</option>`;
                    });
                    
                    html += '</select>';
                    document.getElementById('purchasesFilter').innerHTML = html;
                } else {
                    document.getElementById('purchasesFilter').innerHTML = 
                        '<div class="alert alert-danger">❌ Filtre non trouvé</div>';
                }
            }

            // Test filtre Inventaire
            if (inventoryModule) {
                const inventoryHtml = inventoryModule.render();
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = inventoryHtml;
                
                const filterSelect = tempDiv.querySelector('#filterCategory');
                if (filterSelect) {
                    const options = Array.from(filterSelect.options);
                    const categoryOptions = options.filter(opt => opt.value && opt.value !== '');
                    
                    let html = `
                        <div class="alert alert-${categoryOptions.length >= 15 ? 'success' : 'warning'}">
                            <strong>Options trouvées:</strong> ${categoryOptions.length}
                        </div>
                        <select class="form-select" disabled>
                    `;
                    
                    options.forEach(option => {
                        html += `<option value="${option.value}">${option.textContent}</option>`;
                    });
                    
                    html += '</select>';
                    document.getElementById('inventoryFilter').innerHTML = html;
                } else {
                    document.getElementById('inventoryFilter').innerHTML = 
                        '<div class="alert alert-danger">❌ Filtre non trouvé</div>';
                }
            }
        }

        async function refreshTest() {
            document.getElementById('purchasesFilter').innerHTML = '<p class="text-muted">Rechargement...</p>';
            document.getElementById('inventoryFilter').innerHTML = '<p class="text-muted">Rechargement...</p>';
            
            await initializeModules();
            displayStorageCategories();
            testFilters();
        }

        function forceUpdate() {
            const newCategories = [
                { id: 1, name: 'Viandes', color: '#e74c3c' },
                { id: 2, name: 'Légumes', color: '#27ae60' },
                { id: 3, name: 'Volailles', color: '#fd7e14' },
                { id: 4, name: 'Poissons', color: '#17a2b8' },
                { id: 5, name: 'Produits laitiers', color: '#3498db' },
                { id: 6, name: 'Épicerie', color: '#9b59b6' },
                { id: 7, name: 'Alimentation générale', color: '#6c757d' },
                { id: 8, name: 'Achats divers', color: '#495057' },
                { id: 9, name: 'Produits asiatiques', color: '#e83e8c' },
                { id: 10, name: 'Charcuterie', color: '#dc3545' },
                { id: 11, name: 'Boulangerie', color: '#f4a261' },
                { id: 12, name: 'Pâtisserie', color: '#e76f51' },
                { id: 13, name: 'Emballage', color: '#2a9d8f' },
                { id: 14, name: 'Produits finis', color: '#264653' },
                { id: 15, name: 'Boissons', color: '#1abc9c' }
            ];
            
            storage.set('materialCategories', newCategories);
            alert('✅ Catégories mises à jour! Actualisation du test...');
            refreshTest();
        }

        function openMainApp() {
            window.open('/', '_blank');
        }
    </script>
</body>
</html>
