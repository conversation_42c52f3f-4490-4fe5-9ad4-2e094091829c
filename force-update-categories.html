<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mise à Jour Catégories - RestoManager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Mise à Jour des Catégories</h1>
        
        <div class="alert alert-warning">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>Attention</h6>
            <p>Cette page force la mise à jour des catégories dans le localStorage. Utilisez-la si les nouvelles catégories n'apparaissent pas dans les modules.</p>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <button class="btn btn-warning" onclick="forceUpdateCategories()">
                                <i class="fas fa-sync me-2"></i>Forcer Mise à Jour
                            </button>
                            
                            <button class="btn btn-danger ms-2" onclick="resetAllData()">
                                <i class="fas fa-trash me-2"></i>Reset Complet
                            </button>
                        </div>
                        
                        <div class="mb-3">
                            <button class="btn btn-info" onclick="checkCategories()">
                                <i class="fas fa-eye me-2"></i>Vérifier Catégories
                            </button>
                            
                            <button class="btn btn-success ms-2" onclick="testModules()">
                                <i class="fas fa-play me-2"></i>Tester Modules
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Statut</h5>
                    </div>
                    <div class="card-body">
                        <div id="status">
                            <p class="text-muted">Aucune action effectuée</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Catégories Actuelles</h5>
                    </div>
                    <div class="card-body">
                        <div id="categoriesList">
                            <p class="text-muted">Chargement...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Test des Filtres</h5>
                    </div>
                    <div class="card-body">
                        <div id="filtersTest">
                            <p class="text-muted">Aucun test effectué</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/core/storage.js"></script>
    <script src="assets/js/core/utils.js"></script>
    <script src="assets/js/modules/purchases.js"></script>
    <script src="assets/js/modules/inventory.js"></script>

    <script>
        const newCategories = [
            { id: 1, name: 'Viandes', color: '#e74c3c' },
            { id: 2, name: 'Légumes', color: '#27ae60' },
            { id: 3, name: 'Volailles', color: '#fd7e14' },
            { id: 4, name: 'Poissons', color: '#17a2b8' },
            { id: 5, name: 'Produits laitiers', color: '#3498db' },
            { id: 6, name: 'Épicerie', color: '#9b59b6' },
            { id: 7, name: 'Alimentation générale', color: '#6c757d' },
            { id: 8, name: 'Achats divers', color: '#495057' },
            { id: 9, name: 'Produits asiatiques', color: '#e83e8c' },
            { id: 10, name: 'Charcuterie', color: '#dc3545' },
            { id: 11, name: 'Boulangerie', color: '#f4a261' },
            { id: 12, name: 'Pâtisserie', color: '#e76f51' },
            { id: 13, name: 'Emballage', color: '#2a9d8f' },
            { id: 14, name: 'Produits finis', color: '#264653' },
            { id: 15, name: 'Boissons', color: '#1abc9c' }
        ];

        // Initialiser au chargement
        window.addEventListener('load', () => {
            checkCategories();
        });

        function forceUpdateCategories() {
            try {
                // Forcer la mise à jour des catégories
                storage.set('materialCategories', newCategories);
                
                updateStatus('success', `✅ Catégories mises à jour avec succès! ${newCategories.length} catégories disponibles.`);
                checkCategories();
                
            } catch (error) {
                updateStatus('error', `❌ Erreur lors de la mise à jour: ${error.message}`);
            }
        }

        function resetAllData() {
            if (confirm('⚠️ Êtes-vous sûr de vouloir supprimer TOUTES les données? Cette action est irréversible!')) {
                try {
                    storage.clear();
                    updateStatus('warning', '🔄 Toutes les données ont été supprimées. Rechargement de la page...');
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } catch (error) {
                    updateStatus('error', `❌ Erreur lors du reset: ${error.message}`);
                }
            }
        }

        function checkCategories() {
            const categories = storage.get('materialCategories') || [];
            
            let html = '<div class="row">';
            categories.forEach(category => {
                html += `
                    <div class="col-md-4 mb-2">
                        <div class="d-flex align-items-center">
                            <span class="badge me-2" style="background-color: ${category.color};">
                                ${category.id}
                            </span>
                            <span style="color: ${category.color}; font-weight: bold;">
                                ${category.name}
                            </span>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            
            html += `
                <div class="mt-3 alert alert-info">
                    <strong>Total:</strong> ${categories.length} catégories trouvées<br>
                    <strong>Attendu:</strong> ${newCategories.length} catégories<br>
                    <strong>Statut:</strong> ${categories.length === newCategories.length ? '✅ Complet' : '❌ Incomplet'}
                </div>
            `;
            
            document.getElementById('categoriesList').innerHTML = html;
            
            // Vérifier si toutes les catégories sont présentes
            const existingNames = categories.map(c => c.name);
            const expectedNames = newCategories.map(c => c.name);
            const missing = expectedNames.filter(name => !existingNames.includes(name));
            
            if (missing.length > 0) {
                updateStatus('warning', `⚠️ ${missing.length} catégorie(s) manquante(s): ${missing.join(', ')}`);
            } else {
                updateStatus('success', '✅ Toutes les catégories sont présentes!');
            }
        }

        async function testModules() {
            let html = '<h6>Test des Modules:</h6>';
            
            try {
                // Test module Achats
                html += '<div class="mb-3">';
                html += '<h6>Module Achats:</h6>';
                
                const purchasesModule = new PurchasesModule();
                await purchasesModule.init();
                
                const purchasesCategories = purchasesModule.categories || [];
                html += `<p>Catégories chargées: ${purchasesCategories.length}</p>`;
                
                // Générer le HTML du module pour tester les filtres
                const purchasesHtml = purchasesModule.render();
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = purchasesHtml;
                
                const filterSelect = tempDiv.querySelector('#filterCategory');
                if (filterSelect) {
                    const options = Array.from(filterSelect.options).filter(opt => opt.value);
                    html += `<p>Options dans le filtre: ${options.length}</p>`;
                    html += `<small>Options: ${options.map(opt => opt.textContent).join(', ')}</small>`;
                } else {
                    html += '<p class="text-danger">❌ Filtre de catégorie non trouvé</p>';
                }
                html += '</div>';
                
                // Test module Inventaire
                html += '<div class="mb-3">';
                html += '<h6>Module Inventaire:</h6>';
                
                const inventoryModule = new InventoryModule();
                await inventoryModule.init();
                
                const inventoryCategories = inventoryModule.categories || [];
                html += `<p>Catégories chargées: ${inventoryCategories.length}</p>`;
                
                // Générer le HTML du module pour tester les filtres
                const inventoryHtml = inventoryModule.render();
                const tempDiv2 = document.createElement('div');
                tempDiv2.innerHTML = inventoryHtml;
                
                const filterSelect2 = tempDiv2.querySelector('#filterCategory');
                if (filterSelect2) {
                    const options2 = Array.from(filterSelect2.options).filter(opt => opt.value);
                    html += `<p>Options dans le filtre: ${options2.length}</p>`;
                    html += `<small>Options: ${options2.map(opt => opt.textContent).join(', ')}</small>`;
                } else {
                    html += '<p class="text-danger">❌ Filtre de catégorie non trouvé</p>';
                }
                html += '</div>';
                
            } catch (error) {
                html += `<div class="alert alert-danger">❌ Erreur lors du test: ${error.message}</div>`;
            }
            
            document.getElementById('filtersTest').innerHTML = html;
        }

        function updateStatus(type, message) {
            const alertClass = {
                'success': 'alert-success',
                'error': 'alert-danger',
                'warning': 'alert-warning',
                'info': 'alert-info'
            }[type] || 'alert-info';
            
            document.getElementById('status').innerHTML = `
                <div class="alert ${alertClass}">
                    ${message}
                </div>
            `;
        }
    </script>
</body>
</html>
