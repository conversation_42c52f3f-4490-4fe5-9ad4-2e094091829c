/**
 * RestoManager - Application principale
 * Gestion de la navigation et initialisation des modules
 */

class RestoManagerApp {
    constructor() {
        this.currentModule = null;
        this.modules = new Map();
        this.isInitialized = false;
        
        this.init();
    }

    /**
     * Initialise l'application
     */
    async init() {
        try {
            // Attendre que le DOM soit chargé
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.initApp());
            } else {
                this.initApp();
            }
        } catch (error) {
            console.error('Erreur lors de l\'initialisation:', error);
            Utils.showToast('Erreur lors du chargement de l\'application', 'error');
        }
    }

    /**
     * Initialise l'application après chargement du DOM
     */
    async initApp() {
        try {
            // Masquer l'écran de chargement après un délai
            setTimeout(() => {
                const loadingScreen = document.getElementById('loadingScreen');
                if (loadingScreen) {
                    loadingScreen.classList.add('hidden');
                }
            }, 1500);

            // Initialiser les gestionnaires d'événements
            this.initEventListeners();
            
            // Charger le module par défaut (dashboard)
            await this.loadModule('dashboard');
            
            // Marquer comme initialisé
            this.isInitialized = true;
            
            // Vérifier la santé du stockage
            this.checkStorageHealth();
            
            console.log('RestoManager initialisé avec succès');
            
        } catch (error) {
            console.error('Erreur lors de l\'initialisation de l\'app:', error);
            Utils.showToast('Erreur lors de l\'initialisation', 'error');
        }
    }

    /**
     * Initialise les gestionnaires d'événements
     */
    initEventListeners() {
        // Navigation des modules
        document.addEventListener('click', (e) => {
            const moduleLink = e.target.closest('[data-module]');
            if (moduleLink) {
                e.preventDefault();
                const moduleName = moduleLink.dataset.module;
                this.loadModule(moduleName);
                this.updateActiveNavigation(moduleLink);
            }

            // Actions globales
            const actionLink = e.target.closest('[data-action]');
            if (actionLink) {
                e.preventDefault();
                const action = actionLink.dataset.action;
                this.handleGlobalAction(action);
            }
        });

        // Gestion des raccourcis clavier
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 's':
                        e.preventDefault();
                        this.handleGlobalAction('backup');
                        break;
                    case 'e':
                        e.preventDefault();
                        this.handleGlobalAction('export-data');
                        break;
                }
            }
        });

        // Gestion de la fermeture/actualisation
        window.addEventListener('beforeunload', (e) => {
            // Créer une sauvegarde automatique
            storage.createBackup();
        });

        // Gestion du redimensionnement
        window.addEventListener('resize', Utils.debounce(() => {
            this.handleResize();
        }, 250));
    }

    /**
     * Charge un module spécifique
     */
    async loadModule(moduleName) {
        try {
            const moduleContent = document.getElementById('moduleContent');
            if (!moduleContent) {
                throw new Error('Conteneur de module non trouvé');
            }

            // Afficher un indicateur de chargement
            moduleContent.innerHTML = `
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <p class="mt-3">Chargement du module ${moduleName}...</p>
                </div>
            `;

            // Vérifier si le module existe dans window
            const moduleClass = window[this.getModuleClassName(moduleName)];
            if (!moduleClass) {
                throw new Error(`Module ${moduleName} non trouvé`);
            }

            // Détruire le module précédent s'il existe
            if (this.currentModule && typeof this.currentModule.destroy === 'function') {
                this.currentModule.destroy();
            }

            // Créer et initialiser le nouveau module
            this.currentModule = new moduleClass();
            this.modules.set(moduleName, this.currentModule);

            // Initialiser le module
            if (typeof this.currentModule.init === 'function') {
                await this.currentModule.init();
            }

            // Rendre le module
            if (typeof this.currentModule.render === 'function') {
                const content = await this.currentModule.render();
                moduleContent.innerHTML = content;
                
                // Animer l'apparition
                Utils.animateIn(moduleContent);
            }

            // Appeler la méthode postRender si elle existe
            if (typeof this.currentModule.postRender === 'function') {
                this.currentModule.postRender();
            }

            console.log(`Module ${moduleName} chargé avec succès`);

        } catch (error) {
            console.error(`Erreur lors du chargement du module ${moduleName}:`, error);
            this.showModuleError(moduleName, error.message);
        }
    }

    /**
     * Génère le nom de classe du module
     */
    getModuleClassName(moduleName) {
        return moduleName.split('-')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join('') + 'Module';
    }

    /**
     * Met à jour la navigation active
     */
    updateActiveNavigation(activeLink) {
        // Retirer la classe active de tous les liens
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });

        // Ajouter la classe active au lien cliqué
        activeLink.classList.add('active');
    }

    /**
     * Affiche une erreur de module
     */
    showModuleError(moduleName, errorMessage) {
        const moduleContent = document.getElementById('moduleContent');
        moduleContent.innerHTML = `
            <div class="alert alert-danger" role="alert">
                <h4 class="alert-heading">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Erreur de chargement
                </h4>
                <p>Impossible de charger le module <strong>${moduleName}</strong>.</p>
                <hr>
                <p class="mb-0">
                    <small>Détails: ${errorMessage}</small>
                </p>
                <div class="mt-3">
                    <button class="btn btn-outline-danger" onclick="location.reload()">
                        <i class="fas fa-redo me-1"></i>
                        Recharger la page
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Gère les actions globales
     */
    async handleGlobalAction(action) {
        switch (action) {
            case 'export-data':
                await this.exportData();
                break;
            case 'import-data':
                await this.importData();
                break;
            case 'backup':
                await this.createBackup();
                break;
            default:
                console.warn(`Action globale inconnue: ${action}`);
        }
    }

    /**
     * Exporte toutes les données
     */
    async exportData() {
        try {
            const data = storage.exportData();
            const filename = `restomanager_export_${new Date().toISOString().split('T')[0]}.json`;
            Utils.exportToJSON(data, filename);
            Utils.showToast('Données exportées avec succès', 'success');
        } catch (error) {
            console.error('Erreur lors de l\'export:', error);
            Utils.showToast('Erreur lors de l\'export des données', 'error');
        }
    }

    /**
     * Importe des données
     */
    async importData() {
        try {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            
            input.onchange = async (e) => {
                const file = e.target.files[0];
                if (!file) return;

                try {
                    const content = await Utils.readFile(file);
                    const data = JSON.parse(content);
                    
                    const confirmed = await Utils.confirm(
                        'Cette action remplacera toutes les données actuelles. Voulez-vous continuer ?',
                        'Confirmer l\'import'
                    );
                    
                    if (confirmed) {
                        const result = storage.importData(data);
                        if (result.success) {
                            Utils.showToast('Données importées avec succès', 'success');
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            throw new Error(result.error);
                        }
                    }
                } catch (error) {
                    console.error('Erreur lors de l\'import:', error);
                    Utils.showToast('Erreur lors de l\'import: fichier invalide', 'error');
                }
            };
            
            input.click();
        } catch (error) {
            console.error('Erreur lors de l\'import:', error);
            Utils.showToast('Erreur lors de l\'import des données', 'error');
        }
    }

    /**
     * Crée une sauvegarde
     */
    async createBackup() {
        try {
            const backup = storage.createBackup();
            const filename = `restomanager_backup_${new Date().toISOString().split('T')[0]}.json`;
            Utils.exportToJSON(backup, filename);
            Utils.showToast('Sauvegarde créée avec succès', 'success');
        } catch (error) {
            console.error('Erreur lors de la sauvegarde:', error);
            Utils.showToast('Erreur lors de la création de la sauvegarde', 'error');
        }
    }

    /**
     * Vérifie la santé du stockage
     */
    checkStorageHealth() {
        const health = storage.checkStorageHealth();
        
        if (!health.healthy) {
            Utils.showToast(
                `Attention: Stockage à ${health.usage}% de capacité. ${health.recommendations.join(', ')}`,
                'warning',
                5000
            );
        }
    }

    /**
     * Gère le redimensionnement de la fenêtre
     */
    handleResize() {
        // Réajuster les éléments si nécessaire
        if (this.currentModule && typeof this.currentModule.onResize === 'function') {
            this.currentModule.onResize();
        }
    }

    /**
     * Obtient des statistiques globales
     */
    getGlobalStats() {
        return {
            suppliers: storage.get('suppliers')?.length || 0,
            inventory: storage.get('inventory')?.length || 0,
            dishes: storage.get('dishes')?.length || 0,
            staff: storage.get('staff')?.length || 0,
            storageHealth: storage.checkStorageHealth()
        };
    }
}

// Initialiser l'application quand le script est chargé
window.app = new RestoManagerApp();
