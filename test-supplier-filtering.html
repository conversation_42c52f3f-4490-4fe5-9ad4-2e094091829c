<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Filtrage par Fournisseur</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Test Filtrage par Type de Fournisseur</h1>
        
        <div class="alert alert-info">
            <p>Cette page teste le nouveau système de filtrage des matières premières par type de fournisseur.</p>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Actions</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary mb-2" onclick="initPurchaseModule()">1. Initialiser Module</button>
                        <button class="btn btn-success mb-2" onclick="testFiltering()">2. Tester Filtrage</button>
                        <button class="btn btn-info mb-2" onclick="openPurchaseModal()">3. Ouvrir Modal</button>
                        <button class="btn btn-warning mb-2" onclick="showMappingInfo()">4. Voir Mapping</button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Résultats</h5>
                    </div>
                    <div class="card-body">
                        <div id="results">
                            <p class="text-muted">Aucun test effectué</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test de filtrage direct -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Filtrage Direct</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <label class="form-label">Fournisseur</label>
                                <select class="form-select" id="testSupplier" onchange="testSupplierChange()">
                                    <option value="">Sélectionner un fournisseur</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Type</label>
                                <input type="text" class="form-control" id="testSupplierType" readonly>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Matières filtrées</label>
                                <input type="text" class="form-control" id="testFilteredCount" readonly>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">Matières premières disponibles</label>
                                <select class="form-select" id="testMaterials" size="8">
                                    <option value="">Sélectionner un fournisseur d'abord</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Catégories correspondantes</label>
                                <div id="testCategories" class="border rounded p-3" style="height: 200px; overflow-y: auto;">
                                    <p class="text-muted">Sélectionner un fournisseur</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal d'achat pour test -->
        <div class="modal fade" id="testPurchaseModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Test Modal Achat avec Filtrage</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-4">
                                <label class="form-label">Date</label>
                                <input type="date" class="form-control" id="modalDate">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Fournisseur *</label>
                                <select class="form-select" id="modalSupplier" onchange="modalSupplierChange()">
                                    <option value="">Sélectionner un fournisseur</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Type</label>
                                <input type="text" class="form-control" id="modalSupplierType" readonly>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6>Articles achetés</h6>
                                <div id="modalPurchaseItems">
                                    <!-- Items will be added here -->
                                </div>
                                <button type="button" class="btn btn-success btn-sm" onclick="addModalItem()">
                                    <i class="fas fa-plus me-1"></i>
                                    Ajouter un article
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/core/storage.js"></script>
    <script src="assets/js/core/utils.js"></script>
    <script src="assets/js/modules/purchases.js"></script>

    <script>
        let purchaseModule = null;

        window.addEventListener('load', () => {
            storage.init();
            updateResults('✅ Storage initialisé');
        });

        async function initPurchaseModule() {
            try {
                purchaseModule = new PurchasesModule();
                await purchaseModule.init();
                
                // Rendre disponible globalement
                window.purchasesModule = purchaseModule;
                
                updateResults(`✅ Module initialisé avec ${purchaseModule.rawMaterials.length} matières et ${purchaseModule.suppliers.length} fournisseurs`);
                
                // Remplir les selects de test
                populateTestSelects();
                
            } catch (error) {
                updateResults(`❌ Erreur: ${error.message}`);
                console.error(error);
            }
        }

        function populateTestSelects() {
            const testSupplier = document.getElementById('testSupplier');
            const modalSupplier = document.getElementById('modalSupplier');
            
            // Utiliser la méthode getUniqueSuppliers du module
            const uniqueSuppliers = purchaseModule.getUniqueSuppliers();
            
            [testSupplier, modalSupplier].forEach(select => {
                select.innerHTML = '<option value="">Sélectionner un fournisseur</option>';
                uniqueSuppliers.forEach(supplier => {
                    const option = document.createElement('option');
                    option.value = supplier.id;
                    option.dataset.type = supplier.type || '';
                    option.textContent = `${supplier.name} (${supplier.type || 'Sans type'})`;
                    select.appendChild(option);
                });
            });
            
            updateResults(`✅ ${uniqueSuppliers.length} fournisseurs uniques chargés`);
        }

        function testSupplierChange() {
            const select = document.getElementById('testSupplier');
            const typeInput = document.getElementById('testSupplierType');
            const countInput = document.getElementById('testFilteredCount');
            const materialsSelect = document.getElementById('testMaterials');
            const categoriesDiv = document.getElementById('testCategories');
            
            const selectedOption = select.selectedOptions[0];
            
            if (!selectedOption || !selectedOption.value) {
                typeInput.value = '';
                countInput.value = '';
                materialsSelect.innerHTML = '<option value="">Sélectionner un fournisseur d\'abord</option>';
                categoriesDiv.innerHTML = '<p class="text-muted">Sélectionner un fournisseur</p>';
                return;
            }
            
            const supplierType = selectedOption.dataset.type;
            typeInput.value = supplierType;
            
            // Utiliser la méthode de filtrage du module
            testFilteringLogic(supplierType, materialsSelect, countInput, categoriesDiv);
        }

        function testFilteringLogic(supplierType, materialsSelect, countInput, categoriesDiv) {
            // Mapping des types vers les catégories (même que dans le module)
            const typeMapping = {
                'Alimentation générale': ['Épicerie', 'Condiments'],
                'Légumes et fruits': ['Légumes', 'Fruits'],
                'Viandes': ['Viandes'],
                'Poissons': ['Poissons'],
                'Produits laitiers': ['Produits laitiers'],
                'Boulangerie': ['Boulangerie'],
                'Boissons': ['Boissons'],
                'Surgelés': ['Surgelés']
            };
            
            const allowedCategories = typeMapping[supplierType] || [];
            
            // Afficher les catégories correspondantes
            if (allowedCategories.length > 0) {
                categoriesDiv.innerHTML = `
                    <h6>Catégories autorisées :</h6>
                    <ul>
                        ${allowedCategories.map(cat => `<li><span class="badge bg-primary">${cat}</span></li>`).join('')}
                    </ul>
                `;
            } else {
                categoriesDiv.innerHTML = '<p class="text-warning">Aucune catégorie définie pour ce type</p>';
            }
            
            // Filtrer les matières premières
            const allowedCategoryIds = purchaseModule.categories
                .filter(cat => allowedCategories.includes(cat.name))
                .map(cat => cat.id);
            
            const filteredMaterials = purchaseModule.rawMaterials.filter(material => 
                allowedCategoryIds.includes(material.categoryId)
            );
            
            countInput.value = `${filteredMaterials.length} / ${purchaseModule.rawMaterials.length}`;
            
            // Remplir le select
            materialsSelect.innerHTML = '';
            if (filteredMaterials.length === 0) {
                materialsSelect.innerHTML = '<option value="">Aucune matière correspondante</option>';
            } else {
                filteredMaterials.forEach(material => {
                    const category = purchaseModule.categories.find(c => c.id === material.categoryId);
                    const inventory = storage.get('inventory') || [];
                    const invItem = inventory.find(i => i.materialId === material.id);
                    const price = invItem ? (invItem.unitPrice || invItem.averagePrice || 0) : 0;
                    const priceInfo = price > 0 ? ` - ${Utils.formatPrice(price)}` : '';
                    
                    const option = document.createElement('option');
                    option.value = material.id;
                    option.textContent = `${material.name} (${category?.name || 'N/A'})${priceInfo}`;
                    materialsSelect.appendChild(option);
                });
            }
        }

        function modalSupplierChange() {
            const select = document.getElementById('modalSupplier');
            const typeInput = document.getElementById('modalSupplierType');
            const selectedOption = select.selectedOptions[0];
            
            if (selectedOption && selectedOption.value) {
                const supplierType = selectedOption.dataset.type || '';
                typeInput.value = supplierType;
                updateResults(`✅ Fournisseur sélectionné: ${selectedOption.textContent}`);
            } else {
                typeInput.value = '';
            }
        }

        function addModalItem() {
            if (!purchaseModule) return;
            
            const container = document.getElementById('modalPurchaseItems');
            const itemIndex = container.children.length;
            
            const itemHtml = `
                <div class="purchase-item mb-3 p-3 border rounded">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">Matière première</label>
                            <select class="form-select" name="materialId">
                                <option value="">Sélectionner</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Quantité</label>
                            <input type="number" class="form-control" name="quantity" value="1">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Prix unitaire (DH)</label>
                            <input type="number" class="form-control" name="unitPrice" readonly>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Total (DH)</label>
                            <input type="number" class="form-control" name="total" readonly>
                        </div>
                    </div>
                </div>
            `;
            
            container.insertAdjacentHTML('beforeend', itemHtml);
            
            // Remplir le select avec filtrage
            const newItem = container.lastElementChild;
            const materialSelect = newItem.querySelector('[name="materialId"]');
            const supplierSelect = document.getElementById('modalSupplier');
            const selectedSupplier = supplierSelect.selectedOptions[0];
            
            if (selectedSupplier && selectedSupplier.value) {
                const supplierType = selectedSupplier.dataset.type || '';
                purchaseModule.populateMaterialSelectFiltered(materialSelect, supplierType);
                updateResults(`✅ Article ajouté avec filtrage pour type: ${supplierType}`);
            } else {
                purchaseModule.populateMaterialSelect(materialSelect);
                updateResults('✅ Article ajouté sans filtrage (aucun fournisseur sélectionné)');
            }
        }

        function testFiltering() {
            if (!purchaseModule) {
                updateResults('❌ Module non initialisé');
                return;
            }
            
            updateResults('🔍 Test du système de filtrage...');
            
            const typeMapping = {
                'Alimentation générale': ['Épicerie', 'Condiments'],
                'Légumes et fruits': ['Légumes', 'Fruits'],
                'Viandes': ['Viandes'],
                'Poissons': ['Poissons'],
                'Produits laitiers': ['Produits laitiers']
            };
            
            Object.keys(typeMapping).forEach(type => {
                const allowedCategories = typeMapping[type];
                const allowedCategoryIds = purchaseModule.categories
                    .filter(cat => allowedCategories.includes(cat.name))
                    .map(cat => cat.id);
                
                const filteredCount = purchaseModule.rawMaterials.filter(material => 
                    allowedCategoryIds.includes(material.categoryId)
                ).length;
                
                updateResults(`${type}: ${filteredCount} matières disponibles`);
            });
        }

        function showMappingInfo() {
            const mappingInfo = `
                <h6>Mapping Type → Catégories :</h6>
                <ul>
                    <li><strong>Alimentation générale</strong> → Épicerie, Condiments</li>
                    <li><strong>Légumes et fruits</strong> → Légumes, Fruits</li>
                    <li><strong>Viandes</strong> → Viandes</li>
                    <li><strong>Poissons</strong> → Poissons</li>
                    <li><strong>Produits laitiers</strong> → Produits laitiers</li>
                    <li><strong>Boulangerie</strong> → Boulangerie</li>
                    <li><strong>Boissons</strong> → Boissons</li>
                </ul>
            `;
            updateResults(mappingInfo);
        }

        function openPurchaseModal() {
            if (!purchaseModule) {
                updateResults('❌ Module non initialisé');
                return;
            }
            
            document.getElementById('modalDate').value = new Date().toISOString().split('T')[0];
            document.getElementById('modalPurchaseItems').innerHTML = '';
            
            new bootstrap.Modal(document.getElementById('testPurchaseModal')).show();
            updateResults('✅ Modal de test ouvert');
        }

        function updateResults(message) {
            const results = document.getElementById('results');
            results.innerHTML += `<p class="mb-1">${message}</p>`;
            results.scrollTop = results.scrollHeight;
        }
    </script>
</body>
</html>
